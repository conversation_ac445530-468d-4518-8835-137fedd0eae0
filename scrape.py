#!/usr/bin/env python3
"""
Convenience script for running news scraping tools.
This script provides easy access to all scraping functionality from the root directory.
"""

import sys
import subprocess


def show_help():
    """Show available commands."""
    print("🗞️  News Scraper - Convenience Script")
    print("=" * 50)
    print()
    print("📰 Scraping Commands:")
    print("  python scrape.py today          # Scrape today's news")
    print("  python scrape.py yesterday      # Scrape yesterday's news")
    print("  python scrape.py stats          # Show scraping statistics")
    print("  python scrape.py files          # Show recent data files")
    print("  python scrape.py health         # System health check")
    print()
    print("🤖 AI Rephrasing Commands:")
    print("  python scrape.py rephrase today [batch-size] # Rephrase today's articles (default batch: 5)")
    print("  python scrape.py rephrase date 2025-07-01 [batch-size]  # Rephrase specific date")
    print("  python scrape.py quota          # Check AI quota status")
    print("  python scrape.py test-ai        # Test AI connection")
    print()
    print("📅 Date-based Scraping:")
    print("  python scrape.py date 2025-07-01                    # Single date")
    print("  python scrape.py range 2025-06-28 2025-07-01        # Date range")
    print()
    print("🔧 Advanced Options:")
    print("  python scrape.py bulk --help                        # Full bulk scraping options")
    print("  python scrape.py date 2025-07-01 --dry-run          # Preview without scraping")
    print("  python scrape.py range 2025-06-28 2025-07-01 --delay 3.0  # Custom delays")
    print()
    print("📊 Examples:")
    print("  python scrape.py today                              # Quick daily scrape")
    print("  python scrape.py range 2025-06-01 2025-06-30        # Scrape June 2025")
    print("  python scrape.py stats                              # Check progress")
    print()
    print("💡 For advanced usage, use the CLI directly:")
    print("  python -m cli scrape --help")
    print("  python -m cli status --help")


def run_cli_command(cli_args: list):
    """Run a CLI command."""
    cmd = [sys.executable, '-m', 'cli'] + cli_args
    return subprocess.run(cmd).returncode


def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        show_help()
        return 0

    command = sys.argv[1].lower()
    args = sys.argv[2:]

    # Quick utility commands
    if command == 'today':
        return run_cli_command(['scrape', 'today'])

    elif command == 'yesterday':
        return run_cli_command(['scrape', 'yesterday'])

    elif command == 'stats':
        return run_cli_command(['status', 'stats', '--days', '7'])

    elif command == 'files':
        return run_cli_command(['status', 'files', '--limit', '10'])

    elif command == 'health':
        return run_cli_command(['status', 'health'])

    elif command == 'quota':
        return run_cli_command(['rephrase', 'quota'])

    elif command == 'test-ai':
        return run_cli_command(['rephrase', 'test'])

    # Rephrasing commands
    elif command == 'rephrase':
        if len(args) < 1:
            print("❌ Rephrase subcommand required. Usage: python scrape.py rephrase today|date YYYY-MM-DD [batch-size]")
            return 1

        subcommand = args[0]
        batch_size = args[2] if len(args) > 2 else '5'  # Default batch size

        if subcommand == 'today':
            from datetime import date
            today = date.today().strftime('%Y-%m-%d')
            return run_cli_command([
                'rephrase', 'process',
                '--source', 'macaodaily',
                '--date', today,
                '--batch-size', batch_size,
                '--priority-threshold', '0.6',  # Only high-priority articles
                '--max-articles', '50'
            ])
        elif subcommand == 'date' and len(args) > 1:
            return run_cli_command([
                'rephrase', 'process',
                '--source', 'macaodaily',
                '--date', args[1],
                '--batch-size', batch_size,
                '--priority-threshold', '0.6',
                '--max-articles', '50'
            ])
        else:
            print("❌ Invalid rephrase command. Use: today or date YYYY-MM-DD [batch-size]")
            return 1

    # Date-based commands
    elif command == 'date':
        if len(args) < 1:
            print("❌ Date required. Usage: python scrape.py date 2025-07-01")
            return 1

        # Handle dry-run flag
        cli_args = []
        scrape_args = ['scrape', 'macaodaily', '--date', args[0]]

        # Check for dry-run in remaining args
        remaining_args = args[1:]
        if '--dry-run' in remaining_args:
            cli_args.append('--dry-run')
            remaining_args.remove('--dry-run')

        return run_cli_command(cli_args + scrape_args + remaining_args)

    elif command == 'range':
        if len(args) < 2:
            print("❌ Start and end dates required. Usage: python scrape.py range 2025-06-28 2025-07-01")
            return 1

        # Handle dry-run flag
        cli_args = []
        scrape_args = ['scrape', 'bulk', '--start-date', args[0], '--end-date', args[1], '--source', 'macaodaily']

        # Check for dry-run in remaining args
        remaining_args = args[2:] if len(args) > 2 else []
        if '--dry-run' in remaining_args:
            cli_args.append('--dry-run')
            remaining_args.remove('--dry-run')

        return run_cli_command(cli_args + scrape_args + remaining_args)

    # Direct bulk script access
    elif command == 'bulk':
        return run_cli_command(['scrape', 'bulk'] + args)

    # Help
    elif command in ['help', '--help', '-h']:
        show_help()
        return 0

    else:
        print(f"❌ Unknown command: {command}")
        print("💡 Use 'python scrape.py help' to see available commands")
        return 1


if __name__ == "__main__":
    sys.exit(main())
