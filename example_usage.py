#!/usr/bin/env python3
"""
Example usage script demonstrating how to use the news scraping system.
This shows how to use individual components and the complete system.
"""

import json
from pathlib import Path

# Import our modules
from src.core.models.article import ScrapedArticle
from src.scraping.scrapers.macaodaily_scraper import Macao<PERSON><PERSON>yScraper
from src.scraping.utils.storage import DataStorage


def example_create_article():
    """Example: Create and manipulate an Article object."""
    print("📰 Example: Creating an Article object")
    print("-" * 40)
    
    # Create a sample article
    article = ScrapedArticle(
        source_site_name="示例新聞網",
        source_site_url="https://example-news.com",
        original_url="https://example-news.com/article/123",
        original_title="重要新聞：科技發展新突破",
        content_html="<div><p>這是一篇關於科技發展的重要新聞。</p><p>內容包含多個段落。</p></div>",
        publish_date="2025-06-28T10:30:00+08:00",
        author="記者張三"
    )
    
    print(f"Title: {article.original_title}")
    print(f"Author: {article.author}")
    print(f"URL: {article.original_url}")
    print(f"Content (text): {article.content_text[:50]}...")
    print(f"Content (markdown): {article.content_markdown[:50]}...")
    
    # Convert to JSON
    json_str = article.to_json()
    print(f"\nJSON length: {len(json_str)} characters")
    
    print("✅ Article creation example completed\n")


def example_single_scraper():
    """Example: Run a single scraper manually."""
    print("🕷️ Example: Running a single scraper")
    print("-" * 40)
    
    # Create a scraper instance
    scraper = MacaoDailyScraper()
    
    print(f"Scraper: {scraper.source_site_name}")
    print(f"Target URL: http://www.macaodaily.com/html/2025-05/28/node_1.htm")
    
    # Note: This is just an example - actual scraping would require network access
    print("Note: This example shows the structure. Actual scraping requires network access.")
    
    # Show what the scraper would do:
    print(f"Source site: {scraper.source_site_name}")
    print(f"Base URL: {scraper.source_site_url}")
    print(f"Scraper type: {type(scraper).__name__}")
    
    print("✅ Single scraper example completed\n")


def example_storage_operations():
    """Example: Storage operations."""
    print("💾 Example: Storage operations")
    print("-" * 40)
    
    # Create storage instance
    storage = DataStorage()
    
    # Create some sample articles
    articles = [
        ScrapedArticle(
            source_site_name="測試網站A",
            source_site_url="https://test-a.com",
            original_url="https://test-a.com/article1",
            original_title="測試文章1",
            content_html="<p>測試內容1</p>",
            publish_date="2025-06-28T10:00:00+08:00"
        ),
        ScrapedArticle(
            source_site_name="測試網站A",
            source_site_url="https://test-a.com",
            original_url="https://test-a.com/article2",
            original_title="測試文章2",
            content_html="<p>測試內容2</p>",
            publish_date="2025-06-28T11:00:00+08:00"
        )
    ]
    
    # Save articles
    filepath = storage.save_articles(articles, "test_scraper")
    print(f"Saved {len(articles)} articles to: {filepath}")
    
    # Load articles back
    loaded_articles = storage.load_articles(filepath)
    print(f"Loaded {len(loaded_articles)} articles from file")
    
    # Get existing URLs
    existing_urls = storage.get_existing_urls("test_scraper")
    print(f"Found {len(existing_urls)} existing URLs")
    
    # List all files
    all_files = storage.list_all_files()
    print(f"Total data files: {len(all_files)}")
    
    print("✅ Storage operations example completed\n")


def example_configuration():
    """Example: Working with configuration."""
    print("⚙️ Example: Configuration")
    print("-" * 40)
    
    import config
    
    print("Configured scrapers:")
    for i, scraper_config in enumerate(config.SCRAPERS, 1):
        status = "✅ Enabled" if scraper_config.get('enabled', True) else "❌ Disabled"
        print(f"  {i}. {scraper_config['name']} - {status}")
        print(f"     URL: {scraper_config['url']}")
        print(f"     Class: {scraper_config['class_name']}")
    
    print(f"\nOutput directory: {config.OUTPUT_DIR}")
    print(f"Request timeout: {config.REQUEST_TIMEOUT}s")
    print(f"Playwright headless: {config.PLAYWRIGHT_HEADLESS}")
    
    print("✅ Configuration example completed\n")


def example_data_analysis():
    """Example: Analyze scraped data."""
    print("📊 Example: Data analysis")
    print("-" * 40)
    
    storage = DataStorage()
    data_dir = Path(storage.output_dir)
    
    if not data_dir.exists():
        print("No data directory found. Run the scraper first.")
        return
    
    json_files = list(data_dir.glob("*.json"))
    
    if not json_files:
        print("No data files found. Run the scraper first.")
        return
    
    print(f"Found {len(json_files)} data files:")
    
    total_articles = 0
    scrapers_summary = {}
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            scraper_name = data.get('scraper_name', 'unknown')
            article_count = data.get('article_count', 0)
            scrape_time = data.get('scrape_timestamp', 'unknown')
            
            print(f"  📄 {json_file.name}")
            print(f"     Scraper: {scraper_name}")
            print(f"     Articles: {article_count}")
            print(f"     Time: {scrape_time[:19] if scrape_time != 'unknown' else 'unknown'}")
            
            total_articles += article_count
            
            if scraper_name not in scrapers_summary:
                scrapers_summary[scraper_name] = 0
            scrapers_summary[scraper_name] += article_count
            
        except Exception as e:
            print(f"  ❌ Error reading {json_file.name}: {e}")
    
    print(f"\n📈 Summary:")
    print(f"  Total articles: {total_articles}")
    print(f"  Articles by scraper:")
    for scraper, count in scrapers_summary.items():
        print(f"    {scraper}: {count}")
    
    print("✅ Data analysis example completed\n")


def example_rephrasing():
    """Example: AI rephrasing functionality."""
    print("🤖 Example: AI Rephrasing")
    print("-" * 40)

    try:
        # Test article filtering
        from src.rephrasing.processors.article_filter import ArticleFilter, create_daily_filter
        from src.core.models.article import ScrapedArticle
        from datetime import datetime

        # Create sample articles
        sample_articles = [
            ScrapedArticle(
                title="澳門經濟發展新動向",
                content="澳門特別行政區政府今日宣布一系列促進經濟發展的新措施，包括支持中小企業發展、推動科技創新等。這些措施預計將為澳門經濟注入新活力，促進可持續發展。" * 3,
                source_url="http://example.com/1",
                source_site_name="澳門日報",
                tags=["A01", "澳聞", "經濟"],
                publish_date=datetime.now()
            ),
            ScrapedArticle(
                title="廣告：特價商品促銷",
                content="本店特價商品大促銷，歡迎選購。",
                source_url="http://example.com/2",
                source_site_name="澳門日報",
                tags=["A13", "廣告"],
                publish_date=datetime.now()
            )
        ]

        # Test filtering
        filter_obj = create_daily_filter(250)
        filtered = filter_obj.filter_articles(sample_articles)

        print(f"📊 Article filtering:")
        print(f"  Original articles: {len(sample_articles)}")
        print(f"  Filtered articles: {len(filtered)}")

        for article in filtered:
            priority = filter_obj.calculate_priority_score(article)
            print(f"    - {article.title} (Priority: {priority:.2f})")

        # Test rate limiter
        from src.rephrasing.utils.rate_limiter import GeminiRateLimiter

        rate_limiter = GeminiRateLimiter()
        status = rate_limiter.get_quota_status()

        print(f"\n📊 Rate limiter status:")
        print(f"  Daily requests: {status['daily_requests']}/{status['daily_limit']}")
        print(f"  Can make request: {status['can_make_request']}")

        # Test prompt building
        from src.rephrasing.templates.prompts import PromptBuilder

        builder = PromptBuilder()
        prompt = builder.build_rephrase_prompt(sample_articles[0])

        print(f"\n📝 Prompt generation:")
        print(f"  Generated prompt length: {len(prompt)} characters")
        print(f"  Estimated tokens: {len(prompt) // 4}")

        print("\n💡 To use AI rephrasing:")
        print("  1. Set GEMINI_API_KEY environment variable")
        print("  2. Run: python scrape.py rephrase today")
        print("  3. Or: python -m cli rephrase process --source macaodaily --date 2025-07-01")

        print("✅ Rephrasing components working correctly\n")

    except ImportError as e:
        print(f"⚠️  Rephrasing components not available: {e}\n")
    except Exception as e:
        print(f"❌ Error testing rephrasing: {e}\n")


def main():
    """Run all examples."""
    print("🚀 News Scraping System - Usage Examples")
    print("=" * 50)
    print()
    
    examples = [
        example_create_article,
        example_single_scraper,
        example_storage_operations,
        example_configuration,
        example_data_analysis,
        example_rephrasing
    ]
    
    for example in examples:
        try:
            example()
        except Exception as e:
            print(f"❌ Error in {example.__name__}: {e}\n")
    
    print("🎉 All examples completed!")
    print("\nNext steps:")
    print("1. Run the tests: python run_tests.py")
    print("2. Run the main scraper: python main.py")
    print("3. Check results: python main.py list")


if __name__ == "__main__":
    main()
