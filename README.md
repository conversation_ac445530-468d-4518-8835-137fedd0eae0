# 新聞內容聚合平台 (News Content Aggregation Platform)

A comprehensive Python project for scraping news articles and intelligently rephrasing them using AI. Features robust scraping, smart content filtering, and production-ready AI processing with quota management.

## 🎯 Project Overview

This project implements a professional news processing pipeline with two core features:

### 📰 **News Scraping**
1. **澳門日報 (Macao Daily)** - ✅ **Fully Functional** - Static website scraping with comprehensive content extraction
2. **市民日報 (Shimin Daily)** - ⚠️ **In Development** - Dynamic website with advanced Cloudflare protection

### 🤖 **AI-Powered Content Rephrasing**
- **Google Gemini 2.5 Flash Integration** - ✅ **Production Ready**
- **Smart Article Filtering** - Automatically selects high-value content
- **Quota Management** - Respects API limits with graceful handling
- **Batch Processing** - Efficient processing with progress tracking
- **Error Recovery** - Robust retry logic and resumable processing

### 🚀 **API Integration**
- **REST API Upload** - ✅ **Production Ready** - Upload rephrased articles to your server
- **Image Handling** - Support for external URLs and base64 encoding (uses article slug as filename)
- **Upload Tracking** - Comprehensive status tracking with resumable uploads
- **Rate Limiting** - Configurable rate limits and retry logic
- **Authentication** - Support for Bearer tokens, API keys, and basic auth

## 🏗️ Architecture

The project follows a modular design with clear separation of concerns:

```
scrape-rephrase-news/
├── 📄 README.md              # This documentation
├── 🚀 scrape.py              # Convenience script for easy access
├── 📖 example_usage.py       # Usage examples and demos
├── 📋 requirements.txt       # Python dependencies
├── 🧪 run_tests.py           # Test runner script
├── 🙈 .gitignore             # Git ignore rules
├── 📂 src/                   # Main source code
│   ├── core/                 # Shared functionality
│   │   ├── models/           # Data models (Article, etc.)
│   │   └── exceptions.py     # Custom exceptions
│   ├── scraping/             # Scraping domain
│   │   ├── scrapers/         # Scraper implementations
│   │   │   ├── base.py       # StaticScraper & DynamicScraper base classes
│   │   │   └── macaodaily_scraper.py  # 澳門日報 scraper (static)
│   │   └── utils/            # Scraping utilities (storage, etc.)
│   └── rephrasing/           # AI rephrasing domain (future)
├── 📂 cli/                   # Command-line interface
│   ├── __main__.py           # CLI entry point
│   ├── scrape.py             # Scraping commands
│   └── status.py             # Status and monitoring commands
├── 📂 config/                # Configuration management
│   ├── app.yaml              # Application settings
│   ├── scraping.yaml         # Scraper configurations
│   └── settings.py           # Settings loader
├── 📂 data/                  # Organized data storage
│   ├── raw/macaodaily/2025/  # Raw scraped data by year
│   ├── processed/            # AI-processed content
│   └── exports/              # Final exports
└── 📂 docs/                  # Documentation
    ├── requirements.md       # Project requirements specification
    └── todo.md              # Future improvements
```

## 🚀 Quick Start Guide

### 📦 **1. Installation**

```bash
# Clone the repository
git clone <repository-url>
cd scrape-rephrase-news

# Install Python dependencies
pip install -r requirements.txt

# Install Playwright browsers (for dynamic scraping)
playwright install chromium
```

### ✅ **2. Verify Setup**

```bash
# Test system health
python -m cli status health

# Run comprehensive tests
python run_tests.py
```

### 📰 **3. Scrape News Articles**

```bash
# Scrape today's news
python -m cli scrape today

# Scrape specific date
python -m cli scrape macaodaily --date 2025-07-01

# Scrape date range
python -m cli scrape bulk --start-date 2025-06-28 --end-date 2025-07-01 --source macaodaily
```

### 🤖 **4. AI Rephrasing (Optional)**

```bash
# Get Gemini API key from https://aistudio.google.com/app/apikey
export GEMINI_API_KEY=your_api_key_here

# Test AI connection
python -m cli rephrase test

# Process articles with AI
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01
```

### 📊 **5. Monitor Progress**

```bash
# Check system status
python -m cli status stats

# Check AI quota
python -m cli rephrase quota

# View recent files
python -m cli status files --limit 10
```

### 🚀 **6. Upload to API (Optional)**

```bash
# Configure API settings
export NEWS_API_URL=https://your-server.com/api/posts
export NEWS_API_AUTH_TOKEN=your_bearer_token_here

# Test API connection
python -m cli upload test

# Upload rephrased articles
python -m cli upload single --date 2025-07-01
python -m cli upload range --start-date 2025-07-01 --end-date 2025-07-31
```

### 📁 **7. Check Results**

Scraped articles are saved in organized directories:
- **Raw Data**: `data/raw/macaodaily/2025/20250701.json`
- **Rephrased Content**: `data/processed/rephrased/macaodaily/2025/20250701.json`
- **Upload Status**: `data/processed/upload_status.json`
- **Pre-rephrase Filters**: `data/processed/pre-rephrase/macaodaily/2025/20250701.json`

## 📊 Data Schema

All scraped articles follow a unified schema:

```json
{
  "scraper_name": "macaodaily",
  "target_date": "2025-07-01",
  "scraped_at": "2025-07-01T14:30:22",
  "article_count": 188,
  "articles": [
    {
      "id": "unique-article-id",
      "title": "澳門舉行盛大慶典",
      "content": "這是第一段。...",
      "content_html": "<div><p>這是第一段。</p>...</div>",
      "content_markdown": "這是第一段。\n\n...",
      "content_text": "這是第一段。 ...",
      "source_url": "http://www.macaodaily.com/html/2025-07/01/content_123456.htm",
      "source_site_name": "澳門日報",
      "author": "記者陳大文",
      "publish_date": "2025-07-01T10:00:00",
      "tags": ["A01", "澳聞"],
      "images": []
    }
  ]
}
```

## 🤖 AI Rephrasing - Production Ready System

The system includes a comprehensive AI-powered article rephrasing pipeline using Google Gemini 2.5 Flash with enterprise-grade reliability.

### ✨ Key Features

#### 🎯 **Smart Processing**
- **Intelligent Article Filtering**: Priority-based selection (0.6+ threshold) to maximize quota efficiency
- **Batch Processing**: Optimized batch size (8 articles) for best performance
- **Pre-processing Optimization**: Filters articles before API calls to save tokens
- **Content Quality Assessment**: Automatic filtering of low-value content

#### 🛡️ **Robust Error Handling**
- **Quota Exhaustion Detection**: Immediate graceful stopping when daily limit reached
- **Network Resilience**: Smart retry logic for timeout/connection issues (2 retries with exponential backoff)
- **Progress Preservation**: Incremental saving prevents data loss during interruptions
- **Resumable Processing**: Automatically continues from last successful batch

#### 📊 **Advanced Quota Management**
- **Real-time Tracking**: Live quota monitoring with detailed status reporting
- **Dynamic Rate Limiting**: Extracts retry delays from API responses (no fixed delays)
- **Quota Preservation**: No automatic retries on quota exhaustion to prevent waste
- **Daily Reset Handling**: Automatic detection and handling of quota resets

#### 🔄 **Continuous Processing**
- **Month-Based Processing**: Easy month specification with multiple formats (2025-04, apr-2025, 04, feb)
- **Smart Date Adjustment**: Automatically corrects invalid dates (2025-04-31 → 2025-04-30)
- **Multiple Month Support**: Process several months in one command (2025-04,2025-05,2025-06)
- **Date Range Processing**: Traditional start/end date processing with intelligent stopping
- **Incomplete Detection**: Automatically detects and resumes incomplete processing
- **Priority Ordering**: Processes newest dates first (latest news prioritized)
- **Comprehensive Logging**: Detailed progress tracking and error reporting

### 🚀 Quick Setup

1. **Get API Key**: Obtain a Gemini API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
2. **Set Environment**: `export GEMINI_API_KEY=your_api_key_here`
3. **Start Processing**: `python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01`

### 📊 Rate Limits & Quota (Free Tier)

- **Daily Quota**: 250 requests per day (automatically tracked)
- **Rate Limit**: 10 requests per minute (dynamically managed)
- **Token Limit**: 250,000 tokens per minute (monitored)
- **Smart Handling**: System respects all limits with graceful degradation

### 💡 Usage Examples

#### Basic Processing
```bash
# Process single date
python -m cli rephrase process --source macaodaily --date 2025-07-01

# Process date range (recommended)
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01

# Process with custom batch size
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --batch-size 8
```

#### Quota Management
```bash
# Check current quota status
python -m cli rephrase quota

# Test API connection
python -m cli rephrase test

# Monitor processing with detailed logging
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --log-level DEBUG
```

#### Advanced Options
```bash
# High-priority articles only
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --priority-threshold 0.8

# Skip existing files
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --skip-existing

# Create pre-rephrase files for inspection
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --create-pre-rephrase
```

### 🎯 What Happens During Processing

#### ✅ **Successful Processing**
```
📅 Processing 2025-06-30
📖 Loaded 168 articles from 20250630.json
🔍 Filtered to 90 high-priority articles (threshold: 0.6)
🤖 Starting AI rephrasing with gemini-2.5-flash (batch size: 8)
📦 Processing 90 articles in 12 batches
🔄 Processing batch 1/12 (8 articles)
✅ Batch 1 completed: 8 articles processed
📊 Progress: 8/90 (8.9%)
✅ Job finalized: completed - 90 successful, 0 failed
```

#### 🚫 **Quota Exhaustion (Graceful Handling)**
```
🚫 Daily quota exhausted during batch 3
📊 API suggests waiting 28 seconds (quota reset)
💡 Processing stopped to preserve quota. Resume tomorrow or upgrade plan.
✅ Job finalized: quota_exhausted - 16 successful, 0 failed
🚫 Stopping continuous processing due to quota exhaustion
💡 Resume processing tomorrow or upgrade your Gemini API plan
📊 Processed 0/4 files before quota exhaustion
```

#### 🔄 **Resume Processing (Next Day)**
```
📊 Incomplete processing found for 2025-06-30: 16/90 articles processed (17.8%)
📊 Resume processing: 74 unprocessed articles out of 90 total
🤖 Starting AI rephrasing with gemini-2.5-flash (batch size: 8)
📦 Processing 74 articles in 10 batches
```

## ⚙️ Configuration

Configuration is managed through YAML files in the `config/` directory:

### Application Settings (`config/app.yaml`)

```yaml
data:
  raw_data_path: "data/raw"
  processed_data_path: "data/processed"
  export_data_path: "data/exports"

scraping:
  rate_limit: 2.0
  max_retries: 3
  timeout: 30

rephrasing:
  model: "gemini-2.0-flash"
  max_tokens: 4000
  temperature: 0.3

api:
  api_url: ""  # Set via NEWS_API_URL environment variable
  auth_type: "bearer"
  timeout: 60
  batch_size: 10
  include_images: true
  image_format: "base64"
```

### Scraper Settings (`config/scraping.yaml`)

```yaml
scrapers:
  macaodaily:
    enabled: true
    base_url: "http://www.macaodaily.com"
    description: "澳門日報"
  shimindaily:
    enabled: false
    base_url: "http://www.shimindaily.net"
    description: "市民日報"
```

## 🔧 Adding New Scrapers

### For Static Websites

1. Create a new scraper class inheriting from `StaticScraper`:

```python
from scrapers.base import StaticScraper

class NewSiteScraper(StaticScraper):
    def __init__(self):
        super().__init__(
            source_site_name="新網站",
            source_site_url="https://example.com/"
        )
  
    def parse(self, soup):
        # Implement parsing logic
        articles = []
        # ... extract articles from BeautifulSoup object
        return articles
```

2. Add to `config.py` SCRAPERS list

### For Dynamic Websites

1. Create a new scraper class inheriting from `DynamicScraper`:

```python
from scrapers.base import DynamicScraper

class NewDynamicScraper(DynamicScraper):
    def __init__(self):
        super().__init__(
            source_site_name="動態網站",
            source_site_url="https://dynamic-example.com/"
        )
  
    def parse(self, soup):
        # Implement parsing logic for JavaScript-rendered content
        articles = []
        # ... extract articles
        return articles
```

## 🛠️ Features

- **Modular Design**: Easy to add new scrapers with base classes
- **Duplicate Prevention**: Automatically skips already scraped URLs
- **Error Handling**: Robust error handling with comprehensive logging
- **Multiple Formats**: Content saved as HTML, Markdown, and plain text
- **Cloudflare Support**: Handles basic Cloudflare "Are you human?" challenges
- **Chinese Text Support**: Proper encoding handling for Chinese content
- **Configurable**: Easy configuration management via `config.py`
- **Test Suite**: Built-in setup verification and examples
- **JSON Storage**: Simple file-based storage with future database migration path

## 📝 Logging

Logs are written to both console and `scraper.log` file:

- INFO: General operation status
- ERROR: Scraping failures and errors
- DEBUG: Detailed debugging information

## 🔄 Future Improvements

See `docs/todo.md` for planned enhancements:

- Code quality tools (ruff, uv)
- Database integration
- Web dashboard
- Advanced anti-bot detection
- Async/parallel processing

## 📁 Project Structure

The project follows a domain-driven design with clear separation between scraping and rephrasing functionality:

```
scrape-rephrase-news/
├── src/                         # Main source code
│   ├── core/                    # Shared functionality
│   │   ├── models/              # Data models
│   │   └── utils/               # Shared utilities
│   ├── scraping/                # Scraping domain
│   │   ├── scrapers/            # Scraper implementations
│   │   └── utils/               # Scraping utilities
│   └── rephrasing/              # AI rephrasing domain (future)
├── cli/                         # Command-line interface
├── config/                      # Configuration management
├── data/                        # Organized data storage
│   ├── raw/macaodaily/2025/     # Raw scraped data by year
│   ├── processed/               # AI-processed content
│   └── exports/                 # Final exports
└── docs/                        # Documentation
```

## 📋 Complete CLI Reference

### 🔧 System Status & Monitoring

```bash
# Check system health and configuration
python -m cli status health              # Overall system health check
python -m cli status config              # Show current configuration
python -m cli status stats               # Comprehensive statistics
python -m cli status stats --days 30     # Statistics for last 30 days
python -m cli status files --limit 10    # Show recent files

# AI quota monitoring
python -m cli rephrase quota             # Current quota status
python -m cli rephrase test              # Test API connection
```

### 📰 News Scraping Commands

```bash
# Single date scraping
python -m cli scrape macaodaily --date 2025-07-01
python -m cli scrape today               # Scrape today's news
python -m cli scrape yesterday           # Scrape yesterday's news

# Bulk scraping
python -m cli scrape bulk --start-date 2025-06-28 --end-date 2025-07-01 --source macaodaily

# Dry run mode (preview without scraping)
python -m cli --dry-run scrape macaodaily --date 2025-07-01
```

### 🤖 AI Rephrasing Commands

#### **Recommended: Continuous Processing**
```bash
# Process by month (easiest way!)
python -m cli rephrase continuous --month 2025-04
python -m cli rephrase continuous --month apr-2025
python -m cli rephrase continuous --month 04          # Current year (2025)
python -m cli rephrase continuous --month feb          # Current year

# Process multiple months
python -m cli rephrase continuous --month "2025-04,2025-05,2025-06"
python -m cli rephrase continuous --month "apr-2025,may-2025,jun-2025"

# Process date range (traditional way)
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01

# With custom settings
python -m cli rephrase continuous --month 2025-04 --batch-size 8 --priority-threshold 0.6

# Skip existing files
python -m cli rephrase continuous --month 2025-04 --skip-existing

# Create pre-rephrase files for inspection
python -m cli rephrase continuous --month 2025-04 --create-pre-rephrase

# Smart date adjustment (automatically fixes invalid dates)
python -m cli rephrase continuous --start-date 2025-04-01 --end-date 2025-04-31  # Auto-corrects to 2025-04-30
python -m cli rephrase continuous --start-date 2025-02-01 --end-date 2025-02-31  # Auto-corrects to 2025-02-28
```

#### **Single Date Processing**
```bash
# Process specific date
python -m cli rephrase process --source macaodaily --date 2025-07-01

# With custom priority threshold
python -m cli rephrase process --source macaodaily --date 2025-07-01 --priority-threshold 0.8
```

#### **Debugging & Development**
```bash
# Verbose logging
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --log-level DEBUG

# Test single batch
python -m cli rephrase process --source macaodaily --date 2025-07-01 --batch-size 3
```

### 🚀 API Upload Commands

```bash
# Test API connection and configuration
python -m cli upload test

# Upload single date
python -m cli upload single --date 2025-07-01
python -m cli upload single --date 2025-07-01 --limit 5  # Upload only 5 articles
python -m cli upload single --file data/processed/rephrased/macaodaily/2025/20250701.json

# Upload date range
python -m cli upload range --start-date 2025-07-01 --end-date 2025-07-31

# Check upload status
python -m cli upload status

# Reset failed uploads
python -m cli upload reset-failed

# Dry run mode (preview without uploading)
python -m cli upload single --date 2025-07-01 --limit 3 --dry-run
python -m cli upload range --start-date 2025-07-01 --end-date 2025-07-31 --dry-run
```

### 🎛️ Global Options

```bash
# Available for all commands
--dry-run                    # Preview operations without execution
--log-level DEBUG            # Set logging level (DEBUG, INFO, WARNING, ERROR)
--config /path/to/config     # Use custom configuration file
```

## 🧪 Testing & Development

### Available Scripts

- **`scrape.py`**: Convenience script for easy access to scraping functionality
- **`example_usage.py`**: Demonstrates how to use individual components
- **`run_tests.py`**: Comprehensive test runner for the entire system
- **`run_tests.py`**: Test runner for the comprehensive test suite

### Running Tests

```bash
# Run comprehensive tests
python run_tests.py

# See usage examples
python example_usage.py

# Run comprehensive tests
python run_tests.py quick          # Quick smoke test
python run_tests.py unit           # Unit tests
python run_tests.py integration    # Integration tests
python run_tests.py coverage       # Full test suite with coverage

# Run actual scraping
python scrape.py today
# or
python -m cli scrape macaodaily --date 2025-07-01
```

## 🐛 Troubleshooting & FAQ

### 🚨 Common Issues

#### **AI Rephrasing Issues**

1. **"Daily quota exhausted"**
   - **Solution**: Wait until tomorrow (quota resets daily) or upgrade to paid plan
   - **Prevention**: Use `--priority-threshold 0.8` to process only highest-value articles
   - **Status**: Check remaining quota with `python -m cli rephrase quota`

2. **"Network/timeout issue"**
   - **Automatic**: System retries 2 times with exponential backoff
   - **Manual**: Check internet connection and try again
   - **Logs**: Use `--log-level DEBUG` for detailed error information

3. **"API key not found"**
   - **Solution**: Set environment variable: `export GEMINI_API_KEY=your_key_here`
   - **Verify**: Test with `python -m cli rephrase test`

#### **Scraping Issues**

4. **Playwright Installation**: If dynamic scraping fails:
   ```bash
   playwright install chromium
   ```

5. **Import Errors**: If you see "No module named" errors:
   ```bash
   pip install -r requirements.txt
   ```

6. **Permission Errors**: Ensure write permissions for `data/` directory:
   ```bash
   chmod -R 755 data/
   ```

### 🔍 Understanding System Behavior

#### **Quota Management**
- **Daily Limit**: 250 requests per day (Gemini free tier)
- **Smart Stopping**: System stops immediately when quota exhausted
- **Resume Capability**: Automatically continues from last successful batch
- **Progress Tracking**: All progress saved incrementally

#### **Article Filtering**
- **Priority Threshold**: Default 0.6 (60% priority score)
- **High-Priority Tags**: A01-A08 (澳聞, 要聞, 政治, 經濟, 科技, 社會, 健康, 教育)
- **Content Quality**: Minimum length, proper formatting required
- **Token Optimization**: Pre-processing filters to maximize quota efficiency

#### **Error Recovery**
- **Network Issues**: 2 automatic retries with exponential backoff
- **Quota Exhaustion**: Immediate graceful stop with progress preservation
- **Incomplete Processing**: Automatic detection and resumption
- **Data Loss Prevention**: Incremental saving after each successful batch

### 📊 Current System Status

- **澳門日報 Scraping**: ✅ Fully functional with comprehensive content extraction
- **AI Rephrasing**: ✅ Production-ready with enterprise-grade reliability
- **Quota Management**: ✅ Intelligent handling with graceful degradation
- **Error Recovery**: ✅ Robust retry logic and resumable processing
- **市民日報**: ⚠️ In development (Cloudflare protection challenges)

### 🆘 Getting Help

#### **Check System Status**
```bash
python -m cli status health              # Overall system health
python -m cli rephrase quota             # AI quota status
python -m cli status stats               # Processing statistics
```

#### **Debug Mode**
```bash
# Enable detailed logging for troubleshooting
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --log-level DEBUG
```

#### **Test Components**
```bash
python -m cli rephrase test              # Test AI connection
python -m cli scrape today --dry-run    # Test scraping without execution
```

## 📄 License

This project is for educational and research purposes. Please respect the terms of service of the websites being scraped.

## 🤝 Contributing

1. Follow the existing code structure
2. Add appropriate error handling
3. Update documentation
4. Test with both static and dynamic websites

## 🎯 Production Readiness

This system is **production-ready** with enterprise-grade features:

### ✅ **Reliability**
- **Comprehensive Error Handling**: Graceful handling of all error conditions
- **Progress Preservation**: No data loss during interruptions
- **Automatic Recovery**: Resumable processing with intelligent retry logic
- **Quota Management**: Efficient API usage with respect for rate limits

### ✅ **Scalability**
- **Batch Processing**: Optimized for high-volume processing
- **Modular Architecture**: Easy to extend with new sources and AI providers
- **Configuration Management**: Environment-specific settings
- **Monitoring**: Comprehensive logging and status reporting

### ✅ **User Experience**
- **Clear Feedback**: Detailed progress reporting and error messages
- **Flexible Options**: Customizable processing parameters
- **Easy Setup**: Simple configuration and deployment
- **Comprehensive Documentation**: Complete usage guides and troubleshooting

### 🚀 **Next Steps for Advanced Users**

For additional enhancements, see the detailed implementation plan:
- **`docs/news_platform/ai_rephrase_implementation_plan.md`** - Complete technical documentation
- **Multiple AI Providers** - Support for OpenAI, Claude, and other providers
- **Advanced Analytics** - Processing metrics and quality scoring
- **API Integration** - Direct publishing to content management systems

---

**Status**: Production-ready system with comprehensive AI processing capabilities and enterprise-grade reliability.
