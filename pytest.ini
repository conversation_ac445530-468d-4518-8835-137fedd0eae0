[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --cov=src
    --cov=cli
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests that require network access
    scraper: Tests for scraper functionality
    cli: Tests for CLI commands
    config: Tests for configuration
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
