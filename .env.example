# Environment Configuration Template
# Copy this file to .env and fill in your values

# AI Services
# Get your Gemini API key from: https://aistudio.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_key_here

# Scraping
SCRAPING_RATE_LIMIT=2.0
SCRAPING_MAX_RETRIES=3

# Rephrasing (Gemini 2.5 Flash Free Tier Limits)
REPHRASING_RATE_LIMIT=6.5
REPHRASING_BATCH_SIZE=10
GEMINI_MAX_REQUESTS_PER_DAY=250
GEMINI_MAX_REQUESTS_PER_MINUTE=10

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Paths
DATA_ROOT=./data
LOG_ROOT=./logs

# Database (future use)
DATABASE_URL=sqlite:///data/articles.db

# News Platform API
NEWS_API_URL=https://your-server.com/api/posts
NEWS_API_AUTH_TOKEN=your_bearer_token_here
