#!/usr/bin/env python3
"""
Script to check and fix meta data format consistency across all rephrased files.
"""

import sys
import json
from pathlib import Path
from typing import Dict, List, Tuple

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

def analyze_meta_formats():
    """Analyze meta data formats across all rephrased files."""
    print("🔍 Analyzing meta data formats across all rephrased files...")
    
    data_path = Path("data/processed/rephrased/macaodaily")
    
    if not data_path.exists():
        print(f"❌ Data path not found: {data_path}")
        return
    
    # Find all JSON files
    json_files = []
    for year_dir in data_path.glob("*"):
        if year_dir.is_dir():
            json_files.extend(year_dir.glob("*.json"))
    
    if not json_files:
        print("❌ No rephrased files found")
        return
    
    print(f"📁 Found {len(json_files)} rephrased files")
    
    # Track different formats
    new_format_files = []  # Files with meta object
    old_format_files = []  # Files with meta_title/meta_description
    mixed_format_files = []  # Files with both formats
    no_meta_files = []  # Files with no meta data
    
    total_articles = 0
    
    for file_path in sorted(json_files):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            articles = data.get('articles', [])
            if not articles:
                continue
            
            total_articles += len(articles)
            
            # Check all articles to determine file format
            new_format_count = 0
            old_format_count = 0
            no_meta_count = 0

            for article in articles:
                has_meta_object = 'meta' in article and article['meta']
                has_meta_fields = ('meta_title' in article and article['meta_title']) or \
                                 ('meta_description' in article and article['meta_description'])

                if has_meta_object:
                    new_format_count += 1
                elif has_meta_fields:
                    old_format_count += 1
                else:
                    no_meta_count += 1

            # Classify file based on article formats
            if new_format_count > 0 and old_format_count > 0:
                mixed_format_files.append(file_path)
            elif new_format_count > 0:
                new_format_files.append(file_path)
            elif old_format_count > 0:
                old_format_files.append(file_path)
            else:
                no_meta_files.append(file_path)
                
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
    
    # Print summary
    print(f"\n📊 Meta Data Format Analysis:")
    print(f"   📁 Total files: {len(json_files)}")
    print(f"   📰 Total articles: {total_articles}")
    print(f"   ✅ New format (meta object): {len(new_format_files)} files")
    print(f"   🔄 Old format (meta_title/meta_description): {len(old_format_files)} files")
    print(f"   ⚠️  Mixed format (both): {len(mixed_format_files)} files")
    print(f"   ❌ No meta data: {len(no_meta_files)} files")
    
    # Show examples
    if old_format_files:
        print(f"\n🔄 Old format files (first 5):")
        for file_path in old_format_files[:5]:
            print(f"   📄 {file_path.name}")
    
    if mixed_format_files:
        print(f"\n⚠️  Mixed format files:")
        for file_path in mixed_format_files:
            print(f"   📄 {file_path.name}")
    
    if no_meta_files:
        print(f"\n❌ Files without meta data:")
        for file_path in no_meta_files:
            print(f"   📄 {file_path.name}")
    
    return {
        'new_format': new_format_files,
        'old_format': old_format_files,
        'mixed_format': mixed_format_files,
        'no_meta': no_meta_files
    }

def convert_file_to_new_format(file_path: Path, dry_run: bool = True) -> bool:
    """Convert a file from old format to new format."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles = data.get('articles', [])
        if not articles:
            return True
        
        converted_count = 0
        
        for article in articles:
            # Check if conversion is needed
            has_meta_object = 'meta' in article and article['meta']
            has_old_fields = 'meta_title' in article or 'meta_description' in article
            
            if not has_meta_object and has_old_fields:
                # Convert old format to new format
                meta_title = article.get('meta_title', '')
                meta_description = article.get('meta_description', '')
                
                if meta_title or meta_description:
                    article['meta'] = {
                        'title': meta_title,
                        'description': meta_description
                    }
                    converted_count += 1
                
                # Remove old fields
                if 'meta_title' in article:
                    del article['meta_title']
                if 'meta_description' in article:
                    del article['meta_description']
        
        if converted_count > 0:
            print(f"   🔄 {file_path.name}: Converting {converted_count} articles")
            
            if not dry_run:
                # Write back to file
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                print(f"   ✅ {file_path.name}: Conversion completed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ {file_path.name}: Error - {e}")
        return False

def fix_meta_consistency(dry_run: bool = True):
    """Fix meta data format consistency across all files."""
    print(f"\n🔧 {'[DRY RUN] ' if dry_run else ''}Fixing meta data format consistency...")
    
    # First analyze current state
    analysis = analyze_meta_formats()
    
    if not analysis:
        return False
    
    files_to_convert = analysis['old_format'] + analysis['mixed_format']
    
    if not files_to_convert:
        print("✅ All files already use the new meta format!")
        return True
    
    print(f"\n🔄 {'Would convert' if dry_run else 'Converting'} {len(files_to_convert)} files...")
    
    success_count = 0
    
    for file_path in files_to_convert:
        if convert_file_to_new_format(file_path, dry_run):
            success_count += 1
    
    print(f"\n📊 Results:")
    print(f"   ✅ Successfully {'analyzed' if dry_run else 'converted'}: {success_count}/{len(files_to_convert)} files")
    
    if dry_run:
        print(f"\n💡 To actually perform the conversion, run:")
        print(f"   python scripts/check_meta_consistency.py --fix")
    else:
        print(f"\n🎉 Meta data format consistency fixed!")
        print(f"   All files now use the new meta object format")
    
    return success_count == len(files_to_convert)

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Check and fix meta data format consistency')
    parser.add_argument('--fix', action='store_true', help='Actually fix the files (not just dry run)')
    args = parser.parse_args()
    
    print("🚀 Meta Data Format Consistency Checker")
    print("=" * 50)
    
    if args.fix:
        success = fix_meta_consistency(dry_run=False)
    else:
        success = fix_meta_consistency(dry_run=True)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
