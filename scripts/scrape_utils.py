#!/usr/bin/env python3
"""
Utility functions and convenience scripts for Macao Daily scraping.
"""

import json
import logging
import sys
from datetime import datetime, date, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from src.scraping.scrapers.macaodaily_scraper import MacaoDailyScraper
from src.scraping.utils.storage import DataStorage


def setup_simple_logging():
    """Setup simple logging for utility scripts."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def scrape_today() -> Optional[str]:
    """
    Convenience function to scrape today's news.
    Returns filepath if successful, None if failed.
    """
    logger = setup_simple_logging()
    today = date.today()
    
    logger.info(f"Scraping today's news: {today.strftime('%Y-%m-%d')}")
    
    try:
        storage = DataStorage()
        date_str = today.strftime('%Y-%m-%d')
        scraper = MacaoDailyScraper()  # Uses today by default

        # Check if we should skip full scraping
        should_skip, reason, current_count = storage.should_skip_full_scraping(scraper, date_str)

        if should_skip:
            logger.info(f"Skipping today's scraping: {reason}")
            existing_info = storage.get_existing_file_info('macaodaily', date_str)
            return existing_info['filepath'] if existing_info else None

        logger.info(f"Proceeding with today's scraping: {reason}")

        # Proceed with full scraping
        articles = scraper.scrape()

        if articles:
            filepath = storage.save_articles(articles, 'macaodaily', date_str)
            if filepath:
                logger.info(f"Saved {len(articles)} articles to: {filepath}")
                return filepath
            else:
                logger.warning("Failed to save articles for today")
                return None
        else:
            logger.warning("No articles found for today")
            return None
            
    except Exception as e:
        logger.error(f"Error scraping today's news: {e}")
        return None


def scrape_yesterday() -> Optional[str]:
    """
    Convenience function to scrape yesterday's news.
    Returns filepath if successful, None if failed.
    """
    logger = setup_simple_logging()
    yesterday = date.today() - timedelta(days=1)
    
    logger.info(f"Scraping yesterday's news: {yesterday.strftime('%Y-%m-%d')}")
    
    try:
        storage = DataStorage()
        date_str = yesterday.strftime('%Y-%m-%d')
        scraper = MacaoDailyScraper(target_date=date_str)

        # Check if we should skip full scraping
        should_skip, reason, current_count = storage.should_skip_full_scraping(scraper, date_str)

        if should_skip:
            logger.info(f"Skipping yesterday's scraping: {reason}")
            existing_info = storage.get_existing_file_info('macaodaily', date_str)
            return existing_info['filepath'] if existing_info else None

        logger.info(f"Proceeding with yesterday's scraping: {reason}")

        # Proceed with full scraping
        articles = scraper.scrape()

        if articles:
            filepath = storage.save_articles(articles, 'macaodaily', date_str)

            if filepath:
                logger.info(f"Saved {len(articles)} articles to: {filepath}")
                return filepath
            else:
                logger.warning("Failed to save articles for yesterday")
                return None
        else:
            logger.warning("No articles found for yesterday")
            return None
            
    except Exception as e:
        logger.error(f"Error scraping yesterday's news: {e}")
        return None


def scrape_last_week() -> List[str]:
    """
    Convenience function to scrape the last 7 days.
    Returns list of successful filepaths.
    """
    logger = setup_simple_logging()
    logger.info("Scraping last week's news (7 days)")
    
    successful_files = []
    today = date.today()
    
    for i in range(7):
        target_date = today - timedelta(days=i)
        date_str = target_date.strftime('%Y-%m-%d')
        
        try:
            # Use DataStorage for consistent file handling
            storage = DataStorage()
            scraper = MacaoDailyScraper(target_date=date_str)

            # Check if we should skip full scraping
            should_skip, reason, current_count = storage.should_skip_full_scraping(scraper, date_str)

            if should_skip:
                logger.info(f"Skipping {date_str}: {reason}")
                existing_info = storage.get_existing_file_info('macaodaily', date_str)
                if existing_info:
                    successful_files.append(existing_info['filepath'])
                continue

            logger.info(f"Scraping {date_str}: {reason}")

            # Proceed with full scraping
            articles = scraper.scrape()

            if articles:
                filepath = storage.save_articles(articles, 'macaodaily', date_str)

                if filepath:
                    logger.info(f"Saved {len(articles)} articles for {date_str}")
                    successful_files.append(filepath)
                else:
                    logger.warning(f"Failed to save articles for {date_str}")
            else:
                logger.warning(f"No articles found for {date_str}")
                
        except Exception as e:
            logger.error(f"Error scraping {date_str}: {e}")
        
        # Small delay between requests
        import time
        import random
        time.sleep(random.uniform(1, 3))
    
    logger.info(f"Completed scraping last week. Successful: {len(successful_files)}/7 days")
    return successful_files


def get_scraping_stats() -> Dict[str, Any]:
    """
    Get comprehensive statistics about all scraped data.
    Scans all raw data files and provides detailed analytics.
    """
    raw_data_dir = Path("data/raw")
    if not raw_data_dir.exists():
        return {"error": "Raw data directory not found"}

    stats = {
        "total_files": 0,
        "total_articles": 0,
        "total_days": 0,
        "average_articles_per_day": 0.0,
        "date_range": {"earliest": None, "latest": None},
        "sources": {},
        "files_by_month": {},
        "articles_by_tag": {},
        "daily_counts": {},
        "files": []
    }

    # Process all JSON files in raw data directory recursively
    for filepath in raw_data_dir.rglob("*.json"):
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Skip if not a valid scraping result file
            if 'articles' not in data:
                continue

            stats["total_files"] += 1
            article_count = data.get('article_count', len(data.get('articles', [])))
            stats["total_articles"] += article_count

            # Extract source and date information
            source = data.get('source', 'unknown')
            target_date = data.get('target_date')

            # Try to extract date from filename if not in data
            if not target_date:
                # Handle different filename patterns: YYYYMMDD.json or source_YYYYMMDD.json
                filename = filepath.stem
                if len(filename) == 8 and filename.isdigit():  # YYYYMMDD.json
                    target_date = f"{filename[:4]}-{filename[4:6]}-{filename[6:8]}"
                elif '_' in filename:
                    date_part = filename.split('_')[-1]
                    if len(date_part) == 8 and date_part.isdigit():
                        target_date = f"{date_part[:4]}-{date_part[4:6]}-{date_part[6:8]}"

            # Update source statistics
            if source not in stats["sources"]:
                stats["sources"][source] = {
                    "files": 0,
                    "articles": 0,
                    "date_range": {"earliest": None, "latest": None}
                }

            stats["sources"][source]["files"] += 1
            stats["sources"][source]["articles"] += article_count

            if target_date:
                # Update overall date range
                if not stats["date_range"]["earliest"] or target_date < stats["date_range"]["earliest"]:
                    stats["date_range"]["earliest"] = target_date
                if not stats["date_range"]["latest"] or target_date > stats["date_range"]["latest"]:
                    stats["date_range"]["latest"] = target_date

                # Update source date range
                if not stats["sources"][source]["date_range"]["earliest"] or target_date < stats["sources"][source]["date_range"]["earliest"]:
                    stats["sources"][source]["date_range"]["earliest"] = target_date
                if not stats["sources"][source]["date_range"]["latest"] or target_date > stats["sources"][source]["date_range"]["latest"]:
                    stats["sources"][source]["date_range"]["latest"] = target_date

                # Count by month
                month_key = target_date[:7]  # YYYY-MM
                stats["files_by_month"][month_key] = stats["files_by_month"].get(month_key, 0) + 1

                # Count daily articles
                stats["daily_counts"][target_date] = stats["daily_counts"].get(target_date, 0) + article_count

            # Count articles by tags
            for article in data.get('articles', []):
                tags = article.get('tags', [])
                for tag in tags:
                    stats["articles_by_tag"][tag] = stats["articles_by_tag"].get(tag, 0) + 1

            # File info
            stats["files"].append({
                "filename": filepath.name,
                "filepath": str(filepath.relative_to(raw_data_dir)),
                "source": source,
                "target_date": target_date,
                "article_count": article_count,
                "file_size": filepath.stat().st_size
            })

        except Exception as e:
            print(f"Error processing {filepath}: {e}")
            continue

    # Calculate derived statistics
    stats["total_days"] = len(stats["daily_counts"])
    if stats["total_days"] > 0:
        stats["average_articles_per_day"] = stats["total_articles"] / stats["total_days"]

    # Sort files by date
    stats["files"].sort(key=lambda x: x["target_date"] or "")

    return stats


def print_scraping_stats():
    """Print comprehensive formatted scraping statistics."""
    stats = get_scraping_stats()

    if "error" in stats:
        print(f"❌ {stats['error']}")
        return

    print("📊 Comprehensive News Scraping Statistics")
    print("=" * 60)

    # Overall statistics
    print("📈 OVERALL SUMMARY")
    print("-" * 30)
    print(f"📁 Total files: {stats['total_files']:,}")
    print(f"📰 Total articles: {stats['total_articles']:,}")
    print(f"📅 Total days with data: {stats['total_days']:,}")
    print(f"📊 Average articles per day: {stats['average_articles_per_day']:.1f}")

    if stats['date_range']['earliest'] and stats['date_range']['latest']:
        print(f"�️  Date range: {stats['date_range']['earliest']} to {stats['date_range']['latest']}")

        # Calculate date span
        from datetime import datetime
        try:
            start_date = datetime.strptime(stats['date_range']['earliest'], '%Y-%m-%d')
            end_date = datetime.strptime(stats['date_range']['latest'], '%Y-%m-%d')
            total_span_days = (end_date - start_date).days + 1
            coverage_percentage = (stats['total_days'] / total_span_days) * 100
            print(f"� Coverage: {stats['total_days']}/{total_span_days} days ({coverage_percentage:.1f}%)")
        except:
            pass

    # Source breakdown
    if stats['sources']:
        print(f"\n📰 BY NEWS SOURCE")
        print("-" * 30)
        for source, source_stats in sorted(stats['sources'].items()):
            print(f"🏢 {source}:")
            print(f"   📁 Files: {source_stats['files']:,}")
            print(f"   📰 Articles: {source_stats['articles']:,}")
            if source_stats['date_range']['earliest'] and source_stats['date_range']['latest']:
                print(f"   📅 Range: {source_stats['date_range']['earliest']} to {source_stats['date_range']['latest']}")
            avg_per_file = source_stats['articles'] / max(source_stats['files'], 1)
            print(f"   📊 Avg per file: {avg_per_file:.1f}")
            print()

    # Monthly breakdown
    if stats['files_by_month']:
        print("📅 BY MONTH")
        print("-" * 30)
        for month, count in sorted(stats['files_by_month'].items()):
            # Calculate articles for this month
            month_articles = sum(
                article_count for date, article_count in stats['daily_counts'].items()
                if date and date.startswith(month)
            )
            print(f"  {month}: {count:,} files, {month_articles:,} articles")

    # Top tags - show top 50 with total count
    if stats['articles_by_tag']:
        total_unique_tags = len(stats['articles_by_tag'])
        print(f"\n🏷️  TOP ARTICLE TAGS (Total: {total_unique_tags:,} unique tags)")
        print("-" * 60)
        sorted_tags = sorted(stats['articles_by_tag'].items(), key=lambda x: x[1], reverse=True)

        for i, (tag, count) in enumerate(sorted_tags[:50], 1):  # Top 50 tags
            percentage = (count / stats['total_articles']) * 100
            print(f"  {i:2d}. {tag}: {count:,} articles ({percentage:.1f}%)")

        if len(sorted_tags) > 50:
            remaining_tags = len(sorted_tags) - 50
            remaining_articles = sum(count for _, count in sorted_tags[50:])
            print(f"  ... and {remaining_tags:,} more tags with {remaining_articles:,} articles")

    # Recent activity
    print(f"\n📋 RECENT FILES (Last 10)")
    print("-" * 30)
    for file_info in stats['files'][-10:]:  # Last 10 files
        size_mb = file_info['file_size'] / (1024 * 1024)
        print(f"  📄 {file_info['filepath']}")
        print(f"     📅 {file_info['target_date']} | 📰 {file_info['article_count']} articles | 💾 {size_mb:.1f}MB")

    # Daily activity summary
    if stats['daily_counts']:
        print(f"\n📊 DAILY ACTIVITY SUMMARY")
        print("-" * 30)
        daily_values = list(stats['daily_counts'].values())
        print(f"📈 Highest day: {max(daily_values):,} articles")
        print(f"📉 Lowest day: {min(daily_values):,} articles")
        print(f"📊 Median: {sorted(daily_values)[len(daily_values)//2]:,} articles")

        # Show top 5 most productive days
        top_days = sorted(stats['daily_counts'].items(), key=lambda x: x[1], reverse=True)[:5]
        print(f"\n🏆 TOP 5 MOST PRODUCTIVE DAYS:")
        for i, (date, count) in enumerate(top_days, 1):
            print(f"  {i}. {date}: {count:,} articles")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python scrape_utils.py today      # Scrape today's news")
        print("  python scrape_utils.py yesterday  # Scrape yesterday's news")
        print("  python scrape_utils.py week       # Scrape last 7 days")
        print("  python scrape_utils.py stats      # Show scraping statistics")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "today":
        result = scrape_today()
        if result:
            print(f"✅ Success: {result}")
        else:
            print("❌ Failed to scrape today's news")
    
    elif command == "yesterday":
        result = scrape_yesterday()
        if result:
            print(f"✅ Success: {result}")
        else:
            print("❌ Failed to scrape yesterday's news")
    
    elif command == "week":
        results = scrape_last_week()
        print(f"✅ Scraped {len(results)} days successfully")
    
    elif command == "stats":
        print_scraping_stats()
    
    else:
        print(f"❌ Unknown command: {command}")
        sys.exit(1)
