#!/usr/bin/env python3
"""
Migration script to update rephrased data files to match new API schema.
Updates category format and maps old categories to new ones.
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any
import argparse
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

# Category mapping from old to new
CATEGORY_MAPPING = {
    # Old slug -> New title
    'tech': '科技新聞',
    'international-tech-innovation': '科技新聞',  # Merge with tech
    'economy': '經濟財經',
    'politics': '政府政策',
    'society': '社會民生',
    'health': '健康醫療',
    'education': '教育學習',
}

# Additional mapping for content-based categorization
CONTENT_KEYWORDS_MAPPING = {
    '澳門新聞': ['澳門'],
    '國際新聞': ['國際', '全球', '美國', '歐洲', '世界'],
    '體育新聞': ['體育', '運動', '比賽', '球賽', '奧運', '世界盃'],
    '文化生活': ['文化', '娛樂', '美食', '藝術', '音樂', '電影'],
    '大灣區新聞': ['大灣區', '粵港澳', '港珠澳', '跨境', '香港'],
}

def migrate_categories(old_categories: List[Any]) -> List[str]:
    """
    Convert old category format to new format.
    Old: [slug_string] or [{"slug": "...", "title": "..."}]
    New: [title_string] or [{"title": "...", "slug": "..."}]
    """
    new_categories = []
    
    for cat in old_categories:
        if isinstance(cat, str):
            # Old format: slug string
            if cat in CATEGORY_MAPPING:
                new_categories.append(CATEGORY_MAPPING[cat])
            else:
                # Unknown old category, try to map or keep as is
                print(f"⚠️  Unknown old category slug: {cat}")
                new_categories.append(cat)
        elif isinstance(cat, dict):
            # Old format: {"slug": "...", "title": "..."}
            old_slug = cat.get('slug', '')
            old_title = cat.get('title', '')
            
            if old_slug in CATEGORY_MAPPING:
                new_categories.append(CATEGORY_MAPPING[old_slug])
            elif old_title:
                new_categories.append(old_title)
            else:
                print(f"⚠️  Invalid old category format: {cat}")
    
    return new_categories

def enhance_categorization(article: Dict[str, Any]) -> List[str]:
    """
    Enhance categorization based on content analysis.
    """
    categories = article.get('categories', [])
    title = article.get('title', '').lower()
    content = article.get('content_markdown', '').lower()
    
    # Combine title and content for keyword analysis
    text_content = f"{title} {content}"
    
    # Add categories based on content keywords
    for category, keywords in CONTENT_KEYWORDS_MAPPING.items():
        if any(keyword in text_content for keyword in keywords):
            if category not in categories:
                categories.append(category)
    
    # Ensure at least one category
    if not categories:
        categories = ['澳門新聞']  # Default category
    
    return categories

def migrate_article(article: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migrate a single article to new format.
    """
    # Migrate categories
    old_categories = article.get('categories', [])
    new_categories = migrate_categories(old_categories)
    
    # Enhance categorization based on content
    enhanced_categories = enhance_categorization({
        **article,
        'categories': new_categories
    })
    
    # Update article
    article['categories'] = enhanced_categories
    
    # Ensure required fields for new API
    if 'meta_title' in article and 'meta_description' in article:
        # Move meta fields to proper structure
        if 'meta' not in article:
            article['meta'] = {}
        article['meta']['title'] = article.get('meta_title', '')
        article['meta']['description'] = article.get('meta_description', '')
        
        # Remove old fields
        article.pop('meta_title', None)
        article.pop('meta_description', None)
    
    return article

def migrate_file(file_path: Path, dry_run: bool = False) -> bool:
    """
    Migrate a single rephrased data file.
    """
    try:
        print(f"📄 Processing: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Track changes
        changes_made = False
        original_articles = len(data.get('articles', []))
        
        # Migrate each article
        for i, article in enumerate(data.get('articles', [])):
            old_categories = article.get('categories', []).copy()
            migrated_article = migrate_article(article)
            
            if migrated_article['categories'] != old_categories:
                changes_made = True
                print(f"  📝 Article {i+1}: {old_categories} → {migrated_article['categories']}")
        
        # Update metadata
        if 'migration_info' not in data:
            data['migration_info'] = []
        
        data['migration_info'].append({
            'migration_date': datetime.now().isoformat(),
            'migration_type': 'category_schema_update',
            'changes_made': changes_made,
            'articles_processed': original_articles
        })
        
        if changes_made and not dry_run:
            # Backup original file
            backup_path = file_path.with_suffix('.json.backup')
            if not backup_path.exists():
                file_path.rename(backup_path)
                print(f"  💾 Backup created: {backup_path}")
            
            # Save migrated file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"  ✅ Migrated: {file_path}")
        elif dry_run and changes_made:
            print(f"  🔍 [DRY RUN] Would migrate: {file_path}")
        else:
            print(f"  ⏭️  No changes needed: {file_path}")
        
        return changes_made
        
    except Exception as e:
        print(f"  ❌ Error processing {file_path}: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Migrate rephrased data to new API schema")
    parser.add_argument('--path', type=str, default='../data/processed/rephrased',
                       help='Path to rephrased data directory')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be changed without making changes')
    parser.add_argument('--file', type=str,
                       help='Migrate specific file instead of entire directory')
    
    args = parser.parse_args()
    
    print("🚀 Starting rephrased data migration")
    print("=" * 50)
    
    if args.file:
        # Migrate single file
        file_path = Path(args.file)
        if not file_path.exists():
            print(f"❌ File not found: {file_path}")
            return 1
        
        migrate_file(file_path, args.dry_run)
    else:
        # Migrate entire directory
        base_path = Path(args.path)
        if not base_path.exists():
            print(f"❌ Directory not found: {base_path}")
            return 1
        
        # Find all rephrased JSON files
        json_files = list(base_path.rglob('*.json'))
        if not json_files:
            print(f"❌ No JSON files found in: {base_path}")
            return 1
        
        print(f"📊 Found {len(json_files)} files to process")
        
        migrated_count = 0
        for file_path in json_files:
            if migrate_file(file_path, args.dry_run):
                migrated_count += 1
        
        print("\n" + "=" * 50)
        print(f"🎯 Migration completed!")
        print(f"📊 Files processed: {len(json_files)}")
        print(f"📝 Files migrated: {migrated_count}")
        
        if args.dry_run:
            print("🔍 This was a dry run - no files were actually changed")
            print("💡 Run without --dry-run to apply changes")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
