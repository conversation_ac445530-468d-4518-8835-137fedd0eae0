#!/usr/bin/env python3
"""
Script to show the actual featured image data structure in API requests.
"""

import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.rephrased_article import RephrasedArticle
from src.api.services.data_transformer import DataTransformer

def show_featured_image_structure():
    """Show the actual featured image data structure."""
    print("🔍 Analyzing featured image data structure")
    print("=" * 50)
    
    # Load a real rephrased file with images
    rephrased_file = Path("data/processed/rephrased/macaodaily/2025/20250713.json")
    
    if not rephrased_file.exists():
        print(f"❌ Rephrased file not found: {rephrased_file}")
        return False
    
    try:
        with open(rephrased_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles_data = data.get('articles', [])
        
        # Find an article with images
        article_with_images = None
        for article_data in articles_data:
            if article_data.get('images'):
                article_with_images = article_data
                break
        
        if not article_with_images:
            print("❌ No articles with images found")
            return False
        
        print(f"📰 Article: {article_with_images.get('title', 'Unknown')[:50]}...")
        
        # Show original image data
        original_images = article_with_images.get('images', [])
        print(f"\n📷 Original image data:")
        for i, img in enumerate(original_images):
            print(f"   Image {i+1}:")
            print(f"     • src: {img.get('src', 'NO SRC')}")
            print(f"     • description: {img.get('description', 'NO DESC')[:50]}...")
            print(f"     • alt_text: {img.get('alt_text', 'NO ALT')}")
            print(f"     • width: {img.get('width', 'NO WIDTH')}")
            print(f"     • height: {img.get('height', 'NO HEIGHT')}")
        
        # Create RephrasedArticle and transform
        article = RephrasedArticle.from_dict(article_with_images)
        transformer = DataTransformer()
        post_schema = transformer.transform_article(article)
        
        # Get API data
        post_dict = post_schema.model_dump()
        
        # Show featured image data
        if 'featuredImage' in post_dict and post_dict['featuredImage']:
            featured_image = post_dict['featuredImage']
            print(f"\n🎯 Featured image data in API:")
            print(f"   • Type: {type(featured_image)}")
            
            if isinstance(featured_image, dict):
                for key, value in featured_image.items():
                    if key == 'data' and isinstance(value, str) and len(value) > 100:
                        print(f"   • {key}: [base64 data, {len(value)} chars] {value[:50]}...")
                    elif isinstance(value, str) and len(value) > 100:
                        print(f"   • {key}: {value[:50]}...")
                    else:
                        print(f"   • {key}: {value}")
            else:
                print(f"   • Data: {featured_image}")
        else:
            print(f"\n❌ No featured image in API data")
        
        # Show full JSON structure (truncated)
        print(f"\n📋 Full API request structure:")
        api_keys = list(post_dict.keys())
        print(f"   Top-level fields: {api_keys}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing featured image: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    show_featured_image_structure()
