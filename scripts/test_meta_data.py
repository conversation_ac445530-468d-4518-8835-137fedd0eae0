#!/usr/bin/env python3
"""
Test script to verify that meta data is properly included in API requests.
"""

import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.api.services.data_transformer import DataTransformer
from src.core.models.rephrased_article import RephrasedArticle

def test_meta_data_transformation():
    """Test that meta data is properly transformed for API requests."""
    print("🧪 Testing meta data transformation...")
    
    # Create a sample rephrased article with meta data (new format)
    sample_article = RephrasedArticle(
        id="test-123",
        title="澳門政府推出新經濟政策支持中小企業發展",
        slug="macau-government-new-economic-policy-sme-support",
        content="# 澳門政府推出新經濟政策\n\n澳門特別行政區政府今日宣布推出一系列新的經濟政策措施...",
        summary="澳門政府宣布新經濟政策，支持中小企業發展，促進經濟多元化。",
        source_url="https://example.com/article",
        source_site_name="澳門日報",
        categories=["經濟財經", "政府政策"],
        keywords=["澳門", "經濟政策", "中小企業", "政府支持"],
        publish_date="2025-07-18T10:00:00Z"
    )

    # Add meta data in new format (as dict)
    sample_article.meta = {
        "title": "澳門新經濟政策 | 中小企業支持措施 2025",
        "description": "澳門政府推出全新經濟政策，重點支持中小企業發展和創新，為澳門經濟注入新活力，促進多元化發展。"
    }
    
    # Test data transformer
    transformer = DataTransformer()
    
    try:
        # Transform single article
        post_schema = transformer.transform_article(sample_article)
        
        print("✅ Article transformation successful")
        print(f"📝 Title: {post_schema.title}")
        print(f"🔗 Slug: {post_schema.slug}")
        print(f"📂 Categories: {post_schema.categories}")
        
        # Check meta data
        if post_schema.meta:
            print("✅ Meta data found:")
            print(f"   📋 Meta Title: {post_schema.meta.title}")
            print(f"   📄 Meta Description: {post_schema.meta.description}")
            
            # Validate meta data content
            if post_schema.meta.title and post_schema.meta.description:
                print("✅ Both meta title and description are present")
                
                # Check length constraints
                title_len = len(post_schema.meta.title)
                desc_len = len(post_schema.meta.description)
                
                print(f"📏 Meta title length: {title_len} characters")
                print(f"📏 Meta description length: {desc_len} characters")
                
                if 50 <= title_len <= 60:
                    print("✅ Meta title length is within recommended range (50-60)")
                else:
                    print(f"⚠️  Meta title length ({title_len}) outside recommended range (50-60)")
                
                if 100 <= desc_len <= 150:
                    print("✅ Meta description length is within recommended range (100-150)")
                else:
                    print(f"⚠️  Meta description length ({desc_len}) outside recommended range (100-150)")
                
            else:
                print("❌ Meta title or description is missing")
                return False
        else:
            print("❌ No meta data found in transformed article")
            return False
        
        # Test batch transformation
        print("\n🧪 Testing batch transformation...")
        request_schema = transformer.transform_articles_batch([sample_article])
        
        if request_schema.posts and len(request_schema.posts) > 0:
            first_post = request_schema.posts[0]
            if first_post.meta:
                print("✅ Meta data preserved in batch transformation")
                print(f"   📋 Batch Meta Title: {first_post.meta.title}")
                print(f"   📄 Batch Meta Description: {first_post.meta.description}")
            else:
                print("❌ Meta data lost in batch transformation")
                return False
        else:
            print("❌ No posts in batch transformation result")
            return False
        
        # Test JSON serialization (what gets sent to API)
        print("\n🧪 Testing JSON serialization...")
        json_data = request_schema.model_dump()
        
        if 'posts' in json_data and len(json_data['posts']) > 0:
            first_post_json = json_data['posts'][0]
            if 'meta' in first_post_json and first_post_json['meta']:
                meta_json = first_post_json['meta']
                print("✅ Meta data present in JSON:")
                print(f"   📋 JSON Meta Title: {meta_json.get('title', 'MISSING')}")
                print(f"   📄 JSON Meta Description: {meta_json.get('description', 'MISSING')}")
                
                # Pretty print the meta section
                print("\n📋 Complete meta object in JSON:")
                print(json.dumps(meta_json, ensure_ascii=False, indent=2))
                
            else:
                print("❌ Meta data missing in JSON serialization")
                return False
        else:
            print("❌ No posts in JSON serialization")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during transformation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_rephrased_file():
    """Test with a real rephrased file to see if meta data is present."""
    print("\n🧪 Testing with real rephrased file...")
    
    # Find a recent rephrased file
    data_path = Path("data/processed/rephrased/macaodaily")
    
    # Look for recent files
    json_files = []
    for year_dir in data_path.glob("*"):
        if year_dir.is_dir():
            json_files.extend(year_dir.glob("*.json"))
    
    if not json_files:
        print("❌ No rephrased files found")
        return False
    
    # Use the most recent file
    latest_file = max(json_files, key=lambda f: f.stat().st_mtime)
    print(f"📁 Testing with file: {latest_file}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles = data.get('articles', [])
        if not articles:
            print("❌ No articles found in file")
            return False
        
        # Check first few articles for meta data (both old and new formats)
        meta_count = 0
        total_checked = min(5, len(articles))

        for i, article_data in enumerate(articles[:total_checked]):
            article_id = article_data.get('id', f'article-{i}')

            # Check for new format (meta object)
            has_meta_object = 'meta' in article_data and article_data['meta']
            meta_title = None
            meta_desc = None

            if has_meta_object:
                meta_obj = article_data['meta']
                meta_title = meta_obj.get('title', '')
                meta_desc = meta_obj.get('description', '')
                has_complete_meta = bool(meta_title and meta_desc)
            else:
                # Check for old format (separate fields)
                meta_title = article_data.get('meta_title', '')
                meta_desc = article_data.get('meta_description', '')
                has_complete_meta = bool(meta_title and meta_desc)

            if has_complete_meta:
                meta_count += 1
                print(f"✅ Article {i+1} ({article_id}): Has meta data")
                print(f"   📋 Meta Title: {meta_title[:50]}...")
                print(f"   📄 Meta Description: {meta_desc[:50]}...")
                print(f"   📊 Format: {'New (meta object)' if has_meta_object else 'Old (separate fields)'}")
            else:
                print(f"❌ Article {i+1} ({article_id}): Missing meta data")
                print(f"   📋 Has meta object: {has_meta_object}")
                if has_meta_object:
                    print(f"   📋 Meta title: {bool(meta_title)}")
                    print(f"   📄 Meta description: {bool(meta_desc)}")
                else:
                    print(f"   📋 Has meta_title field: {'meta_title' in article_data}")
                    print(f"   📄 Has meta_description field: {'meta_description' in article_data}")
        
        print(f"\n📊 Meta data summary: {meta_count}/{total_checked} articles have complete meta data")
        
        if meta_count > 0:
            print("✅ Real rephrased files contain meta data")
            return True
        else:
            print("❌ Real rephrased files are missing meta data")
            return False
        
    except Exception as e:
        print(f"❌ Error reading rephrased file: {e}")
        return False

def main():
    """Run all meta data tests."""
    print("🚀 Testing meta data in API upload requests")
    print("=" * 60)
    
    tests = [
        test_meta_data_transformation,
        test_real_rephrased_file
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Meta data is properly included in API requests.")
        return 0
    else:
        print("❌ Some tests failed. Meta data may not be properly included.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
