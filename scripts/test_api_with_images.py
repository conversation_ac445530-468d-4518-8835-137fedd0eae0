#!/usr/bin/env python3
"""
Test script to verify that API requests include images from rephrased articles.
"""

import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.rephrased_article import RephrasedArticle
from src.api.services.data_transformer import DataTransformer

def test_api_includes_images():
    """Test that API transformation includes images from rephrased articles."""
    print("🧪 Testing API transformation with images...")
    
    # Load a real rephrased file with images
    rephrased_file = Path("data/processed/rephrased/macaodaily/2025/20250713.json")
    
    if not rephrased_file.exists():
        print(f"❌ Rephrased file not found: {rephrased_file}")
        return False
    
    try:
        with open(rephrased_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles_data = data.get('articles', [])
        if not articles_data:
            print("❌ No articles found in file")
            return False
        
        print(f"📁 Loaded {len(articles_data)} rephrased articles")
        
        # Find an article with images
        article_with_images = None
        for article_data in articles_data:
            if article_data.get('images'):
                article_with_images = article_data
                break
        
        if not article_with_images:
            print("❌ No articles with images found")
            return False
        
        print(f"✅ Found article with images: {article_with_images.get('title', 'Unknown')[:50]}...")
        print(f"📷 Images in article: {len(article_with_images['images'])}")
        
        # Create RephrasedArticle object
        article = RephrasedArticle.from_dict(article_with_images)
        
        print(f"✅ RephrasedArticle created successfully")
        print(f"📷 Images in object: {len(article.images)}")
        
        # Transform for API
        transformer = DataTransformer()
        post_schema = transformer.transform_article(article)
        
        print(f"✅ Article transformed for API")
        
        # Check API structure
        post_dict = post_schema.model_dump()
        
        # Check if featuredImage is included
        if 'featuredImage' in post_dict and post_dict['featuredImage']:
            featured_image = post_dict['featuredImage']
            print(f"✅ Featured image included in API request:")
            print(f"   📷 URL: {featured_image.get('url', 'NO URL')}")
            print(f"   📝 Alt: {featured_image.get('alt', 'NO ALT')}")
            print(f"   📊 Type: {type(featured_image)}")
            return True
        else:
            print("❌ Featured image missing in API request")
            print(f"🔍 Available fields: {list(post_dict.keys())}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing API with images: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_api_with_images():
    """Test that batch API transformation includes images."""
    print("\n🧪 Testing batch API transformation with images...")
    
    # Load a real rephrased file with images
    rephrased_file = Path("data/processed/rephrased/macaodaily/2025/20250713.json")
    
    if not rephrased_file.exists():
        print(f"❌ Rephrased file not found: {rephrased_file}")
        return False
    
    try:
        with open(rephrased_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles_data = data.get('articles', [])[:3]  # Test first 3 articles
        
        # Create RephrasedArticle objects
        articles = []
        for article_data in articles_data:
            if article_data.get('images'):  # Only articles with images
                articles.append(RephrasedArticle.from_dict(article_data))
        
        if not articles:
            print("❌ No articles with images found")
            return False
        
        print(f"📁 Testing {len(articles)} articles with images")
        
        # Transform batch for API
        transformer = DataTransformer()
        request_schema = transformer.transform_articles_batch(articles)
        
        print(f"✅ Batch transformation successful")
        
        # Check API structure
        request_dict = request_schema.model_dump()
        
        if 'posts' not in request_dict:
            print("❌ No posts in API request")
            return False
        
        posts = request_dict['posts']
        print(f"📰 API request contains {len(posts)} posts")
        
        # Check each post for featured image
        posts_with_images = 0
        for i, post in enumerate(posts):
            if 'featuredImage' in post and post['featuredImage']:
                posts_with_images += 1
                featured_image = post['featuredImage']
                print(f"✅ Post {i+1}: Featured image included")
                print(f"   📷 URL: {featured_image.get('url', 'NO URL')[:50]}...")
                print(f"   📝 Alt: {featured_image.get('alt', 'NO ALT')[:30]}...")
            else:
                print(f"❌ Post {i+1}: No featured image")
        
        print(f"\n📊 Summary: {posts_with_images}/{len(posts)} posts have featured images")
        
        return posts_with_images == len(posts)
        
    except Exception as e:
        print(f"❌ Error testing batch API with images: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_serialization_with_images():
    """Test that API requests with images can be serialized to JSON."""
    print("\n🧪 Testing JSON serialization with images...")
    
    # Load a real rephrased file with images
    rephrased_file = Path("data/processed/rephrased/macaodaily/2025/20250713.json")
    
    if not rephrased_file.exists():
        print(f"❌ Rephrased file not found: {rephrased_file}")
        return False
    
    try:
        with open(rephrased_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles_data = data.get('articles', [])
        
        # Find an article with images
        article_with_images = None
        for article_data in articles_data:
            if article_data.get('images'):
                article_with_images = article_data
                break
        
        if not article_with_images:
            print("❌ No articles with images found")
            return False
        
        # Create and transform article
        article = RephrasedArticle.from_dict(article_with_images)
        transformer = DataTransformer()
        request_schema = transformer.transform_articles_batch([article])
        
        # Serialize to JSON
        json_data = request_schema.model_dump()
        json_str = json.dumps(json_data, ensure_ascii=False, indent=2, default=str)
        
        print("✅ JSON serialization successful")
        
        # Parse back and verify
        parsed_data = json.loads(json_str)
        
        if 'posts' in parsed_data and len(parsed_data['posts']) > 0:
            first_post = parsed_data['posts'][0]
            
            if 'featuredImage' in first_post and first_post['featuredImage']:
                featured_image = first_post['featuredImage']
                print(f"✅ Featured image preserved in JSON:")
                print(f"   📷 URL: {featured_image.get('url', 'NO URL')[:50]}...")
                print(f"   📝 Alt: {featured_image.get('alt', 'NO ALT')[:30]}...")
                return True
            else:
                print("❌ Featured image missing in JSON")
                return False
        else:
            print("❌ No posts in parsed JSON")
            return False
        
    except Exception as e:
        print(f"❌ JSON serialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all API image tests."""
    print("🚀 Testing API transformation with images")
    print("=" * 60)
    
    tests = [
        test_api_includes_images,
        test_batch_api_with_images,
        test_json_serialization_with_images
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! API requests now include images.")
        print("\n📋 Summary:")
        print("   • Rephrased articles include first image from original articles")
        print("   • API transformation converts images to featuredImage format")
        print("   • JSON serialization preserves image data")
        print("   • Batch processing handles multiple articles with images")
        return 0
    else:
        print("❌ Some tests failed. API image handling may have issues.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
