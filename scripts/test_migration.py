#!/usr/bin/env python3
"""
Test script to validate migration and upload functionality.
"""

import json
import sys
from pathlib import Path
from typing import Dict, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.api.services.data_transformer import DataTransformer
from src.core.models.rephrased_article import RephrasedArticle

def test_category_transformation():
    """Test category transformation logic."""
    print("🧪 Testing category transformation...")
    
    transformer = DataTransformer()
    
    # Test cases
    test_cases = [
        # Old format: slug strings
        {
            'input': ['tech', 'economy', 'politics'],
            'expected': ['科技新聞', '經濟財經', '政府政策'],
            'description': 'Old slug strings'
        },
        # Old format: objects with slug
        {
            'input': [
                {'slug': 'tech', 'title': 'Technology'},
                {'slug': 'society', 'title': 'Social Issues'}
            ],
            'expected': ['科技新聞', '社會民生'],
            'description': 'Old format objects'
        },
        # New format: title strings
        {
            'input': ['澳門新聞', '國際新聞', '體育新聞'],
            'expected': ['澳門新聞', '國際新聞', '體育新聞'],
            'description': 'New title strings'
        },
        # Mixed format
        {
            'input': [
                'tech',  # Old slug
                '澳門新聞',  # New title
                {'slug': 'economy', 'title': 'Economics'},  # Old object
                {'title': '大灣區新聞', 'slug': 'greater-bay-area'}  # New object
            ],
            'expected': ['科技新聞', '澳門新聞', '經濟財經', '大灣區新聞'],
            'description': 'Mixed formats'
        },
        # Duplicates
        {
            'input': ['tech', '科技新聞', 'international-tech-innovation'],
            'expected': ['科技新聞'],  # Should deduplicate
            'description': 'Duplicates removal'
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            result = transformer._transform_categories(test_case['input'])
            
            if result == test_case['expected']:
                print(f"  ✅ Test {i} ({test_case['description']}): PASSED")
            else:
                print(f"  ❌ Test {i} ({test_case['description']}): FAILED")
                print(f"     Input: {test_case['input']}")
                print(f"     Expected: {test_case['expected']}")
                print(f"     Got: {result}")
                all_passed = False
                
        except Exception as e:
            print(f"  ❌ Test {i} ({test_case['description']}): ERROR - {e}")
            all_passed = False
    
    return all_passed

def test_article_transformation():
    """Test full article transformation."""
    print("\n🧪 Testing article transformation...")
    
    transformer = DataTransformer()
    
    # Create a mock rephrased article with old format
    article_data = {
        'id': 'test-123',
        'title': 'Test Article',
        'slug': 'test-article',
        'content': '# Test Content\n\nThis is a test article.',
        'summary': 'Test summary',
        'source_url': 'https://example.com/article',
        'source_site_name': 'Test Site',
        'categories': ['tech', 'economy'],  # Old format
        'meta_title': 'Test Meta Title',
        'meta_description': 'Test meta description',
        'publish_date': '2025-07-13T10:00:00Z'
    }
    
    try:
        # Create RephrasedArticle instance
        article = RephrasedArticle(**article_data)
        
        # Transform to API format
        post_schema = transformer.transform_article(article)
        
        # Validate transformation
        expected_categories = ['科技新聞', '經濟財經']
        
        if post_schema.categories == expected_categories:
            print("  ✅ Article transformation: PASSED")
            print(f"     Categories: {article_data['categories']} → {post_schema.categories}")
            return True
        else:
            print("  ❌ Article transformation: FAILED")
            print(f"     Expected categories: {expected_categories}")
            print(f"     Got categories: {post_schema.categories}")
            return False
            
    except Exception as e:
        print(f"  ❌ Article transformation: ERROR - {e}")
        return False

def test_migration_script():
    """Test the migration script logic."""
    print("\n🧪 Testing migration script logic...")
    
    # Import migration functions
    sys.path.insert(0, str(Path(__file__).parent))
    from migrate_rephrased_data import migrate_categories, enhance_categorization
    
    # Test migrate_categories function
    test_cases = [
        {
            'input': ['tech', 'economy'],
            'expected': ['科技新聞', '經濟財經'],
            'description': 'Basic slug migration'
        },
        {
            'input': [{'slug': 'politics', 'title': 'Politics'}],
            'expected': ['政府政策'],
            'description': 'Object migration'
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            result = migrate_categories(test_case['input'])
            
            if result == test_case['expected']:
                print(f"  ✅ Migration test {i}: PASSED")
            else:
                print(f"  ❌ Migration test {i}: FAILED")
                print(f"     Expected: {test_case['expected']}")
                print(f"     Got: {result}")
                all_passed = False
                
        except Exception as e:
            print(f"  ❌ Migration test {i}: ERROR - {e}")
            all_passed = False
    
    # Test content-based categorization
    test_article = {
        'title': '澳門交通意外新聞',
        'content_markdown': '今日澳門發生交通意外，市民關注交通安全問題。',
        'categories': ['society']
    }
    
    try:
        enhanced_categories = enhance_categorization(test_article)
        
        if '澳門新聞' in enhanced_categories:
            print("  ✅ Content-based categorization: PASSED")
            print(f"     Enhanced categories: {enhanced_categories}")
        else:
            print("  ❌ Content-based categorization: FAILED")
            print(f"     Expected '澳門新聞' in: {enhanced_categories}")
            all_passed = False
            
    except Exception as e:
        print(f"  ❌ Content-based categorization: ERROR - {e}")
        all_passed = False
    
    return all_passed

def main():
    """Run all tests."""
    print("🚀 Starting migration and transformation tests")
    print("=" * 60)
    
    tests = [
        test_category_transformation,
        test_article_transformation,
        test_migration_script
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Migration system is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
