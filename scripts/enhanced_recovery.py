#!/usr/bin/env python3
"""
Enhanced recovery script for missing source_url and original_article_id.
Uses multiple matching strategies to maximize recovery rate.
"""

import json
import sys
from pathlib import Path
from datetime import datetime
import re

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.article import ScrapedArticle

def normalize_text(text):
    """Normalize text for comparison."""
    if not text:
        return ""
    # Remove extra whitespace, convert to lowercase
    return re.sub(r'\s+', ' ', text.strip().lower())

def extract_keywords(text):
    """Extract key words from text for fuzzy matching."""
    if not text:
        return set()
    # Remove common words and extract meaningful keywords
    words = normalize_text(text).split()
    # Filter out very short words and common words
    keywords = {word for word in words if len(word) > 3 and word not in {'澳門', '香港', '中國', '今日', '昨日'}}
    return keywords

def calculate_similarity(text1, text2):
    """Calculate similarity between two texts."""
    if not text1 or not text2:
        return 0.0
    
    keywords1 = extract_keywords(text1)
    keywords2 = extract_keywords(text2)
    
    if not keywords1 or not keywords2:
        return 0.0
    
    intersection = keywords1.intersection(keywords2)
    union = keywords1.union(keywords2)
    
    return len(intersection) / len(union) if union else 0.0

def enhanced_recovery_for_file(rephrased_file: Path):
    """Enhanced recovery for a single rephrased file."""
    print(f"🔧 Enhanced recovery for {rephrased_file}...")
    
    try:
        # Load rephrased data
        with open(rephrased_file, 'r', encoding='utf-8') as f:
            rephrased_data = json.load(f)
        
        # Extract source and date info
        source = rephrased_data.get('source', 'macaodaily')
        target_date = rephrased_data.get('target_date', '')
        
        if not target_date:
            print(f"  ⚠️  No target_date found")
            return
        
        # Find corresponding raw data file
        raw_file = Path(f"data/raw/{source}/2025/{target_date}.json")
        if not raw_file.exists():
            print(f"  ⚠️  Raw file not found: {raw_file}")
            return
        
        # Load raw data
        with open(raw_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
        
        # Create comprehensive mapping
        raw_articles = []
        content_length_map = {}
        title_similarity_map = {}
        
        for raw_article_data in raw_data.get('articles', []):
            article = ScrapedArticle.from_dict(raw_article_data)
            url = article.source_url or article.original_url
            
            article_info = {
                'id': article.id,
                'url': url,
                'title': article.title,
                'content': article.content,
                'content_length': len(article.content)
            }
            raw_articles.append(article_info)
            
            # Content length mapping
            length = len(article.content)
            if length not in content_length_map:
                content_length_map[length] = []
            content_length_map[length].append(article_info)
        
        print(f"  📊 Loaded {len(raw_articles)} raw articles for matching")
        
        # Process articles that need fixing
        articles_to_fix = []
        for i, article in enumerate(rephrased_data.get('articles', [])):
            needs_id = article.get('original_article_id') is None
            needs_url = article.get('source_url', '') == ''
            
            if needs_id or needs_url:
                articles_to_fix.append((i, article))
        
        print(f"  🎯 Found {len(articles_to_fix)} articles needing recovery")
        
        fixed_count = 0
        
        for article_index, article in articles_to_fix:
            original_content_length = article.get('processing_metadata', {}).get('original_content_length')
            rephrased_title = article.get('title', '')
            
            matched_article = None
            match_method = None
            
            # Strategy 1: Exact content length match (if unique)
            if original_content_length and original_content_length in content_length_map:
                candidates = content_length_map[original_content_length]
                if len(candidates) == 1:
                    matched_article = candidates[0]
                    match_method = f"exact_length({original_content_length})"
                elif len(candidates) > 1:
                    # Strategy 2: Content length + title similarity
                    best_similarity = 0
                    best_candidate = None
                    
                    for candidate in candidates:
                        similarity = calculate_similarity(rephrased_title, candidate['title'])
                        if similarity > best_similarity and similarity > 0.3:  # Minimum similarity threshold
                            best_similarity = similarity
                            best_candidate = candidate
                    
                    if best_candidate:
                        matched_article = best_candidate
                        match_method = f"length+similarity({original_content_length}, {best_similarity:.2f})"
            
            # Strategy 3: Title similarity across all articles (if no content length match)
            if not matched_article:
                best_similarity = 0
                best_candidate = None
                
                for candidate in raw_articles:
                    similarity = calculate_similarity(rephrased_title, candidate['title'])
                    if similarity > best_similarity and similarity > 0.5:  # Higher threshold for title-only matching
                        best_similarity = similarity
                        best_candidate = candidate
                
                if best_candidate:
                    matched_article = best_candidate
                    match_method = f"title_similarity({best_similarity:.2f})"
            
            # Apply the match
            if matched_article:
                if article.get('original_article_id') is None:
                    article['original_article_id'] = matched_article['id']
                if article.get('source_url', '') == '':
                    article['source_url'] = matched_article['url']
                
                fixed_count += 1
                print(f"    ✅ Fixed article {article_index + 1}: {rephrased_title[:40]}... [{match_method}]")
            else:
                print(f"    ❌ Could not match: {rephrased_title[:40]}...")
        
        if fixed_count > 0:
            # Update metadata
            rephrased_data['processed_at'] = datetime.now().isoformat()
            
            # Save updated file
            with open(rephrased_file, 'w', encoding='utf-8') as f:
                json.dump(rephrased_data, f, indent=2, ensure_ascii=False)
            
            print(f"  ✅ Enhanced recovery completed: {fixed_count} articles fixed")
        else:
            print(f"  ℹ️  No additional articles could be recovered")
            
    except Exception as e:
        print(f"  ❌ Error: {e}")

def main():
    """Run enhanced recovery for specific files."""
    files_to_process = [
        Path("data/processed/rephrased/macaodaily/2025/20250625.json"),
        Path("data/processed/rephrased/macaodaily/2025/20250626.json")
    ]
    
    print("🚀 Starting enhanced recovery process...")
    
    for file_path in files_to_process:
        if file_path.exists():
            enhanced_recovery_for_file(file_path)
        else:
            print(f"❌ File not found: {file_path}")
    
    print("🎉 Enhanced recovery completed!")

if __name__ == "__main__":
    main()
