#!/usr/bin/env python3
"""
Test script to verify the updated rephrase prompts work correctly with new categories.
"""

import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.article import ScrapedArticle
from src.rephrasing.templates.prompts import PromptBuilder
from src.rephrasing.models.response_models import RephrasedContent, BatchRephrasedContent

def test_prompt_generation():
    """Test that prompts are generated correctly with new categories."""
    print("🧪 Testing prompt generation with new categories...")
    
    # Create a sample article
    sample_article = ScrapedArticle(
        id="test-123",
        title="澳門政府推出新經濟政策支持中小企業發展",
        content="澳門特別行政區政府今日宣布推出一系列新的經濟政策措施，旨在支持本地中小企業發展，促進經濟多元化。新政策包括稅收優惠、資金支持和技術培訓等多項措施。",
        source_url="https://example.com/article",
        source_site_name="澳門日報",
        tags=["A01", "澳聞"],
        created_at="2025-07-18T10:00:00Z"
    )
    
    # Test prompt builder
    prompt_builder = PromptBuilder()
    
    try:
        # Generate rephrase prompt
        rephrase_prompt = prompt_builder.build_rephrase_prompt(sample_article)
        
        print("✅ Rephrase prompt generated successfully")
        print("📝 Checking for new categories in prompt...")
        
        # Check if new categories are mentioned in the prompt
        new_categories = [
            "澳門新聞", "政府政策", "經濟財經", "社會民生", "國際新聞",
            "體育新聞", "文化生活", "科技新聞", "大灣區新聞", "健康醫療", "教育學習"
        ]
        
        categories_found = []
        for category in new_categories:
            if category in rephrase_prompt:
                categories_found.append(category)
        
        if len(categories_found) >= 10:  # Should find most categories
            print(f"✅ Found {len(categories_found)} new categories in prompt")
            print(f"   Categories: {', '.join(categories_found[:5])}...")
        else:
            print(f"❌ Only found {len(categories_found)} categories: {categories_found}")
            return False
        
        # Check that old categories are not present
        old_categories = ["tech", "international-tech-innovation", "economy", "politics", "society", "health", "education"]
        old_found = []
        for old_cat in old_categories:
            if old_cat in rephrase_prompt and old_cat not in ["economy", "politics", "society", "health", "education"]:  # These might appear in descriptions
                old_found.append(old_cat)
        
        if old_found:
            print(f"⚠️  Found old category references: {old_found}")
        else:
            print("✅ No old category slugs found in prompt")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating prompt: {e}")
        return False

def test_response_models():
    """Test that response models have correct category descriptions."""
    print("\n🧪 Testing response models...")
    
    try:
        # Test RephrasedContent model
        model_schema = RephrasedContent.model_json_schema()
        categories_field = model_schema['properties']['categories']
        
        print("✅ RephrasedContent model loaded successfully")
        print(f"📝 Categories field description: {categories_field['description'][:100]}...")
        
        # Check if new categories are in the description
        if "澳門新聞" in categories_field['description'] and "政府政策" in categories_field['description']:
            print("✅ New categories found in model description")
        else:
            print("❌ New categories not found in model description")
            return False
        
        # Test example data
        example = RephrasedContent.model_config.get('json_schema_extra', {}).get('example', {})
        if example and 'categories' in example:
            example_categories = example['categories']
            print(f"📋 Example categories: {example_categories}")
            
            # Check if example uses new format
            if any(cat in ["澳門新聞", "政府政策", "經濟財經", "社會民生"] for cat in example_categories):
                print("✅ Example uses new category format")
            else:
                print("❌ Example still uses old category format")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing response models: {e}")
        return False

def test_classification_prompt():
    """Test classification prompt has new categories."""
    print("\n🧪 Testing classification prompt...")
    
    try:
        prompt_builder = PromptBuilder()
        
        # Generate classification prompt
        classification_prompt = prompt_builder.build_classification_prompt(
            title="澳門政府推出新政策",
            content="政府宣布新的經濟措施支持本地企業發展",
            original_tags=["A01", "澳聞"]
        )
        
        print("✅ Classification prompt generated successfully")
        
        # Check for new categories
        new_categories = ["澳門新聞", "政府政策", "經濟財經", "大灣區新聞"]
        found_categories = [cat for cat in new_categories if cat in classification_prompt]
        
        if len(found_categories) >= 3:
            print(f"✅ Found {len(found_categories)} new categories in classification prompt")
        else:
            print(f"❌ Only found {len(found_categories)} categories in classification prompt")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing classification prompt: {e}")
        return False

def main():
    """Run all prompt tests."""
    print("🚀 Testing updated rephrase prompts")
    print("=" * 50)
    
    tests = [
        test_prompt_generation,
        test_response_models,
        test_classification_prompt
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Updated prompts are ready.")
        print("\n📋 Summary of changes:")
        print("   • Updated category list from 7 old slugs to 11 new Chinese titles")
        print("   • Enhanced category descriptions with clear scope definitions")
        print("   • Updated response model examples to use new format")
        print("   • Maintained backward compatibility in data transformer")
        return 0
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
