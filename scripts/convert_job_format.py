#!/usr/bin/env python3
"""
Convert old job_info format to new job_history format in rephrased files.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

def convert_file(file_path: Path):
    """Convert a single file from old format to new format."""
    print(f"Converting {file_path}...")

    try:
        # Load existing data
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        needs_update = False

        # Convert old job_info to new job_history if needed
        if 'job_info' in data and 'job_history' not in data:
            old_job = data['job_info']

            # Create clean job record
            clean_job = {
                "id": old_job.get('id', 'unknown'),
                "started_at": old_job.get('started_at', datetime.now().isoformat()),
                "completed_at": old_job.get('completed_at') or datetime.now().isoformat(),
                "status": "completed" if old_job.get('status') == 'running' else old_job.get('status', 'completed'),
                "total_articles": old_job.get('total_articles', len(data.get('articles', []))),
                "successful_articles": old_job.get('successful_articles', len(data.get('articles', []))),
                "failed_articles": old_job.get('failed_articles', 0),
                "ai_model": data.get('ai_model', 'gemini-2.5-flash'),
                "batch_size": 8  # Default batch size
            }

            # Add error message only if present
            if old_job.get('error_message'):
                clean_job["error_message"] = old_job['error_message']

            # Create new format
            data['job_history'] = [clean_job]
            del data['job_info']
            needs_update = True

        # Add expected_article_count if missing
        if 'expected_article_count' not in data:
            # Try to get from job history
            job_history = data.get('job_history', [])
            if job_history:
                expected_count = job_history[-1].get('total_articles', len(data.get('articles', [])))
            else:
                expected_count = len(data.get('articles', []))

            data['expected_article_count'] = expected_count
            needs_update = True
            print(f"  📊 Added expected_article_count: {expected_count}")

        # Update metadata timestamp
        if needs_update:
            data['processed_at'] = datetime.now().isoformat()

            # Save converted file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            print(f"  ✅ Updated successfully")
        else:
            print(f"  ✅ Already up to date")

    except Exception as e:
        print(f"  ❌ Error: {e}")

def main():
    """Convert all rephrased files to new format."""
    rephrased_dir = Path("data/processed/rephrased")
    
    if not rephrased_dir.exists():
        print("❌ Rephrased directory not found")
        return
    
    # Find all JSON files
    json_files = list(rephrased_dir.rglob("*.json"))
    
    if not json_files:
        print("❌ No JSON files found")
        return
    
    print(f"🔍 Found {len(json_files)} files to convert")
    
    for file_path in json_files:
        convert_file(file_path)
    
    print("🎉 Conversion completed!")

if __name__ == "__main__":
    main()
