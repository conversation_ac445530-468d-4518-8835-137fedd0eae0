#!/usr/bin/env python3
"""
Script to show the corrected API request structure with keywords at the top level.
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.rephrased_article import RephrasedArticle
from src.api.services.data_transformer import DataTransformer

def show_corrected_structure():
    """Show the corrected API request structure."""
    print("📤 Corrected API Request Structure")
    print("=" * 50)
    
    # Create a sample article
    article = RephrasedArticle(
        id="demo-123",
        title="澳門歷史城區保護報告獲世遺會通過，肯定保育成果",
        slug="macau-historic-centre-unesco-report-approved",
        content="# 澳門歷史城區保護報告獲世遺會通過，肯定保育成果\n\n第四十七屆世界遺產委員會會議於本月7日至16日在法國巴黎舉行...",
        summary="澳門歷史城區的保護狀況報告在第四十七屆世界遺產委員會會議上獲決議通過，肯定澳門在遺產保護方面的努力。",
        original_article_id="original-demo-123",
        source_url="http://www.macaodaily.com/html/2025-07/12/content_1844355.htm",
        source_site_name="澳門日報",
        ai_model_used="gemini-2.5-flash",
        processing_timestamp=datetime.now(),
        processing_metadata={
            "original_title": "澳歷史城區報告獲世遺會通過",
            "original_content_length": 667,
            "rephrased_content_length": 707,
            "batch_size": 8,
            "batch_index": 2,
            "prompt_tokens": 700,
            "response_tokens": 652
        },
        categories=["社會民生", "澳門新聞"],
        tags=["A02", "澳聞"],
        keywords=["澳門", "世界遺產", "歷史城區", "文化遺產", "聯合國教科文組織", "西夏陵", "文物保護"],
        priority_score=0.8,
        quality_score=0.0,
        meta_title="澳門歷史城區保護報告獲世遺會通過，肯定保育成果",
        meta_description="澳門歷史城區的保護狀況報告在第四十七屆世界遺產委員會會議上獲決議通過，肯定澳門在遺產保護方面的努力。文化局同時祝賀中國「西夏陵」成功申遺。",
        images=[]
    )
    
    # Transform for API
    transformer = DataTransformer()
    request_schema = transformer.transform_articles_batch([article])
    
    # Convert to dict and show structure
    request_dict = request_schema.model_dump()

    print("🔧 API Request Structure (JSON):")
    print(json.dumps(request_dict, ensure_ascii=False, indent=2, default=str))
    
    print("\n" + "=" * 50)
    print("📋 Key Changes Made:")
    print("✅ Keywords moved from extra.keywords to top-level keywords")
    print("✅ Keywords array is now at the same level as categories, meta, etc.")
    print("✅ Extra object now only contains processing metadata and tags")
    
    # Show the specific structure
    first_post = request_dict['posts'][0]
    
    print(f"\n🎯 Top-level fields in post:")
    for key in sorted(first_post.keys()):
        value = first_post[key]
        if isinstance(value, list):
            print(f"   • {key}: [{len(value)} items] - {value[:2]}{'...' if len(value) > 2 else ''}")
        elif isinstance(value, dict):
            print(f"   • {key}: {{dict with {len(value)} keys}} - {list(value.keys())}")
        elif isinstance(value, str) and len(value) > 50:
            print(f"   • {key}: \"{value[:50]}...\"")
        else:
            print(f"   • {key}: {value}")
    
    print(f"\n🏷️  Keywords specifically:")
    print(f"   📍 Location: Top level (not in extra)")
    print(f"   📊 Count: {len(first_post['keywords'])}")
    print(f"   📝 Values: {first_post['keywords']}")
    
    if 'extra' in first_post and first_post['extra']:
        print(f"\n📦 Extra object contents:")
        for key, value in first_post['extra'].items():
            if isinstance(value, list):
                print(f"   • {key}: [{len(value)} items]")
            elif isinstance(value, dict):
                print(f"   • {key}: {{dict with {len(value)} keys}}")
            else:
                print(f"   • {key}: {value}")
        
        if 'keywords' in first_post['extra']:
            print("   ❌ ERROR: Keywords found in extra (should not be here)")
        else:
            print("   ✅ Keywords correctly NOT in extra")

if __name__ == "__main__":
    show_corrected_structure()
