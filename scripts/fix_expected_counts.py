#!/usr/bin/env python3
"""
Fix expected_article_count in rephrased files by recalculating from original data.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.rephrasing.processors.article_filter import ArticleFilter
from src.core.models.article import ScrapedArticle

def calculate_expected_count(source: str, date_str: str, priority_threshold: float = 0.6) -> int:
    """Calculate expected article count by checking pre-rephrase file first, then re-filtering original data."""
    try:
        # First, try to get count from pre-rephrase file (most accurate)
        pre_rephrase_file = Path(f"data/processed/pre-rephrase/{source}/2025/{date_str}.json")
        if pre_rephrase_file.exists():
            with open(pre_rephrase_file, 'r', encoding='utf-8') as f:
                pre_rephrase_data = json.load(f)
            count = len(pre_rephrase_data.get('articles', []))
            print(f"  📋 Using pre-rephrase file count: {count}")
            return count

        # Fallback: calculate from raw data
        print(f"  📊 No pre-rephrase file, calculating from raw data...")
        raw_file = Path(f"data/raw/{source}/2025/{date_str}.json")
        if not raw_file.exists():
            print(f"  ⚠️  Raw file not found: {raw_file}")
            return 0

        # Load and filter articles
        with open(raw_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)

        articles = [ScrapedArticle.from_dict(a) for a in raw_data['articles']]

        # Apply filtering
        filter_obj = ArticleFilter()
        filtered_articles = filter_obj.filter_articles(articles)

        # Apply priority threshold
        priority_articles = [
            article for article in filtered_articles
            if filter_obj.calculate_priority_score(article) >= priority_threshold
        ]

        return len(priority_articles)

    except Exception as e:
        print(f"  ❌ Error calculating expected count: {e}")
        return 0

def fix_file(file_path: Path):
    """Fix expected_article_count in a rephrased file."""
    print(f"Fixing {file_path}...")
    
    try:
        # Load existing data
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract source and date
        source = data.get('source', 'macaodaily')
        target_date = data.get('target_date', '')
        
        if not target_date:
            print(f"  ⚠️  No target_date found")
            return
        
        # Calculate correct expected count
        expected_count = calculate_expected_count(source, target_date)
        
        if expected_count == 0:
            print(f"  ⚠️  Could not calculate expected count")
            return
        
        # Get current counts
        actual_count = len(data.get('articles', []))
        current_expected = data.get('expected_article_count', 0)
        
        print(f"  📊 Current: {actual_count} articles, expected: {current_expected}")
        print(f"  📊 Correct expected count: {expected_count}")
        
        # Update if different
        if current_expected != expected_count:
            data['expected_article_count'] = expected_count
            data['processed_at'] = datetime.now().isoformat()
            
            # Also update job history status if needed
            job_history = data.get('job_history', [])
            if job_history:
                last_job = job_history[-1]
                if actual_count < expected_count:
                    # Mark as incomplete
                    last_job['status'] = 'incomplete'
                    completion_pct = (actual_count / expected_count) * 100
                    print(f"  🔄 Marked as incomplete ({completion_pct:.1f}% done)")
                else:
                    # Mark as completed
                    last_job['status'] = 'completed'
                    print(f"  ✅ Marked as completed")
            
            # Save updated file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"  ✅ Fixed successfully")
        else:
            print(f"  ✅ Already correct")
            
    except Exception as e:
        print(f"  ❌ Error: {e}")

def main():
    """Fix all rephrased files."""
    rephrased_dir = Path("data/processed/rephrased")
    
    if not rephrased_dir.exists():
        print("❌ Rephrased directory not found")
        return
    
    # Find all JSON files
    json_files = list(rephrased_dir.rglob("*.json"))
    
    if not json_files:
        print("❌ No JSON files found")
        return
    
    print(f"🔍 Found {len(json_files)} files to fix")
    
    for file_path in json_files:
        fix_file(file_path)
    
    print("🎉 Fix completed!")

if __name__ == "__main__":
    main()
