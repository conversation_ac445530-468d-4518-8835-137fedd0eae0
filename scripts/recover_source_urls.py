#!/usr/bin/env python3
"""
Recover missing source_url in rephrased articles by matching with original data.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.article import ScrapedArticle, generate_article_id

def recover_source_urls_for_file(rephrased_file: Path):
    """Recover source URLs for a single rephrased file."""
    print(f"Processing {rephrased_file}...")
    
    try:
        # Load rephrased data
        with open(rephrased_file, 'r', encoding='utf-8') as f:
            rephrased_data = json.load(f)
        
        # Extract source and date info
        source = rephrased_data.get('source', 'macaodaily')
        target_date = rephrased_data.get('target_date', '')
        
        if not target_date:
            print(f"  ⚠️  No target_date found")
            return
        
        # Find corresponding raw data file
        raw_file = Path(f"data/raw/{source}/2025/{target_date}.json")
        if not raw_file.exists():
            print(f"  ⚠️  Raw file not found: {raw_file}")
            return
        
        # Load raw data
        with open(raw_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
        
        # Create mapping of article ID to original URL and metadata
        url_mapping = {}
        content_length_mapping = {}  # Map content length to article info

        for raw_article_data in raw_data.get('articles', []):
            # Create ScrapedArticle to get consistent ID
            article = ScrapedArticle.from_dict(raw_article_data)
            url = article.source_url or article.original_url
            url_mapping[article.id] = url

            # Create content length mapping for fallback matching
            content_length = len(article.content)
            if content_length not in content_length_mapping:
                content_length_mapping[content_length] = []
            content_length_mapping[content_length].append({
                'id': article.id,
                'url': url,
                'title': article.title
            })
        
        print(f"  📊 Created mapping for {len(url_mapping)} articles")
        
        # Update rephrased articles
        updated_count = 0
        articles = rephrased_data.get('articles', [])
        
        for article in articles:
            original_id = article.get('original_article_id')
            current_url = article.get('source_url', '')
            
            # Skip if already has URL
            if current_url and current_url != "":
                continue
            
            # Try to find URL by original_article_id
            new_url = None
            matched_id = None

            if original_id and original_id in url_mapping:
                new_url = url_mapping[original_id]
                matched_id = original_id
            else:
                # Fallback: try to match by original content length
                original_content_length = article.get('processing_metadata', {}).get('original_content_length')
                if original_content_length and original_content_length in content_length_mapping:
                    candidates = content_length_mapping[original_content_length]
                    if len(candidates) == 1:
                        # Unique match by content length
                        candidate = candidates[0]
                        new_url = candidate['url']
                        matched_id = candidate['id']
                        print(f"    🔍 Matched by content length ({original_content_length}): {candidate['title'][:30]}...")
                    else:
                        print(f"    ⚠️  Multiple articles with same content length ({original_content_length}), skipping")
            
            if new_url:
                article['source_url'] = new_url
                # Also update the original_article_id if it was missing
                if not article.get('original_article_id') and matched_id:
                    article['original_article_id'] = matched_id
                updated_count += 1
                print(f"  ✅ Updated article: {article.get('title', 'Unknown')[:50]}...")
            else:
                print(f"  ❌ Could not find URL for: {article.get('title', 'Unknown')[:50]}...")
        
        if updated_count > 0:
            # Update metadata
            rephrased_data['processed_at'] = datetime.now().isoformat()
            
            # Save updated file
            with open(rephrased_file, 'w', encoding='utf-8') as f:
                json.dump(rephrased_data, f, indent=2, ensure_ascii=False)
            
            print(f"  ✅ Updated {updated_count} articles with source URLs")
        else:
            print(f"  ℹ️  No articles needed URL updates")
            
    except Exception as e:
        print(f"  ❌ Error: {e}")

def main():
    """Recover source URLs for all rephrased files."""
    rephrased_dir = Path("data/processed/rephrased")
    
    if not rephrased_dir.exists():
        print("❌ Rephrased directory not found")
        return
    
    # Find all JSON files
    json_files = list(rephrased_dir.rglob("*.json"))
    
    if not json_files:
        print("❌ No JSON files found")
        return
    
    print(f"🔍 Found {len(json_files)} files to process")
    
    for file_path in json_files:
        recover_source_urls_for_file(file_path)
    
    print("🎉 Recovery completed!")

if __name__ == "__main__":
    main()
