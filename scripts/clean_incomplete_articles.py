#!/usr/bin/env python3
"""
Clean incomplete articles (missing original_article_id or source_url) from rephrased files.
This allows the rephrase process to re-process these articles with proper IDs and URLs.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

def clean_file(file_path: Path):
    """Remove articles with missing original_article_id or source_url."""
    print(f"🧹 Cleaning {file_path}...")
    
    try:
        # Load data
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        original_count = len(data.get('articles', []))
        
        # Filter out articles with missing data
        clean_articles = []
        removed_articles = []
        
        for article in data.get('articles', []):
            has_id = article.get('original_article_id') is not None
            has_url = article.get('source_url', '') != ''
            
            if has_id and has_url:
                clean_articles.append(article)
            else:
                removed_articles.append({
                    'title': article.get('title', 'Unknown')[:50],
                    'missing_id': not has_id,
                    'missing_url': not has_url
                })
        
        # Update data
        data['articles'] = clean_articles
        data['article_count'] = len(clean_articles)
        data['processed_at'] = datetime.now().isoformat()
        
        # Update job history to reflect the cleaning
        if 'job_history' in data and data['job_history']:
            # Update the last job's successful count
            last_job = data['job_history'][-1]
            last_job['successful_articles'] = len(clean_articles)
            last_job['failed_articles'] = original_count - len(clean_articles)
        
        # Save cleaned data
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"  📊 Original articles: {original_count}")
        print(f"  📊 Clean articles: {len(clean_articles)}")
        print(f"  📊 Removed articles: {len(removed_articles)}")
        
        if removed_articles:
            print(f"  🗑️  Removed articles:")
            for i, article in enumerate(removed_articles[:5]):  # Show first 5
                missing_info = []
                if article['missing_id']:
                    missing_info.append('ID')
                if article['missing_url']:
                    missing_info.append('URL')
                print(f"    {i+1}. {article['title']}... (missing: {', '.join(missing_info)})")
            
            if len(removed_articles) > 5:
                print(f"    ... and {len(removed_articles) - 5} more")
        
        print(f"  ✅ Cleaning completed")
        return len(removed_articles)
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return 0

def main():
    """Clean incomplete articles from specified files."""
    files_to_clean = [
        Path("data/processed/rephrased/macaodaily/2025/20250625.json"),
        Path("data/processed/rephrased/macaodaily/2025/20250626.json")
    ]
    
    print("🚀 Starting cleanup of incomplete articles...")
    print("This will remove articles missing original_article_id or source_url")
    print("so they can be re-processed with proper data.\n")
    
    total_removed = 0
    
    for file_path in files_to_clean:
        if file_path.exists():
            removed = clean_file(file_path)
            total_removed += removed
            print()
        else:
            print(f"❌ File not found: {file_path}")
    
    print(f"🎉 Cleanup completed!")
    print(f"📊 Total articles removed: {total_removed}")
    
    if total_removed > 0:
        print("\n📋 Next steps:")
        print("1. Run the rephrase process again to fill in the missing articles")
        print("2. The system will now process only the missing articles with proper IDs and URLs")
        print("\nExample command:")
        print("python -m cli rephrase continuous --start-date 2025-06-25 --end-date 2025-06-26 --batch-size 8")

if __name__ == "__main__":
    main()
