#!/usr/bin/env python3
"""
Test script to verify meta data extraction from real rephrased files.
"""

import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.rephrased_article import RephrasedArticle
from src.api.services.data_transformer import DataTransformer

def test_real_file_meta_extraction():
    """Test meta data extraction from the actual 20250713.json file."""
    print("🧪 Testing meta data extraction from real file...")
    
    # Use the specific file mentioned by the user
    file_path = Path("data/processed/rephrased/macaodaily/2025/20250713.json")
    
    if not file_path.exists():
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Load the file
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles_data = data.get('articles', [])
        if not articles_data:
            print("❌ No articles found in file")
            return False
        
        print(f"📁 Loaded {len(articles_data)} articles from {file_path}")
        
        # Test first article
        first_article_data = articles_data[0]
        article_id = first_article_data.get('id', 'unknown')
        
        print(f"\n🔍 Testing article: {article_id}")
        print(f"📝 Title: {first_article_data.get('title', 'No title')[:50]}...")
        
        # Check raw meta data in JSON
        raw_meta = first_article_data.get('meta', {})
        print(f"📋 Raw meta in JSON: {raw_meta}")
        
        # Create RephrasedArticle from data
        article = RephrasedArticle.from_dict(first_article_data)
        
        print(f"✅ Article created successfully")
        print(f"📋 Extracted meta_title: '{article.meta_title}'")
        print(f"📄 Extracted meta_description: '{article.meta_description}'")
        
        # Test data transformer
        transformer = DataTransformer()
        post_schema = transformer.transform_article(article)
        
        print(f"\n🔄 Data transformation completed")
        print(f"📋 Transformed meta: {post_schema.meta}")
        
        # Test JSON serialization
        json_data = post_schema.model_dump()
        api_meta = json_data.get('meta', {})
        
        print(f"\n📤 API JSON meta object:")
        print(json.dumps(api_meta, ensure_ascii=False, indent=2))
        
        # Verify meta data is present and correct
        if api_meta and api_meta.get('title') and api_meta.get('description'):
            print("✅ Meta data successfully extracted and transformed for API")
            
            # Compare with original
            original_meta = raw_meta
            if (api_meta.get('title') == original_meta.get('title') and 
                api_meta.get('description') == original_meta.get('description')):
                print("✅ Meta data matches original JSON format")
                return True
            else:
                print("❌ Meta data doesn't match original")
                print(f"   Original title: '{original_meta.get('title', '')}'")
                print(f"   API title: '{api_meta.get('title', '')}'")
                print(f"   Original desc: '{original_meta.get('description', '')[:50]}...'")
                print(f"   API desc: '{api_meta.get('description', '')[:50]}...'")
                return False
        else:
            print("❌ Meta data missing in API transformation")
            return False
        
    except Exception as e:
        print(f"❌ Error testing file: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_articles():
    """Test meta data extraction from multiple articles."""
    print("\n🧪 Testing multiple articles...")
    
    file_path = Path("data/processed/rephrased/macaodaily/2025/20250713.json")
    
    if not file_path.exists():
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles_data = data.get('articles', [])
        test_count = min(5, len(articles_data))  # Test first 5 articles
        
        transformer = DataTransformer()
        success_count = 0
        
        for i, article_data in enumerate(articles_data[:test_count]):
            article_id = article_data.get('id', f'article-{i}')
            
            try:
                # Create article and transform
                article = RephrasedArticle.from_dict(article_data)
                post_schema = transformer.transform_article(article)
                json_data = post_schema.model_dump()
                
                # Check meta data
                api_meta = json_data.get('meta', {})
                if api_meta and api_meta.get('title') and api_meta.get('description'):
                    success_count += 1
                    print(f"✅ Article {i+1}: Meta data OK")
                else:
                    print(f"❌ Article {i+1}: Meta data missing")
                    
            except Exception as e:
                print(f"❌ Article {i+1}: Error - {e}")
        
        print(f"\n📊 Results: {success_count}/{test_count} articles have valid meta data")
        return success_count == test_count
        
    except Exception as e:
        print(f"❌ Error testing multiple articles: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing meta data extraction from real rephrased files")
    print("=" * 60)
    
    tests = [
        test_real_file_meta_extraction,
        test_multiple_articles
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Meta data is properly extracted and included in API requests.")
        print("\n🎉 The meta data issue has been resolved!")
        print("   • Real rephrased files contain meta objects")
        print("   • RephrasedArticle model correctly extracts meta data")
        print("   • Data transformer includes meta in API requests")
        print("   • JSON serialization preserves meta structure")
        return 0
    else:
        print("❌ Some tests failed. Meta data extraction may have issues.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
