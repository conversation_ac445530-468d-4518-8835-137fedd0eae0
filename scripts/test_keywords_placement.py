#!/usr/bin/env python3
"""
Test script to verify that keywords are placed at the correct level in API requests.
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.rephrased_article import RephrasedArticle
from src.api.services.data_transformer import DataTransformer

def test_keywords_placement():
    """Test that keywords are placed at the top level, not inside extra."""
    print("🧪 Testing keywords placement in API requests...")
    
    # Create a sample rephrased article with keywords
    article = RephrasedArticle(
        id="test-keywords-123",
        title="澳門政府推出新經濟政策支持中小企業發展",
        slug="macau-government-new-economic-policy-sme-support",
        content="# 澳門政府推出新經濟政策\n\n澳門特別行政區政府今日宣布推出一系列新的經濟政策措施...",
        summary="澳門政府宣布新經濟政策，支持中小企業發展，促進經濟多元化。",
        original_article_id="original-123",
        source_url="https://example.com/article",
        source_site_name="澳門日報",
        ai_model_used="gemini-2.5-flash",
        processing_timestamp=datetime.now(),
        processing_metadata={
            "original_title": "政府推新政策",
            "original_content_length": 500,
            "rephrased_content_length": 600,
            "batch_size": 8,
            "batch_index": 1
        },
        categories=["經濟財經", "政府政策"],
        tags=["A01", "澳聞"],
        keywords=["澳門", "經濟政策", "中小企業", "政府支持", "多元化發展"],  # Test keywords
        priority_score=0.8,
        quality_score=0.9,
        meta_title="澳門新經濟政策 | 中小企業支持措施 2025",
        meta_description="澳門政府推出全新經濟政策，重點支持中小企業發展和創新，為澳門經濟注入新活力，促進多元化發展。",
        images=[]
    )
    
    # Transform article for API
    transformer = DataTransformer()
    post_schema = transformer.transform_article(article)
    
    print("✅ Article transformation successful")
    
    # Convert to dict for inspection
    post_dict = post_schema.model_dump()
    
    print(f"📝 Title: {post_dict.get('title', 'MISSING')}")
    print(f"📂 Categories: {post_dict.get('categories', 'MISSING')}")
    
    # Check keywords at top level
    if 'keywords' in post_dict:
        keywords = post_dict['keywords']
        print(f"✅ Keywords found at top level: {keywords}")
        
        if isinstance(keywords, list) and len(keywords) > 0:
            print(f"✅ Keywords is a list with {len(keywords)} items")
            print(f"   🏷️  Keywords: {', '.join(keywords)}")
        else:
            print("❌ Keywords is not a proper list or is empty")
            return False
    else:
        print("❌ Keywords not found at top level")
        return False
    
    # Check that keywords are NOT in extra
    if 'extra' in post_dict and post_dict['extra']:
        extra = post_dict['extra']
        if 'keywords' in extra:
            print("❌ Keywords incorrectly found in extra object")
            print(f"   🚫 Extra keywords: {extra['keywords']}")
            return False
        else:
            print("✅ Keywords correctly NOT in extra object")
    else:
        print("✅ No extra object or empty extra object")
    
    # Verify other fields are still correct
    expected_fields = ['title', 'slug', 'content', 'summary', 'categories', 'keywords', 'meta']
    missing_fields = [field for field in expected_fields if field not in post_dict]
    
    if missing_fields:
        print(f"❌ Missing expected fields: {missing_fields}")
        return False
    else:
        print("✅ All expected fields present")
    
    return True

def test_json_serialization():
    """Test that the API request with keywords can be properly serialized."""
    print("\n🧪 Testing JSON serialization with keywords...")
    
    # Create article with keywords
    article = RephrasedArticle(
        id="test-json-456",
        title="測試關鍵詞序列化",
        slug="test-keywords-serialization",
        content="測試內容",
        summary="測試摘要",
        original_article_id="original-456",
        source_url=None,  # Avoid HttpUrl serialization issue
        source_site_name="測試網站",
        ai_model_used="gemini-2.5-flash",
        processing_timestamp=datetime.now(),
        processing_metadata={},
        categories=["測試分類"],
        tags=["測試"],
        keywords=["測試", "關鍵詞", "序列化", "API"],
        priority_score=0.7,
        quality_score=0.8,
        meta_title="測試SEO標題",
        meta_description="測試SEO描述",
        images=[]
    )
    
    try:
        # Transform and create request
        transformer = DataTransformer()
        request_schema = transformer.transform_articles_batch([article])
        
        # Serialize to JSON
        json_data = request_schema.model_dump()
        json_str = json.dumps(json_data, ensure_ascii=False, indent=2)
        
        print("✅ JSON serialization successful")
        
        # Parse back and verify structure
        parsed_data = json.loads(json_str)
        
        if 'posts' in parsed_data and len(parsed_data['posts']) > 0:
            first_post = parsed_data['posts'][0]
            
            # Check keywords at top level
            if 'keywords' in first_post:
                keywords = first_post['keywords']
                print(f"✅ Keywords preserved in JSON: {keywords}")
                
                # Check structure
                if isinstance(keywords, list) and len(keywords) == 4:
                    print("✅ Keywords structure correct in JSON")
                    return True
                else:
                    print(f"❌ Keywords structure incorrect: {type(keywords)}, length: {len(keywords) if isinstance(keywords, list) else 'N/A'}")
                    return False
            else:
                print("❌ Keywords missing in JSON")
                return False
        else:
            print("❌ No posts in JSON")
            return False
            
    except Exception as e:
        print(f"❌ JSON serialization failed: {e}")
        return False

def test_real_file_keywords():
    """Test keywords extraction from a real rephrased file."""
    print("\n🧪 Testing keywords from real rephrased file...")
    
    # Use the file mentioned by the user
    file_path = Path("data/processed/rephrased/macaodaily/2024/20241228.json")
    
    if not file_path.exists():
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles_data = data.get('articles', [])
        if not articles_data:
            print("❌ No articles found in file")
            return False
        
        print(f"📁 Loaded {len(articles_data)} articles from {file_path}")
        
        # Test first article with keywords
        first_article_data = articles_data[0]
        
        if 'keywords' in first_article_data and first_article_data['keywords']:
            print(f"✅ Found keywords in article: {first_article_data['keywords']}")
            
            # Create RephrasedArticle and transform
            article = RephrasedArticle.from_dict(first_article_data)
            transformer = DataTransformer()
            post_schema = transformer.transform_article(article)
            
            # Check API structure
            post_dict = post_schema.model_dump()
            
            if 'keywords' in post_dict and post_dict['keywords']:
                print(f"✅ Keywords correctly transformed for API: {post_dict['keywords']}")
                
                # Verify not in extra
                if 'extra' in post_dict and post_dict['extra'] and 'keywords' in post_dict['extra']:
                    print("❌ Keywords incorrectly duplicated in extra")
                    return False
                else:
                    print("✅ Keywords only at top level, not in extra")
                    return True
            else:
                print("❌ Keywords missing in API transformation")
                return False
        else:
            print("⚠️  No keywords found in first article, trying next articles...")
            
            # Try to find an article with keywords
            for i, article_data in enumerate(articles_data[:5]):
                if 'keywords' in article_data and article_data['keywords']:
                    print(f"✅ Found keywords in article {i+1}: {article_data['keywords']}")
                    return True
            
            print("❌ No articles with keywords found in first 5 articles")
            return False
        
    except Exception as e:
        print(f"❌ Error testing real file: {e}")
        return False

def main():
    """Run all keywords placement tests."""
    print("🚀 Testing keywords placement in API requests")
    print("=" * 60)
    
    tests = [
        test_keywords_placement,
        test_json_serialization,
        test_real_file_keywords
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Keywords are correctly placed at top level.")
        print("\n📋 Summary:")
        print("   • Keywords are at the same level as categories, meta, etc.")
        print("   • Keywords are NOT inside the extra object")
        print("   • JSON serialization preserves keywords structure")
        print("   • Real file data transforms correctly")
        return 0
    else:
        print("❌ Some tests failed. Keywords placement may be incorrect.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
