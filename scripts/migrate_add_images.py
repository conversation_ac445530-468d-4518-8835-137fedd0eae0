#!/usr/bin/env python3
"""
Migration script to add images from original articles to existing rephrased files.
"""

import sys
import json
from pathlib import Path
from typing import Dict, List, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.scraping.utils.storage import load_articles

def find_matching_raw_file(rephrased_file: Path) -> Path:
    """Find the corresponding raw file for a rephrased file."""
    # Extract date from rephrased file name (e.g., 20250713.json)
    date_str = rephrased_file.stem  # e.g., "20250713"
    
    # Construct raw file path
    year = date_str[:4]  # e.g., "2025"
    raw_file = Path(f"data/raw/macaodaily/{year}/{date_str}.json")
    
    return raw_file

def migrate_images_for_file(rephrased_file: Path, dry_run: bool = True) -> bool:
    """Migrate images from raw file to rephrased file."""
    print(f"\n🔄 Processing: {rephrased_file.name}")
    
    # Find corresponding raw file
    raw_file = find_matching_raw_file(rephrased_file)
    
    if not raw_file.exists():
        print(f"   ❌ Raw file not found: {raw_file}")
        return False
    
    try:
        # Load original articles
        original_articles = load_articles(str(raw_file))
        print(f"   📁 Loaded {len(original_articles)} original articles")
        
        # Create lookup by URL for matching
        original_by_url = {}
        for article in original_articles:
            if hasattr(article, 'source_url') and article.source_url:
                original_by_url[str(article.source_url)] = article
        
        # Load rephrased articles
        with open(rephrased_file, 'r', encoding='utf-8') as f:
            rephrased_data = json.load(f)
        
        rephrased_articles = rephrased_data.get('articles', [])
        print(f"   📰 Loaded {len(rephrased_articles)} rephrased articles")
        
        # Track changes
        articles_updated = 0
        images_added = 0
        
        for rephrased_article in rephrased_articles:
            source_url = rephrased_article.get('source_url', '')
            
            # Check if article already has images
            existing_images = rephrased_article.get('images', [])
            if existing_images:
                continue  # Skip if already has images
            
            # Find matching original article
            if source_url in original_by_url:
                original_article = original_by_url[source_url]
                
                # Get first image from original
                if hasattr(original_article, 'images') and original_article.images:
                    first_image = original_article.images[0]
                    
                    # Convert to dict format
                    image_dict = {
                        'src': first_image.src,
                        'description': first_image.description,
                        'alt_text': first_image.alt_text,
                        'width': first_image.width,
                        'height': first_image.height
                    }
                    
                    # Add to rephrased article
                    rephrased_article['images'] = [image_dict]
                    articles_updated += 1
                    images_added += 1
                    
                    print(f"   ✅ Added image to: {rephrased_article.get('title', 'Unknown')[:50]}...")
        
        print(f"   📊 Summary: {articles_updated} articles updated, {images_added} images added")
        
        if articles_updated > 0 and not dry_run:
            # Write back to file
            with open(rephrased_file, 'w', encoding='utf-8') as f:
                json.dump(rephrased_data, f, ensure_ascii=False, indent=2)
            print(f"   💾 File updated successfully")
        elif articles_updated > 0:
            print(f"   🔍 [DRY RUN] Would update file with {articles_updated} changes")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error processing file: {e}")
        return False

def migrate_all_images(dry_run: bool = True, limit_files: int = None):
    """Migrate images for all rephrased files."""
    print(f"🚀 {'[DRY RUN] ' if dry_run else ''}Migrating images to rephrased files")
    print("=" * 60)
    
    # Find all rephrased files
    rephrased_path = Path("data/processed/rephrased/macaodaily")
    
    if not rephrased_path.exists():
        print(f"❌ Rephrased data path not found: {rephrased_path}")
        return False
    
    # Get all JSON files
    json_files = []
    for year_dir in rephrased_path.glob("*"):
        if year_dir.is_dir():
            json_files.extend(year_dir.glob("*.json"))
    
    if not json_files:
        print("❌ No rephrased files found")
        return False
    
    # Sort by date (newest first)
    json_files.sort(reverse=True)
    
    if limit_files:
        json_files = json_files[:limit_files]
        print(f"📋 Processing {len(json_files)} most recent files (limited)")
    else:
        print(f"📋 Found {len(json_files)} rephrased files to process")
    
    # Process files
    success_count = 0
    total_articles_updated = 0
    
    for file_path in json_files:
        try:
            if migrate_images_for_file(file_path, dry_run):
                success_count += 1
        except Exception as e:
            print(f"❌ Error processing {file_path.name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Migration Results:")
    print(f"   ✅ Successfully processed: {success_count}/{len(json_files)} files")
    
    if dry_run:
        print(f"\n💡 To actually perform the migration, run:")
        print(f"   python scripts/migrate_add_images.py --apply")
        print(f"   python scripts/migrate_add_images.py --apply --limit 10  # For recent 10 files")
    else:
        print(f"\n🎉 Image migration completed!")
        print(f"   All rephrased articles now include first image from original articles")
    
    return success_count == len(json_files)

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Migrate images from original to rephrased articles')
    parser.add_argument('--apply', action='store_true', help='Actually apply changes (not just dry run)')
    parser.add_argument('--limit', type=int, help='Limit to N most recent files')
    args = parser.parse_args()
    
    success = migrate_all_images(dry_run=not args.apply, limit_files=args.limit)
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
