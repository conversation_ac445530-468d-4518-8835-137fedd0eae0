#!/usr/bin/env python3
"""
Test script to verify that the AI is generating meta data in responses.
"""

import sys
import asyncio
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.article import ScrapedArticle
from src.rephrasing.ai_clients.gemini_client import GeminiClient
from src.rephrasing.models.response_models import RephrasedContent

async def test_ai_meta_generation():
    """Test that the AI generates meta data in its responses."""
    print("🧪 Testing AI meta data generation...")
    
    # Create a sample article
    sample_article = ScrapedArticle(
        id="test-123",
        title="澳門政府推出新經濟政策支持中小企業發展",
        content="澳門特別行政區政府今日宣布推出一系列新的經濟政策措施，旨在支持本地中小企業發展，促進經濟多元化。新政策包括稅收優惠、資金支持和技術培訓等多項措施。政府表示，這些措施將有助於提升澳門經濟的競爭力，為市民創造更多就業機會。",
        source_url="https://example.com/article",
        source_site_name="澳門日報",
        tags=["A01", "澳聞"],
        created_at="2025-07-18T10:00:00Z"
    )
    
    try:
        # Test with Gemini client (if API key is available)
        client = GeminiClient()
        
        print("🤖 Testing single article rephrasing...")
        
        # Test single article
        rephrased_article = await client.rephrase_article(sample_article)
        
        print("✅ Article rephrased successfully")
        print(f"📝 Title: {rephrased_article.title}")
        print(f"🔗 Slug: {rephrased_article.slug}")
        print(f"📂 Categories: {rephrased_article.categories}")
        
        # Check meta data
        if rephrased_article.meta_title and rephrased_article.meta_description:
            print("✅ Meta data generated by AI:")
            print(f"   📋 Meta Title: {rephrased_article.meta_title}")
            print(f"   📄 Meta Description: {rephrased_article.meta_description}")
            
            # Check lengths
            title_len = len(rephrased_article.meta_title)
            desc_len = len(rephrased_article.meta_description)
            
            print(f"📏 Meta title length: {title_len} characters")
            print(f"📏 Meta description length: {desc_len} characters")
            
            return True
        else:
            print("❌ AI did not generate meta data:")
            print(f"   📋 Meta Title: '{rephrased_article.meta_title}'")
            print(f"   📄 Meta Description: '{rephrased_article.meta_description}'")
            return False
        
    except Exception as e:
        error_str = str(e).lower()
        if "api key" in error_str or "unauthorized" in error_str:
            print("⚠️  Skipping AI test - API key not configured")
            return True  # Don't fail the test for missing API key
        else:
            print(f"❌ Error testing AI generation: {e}")
            return False

def test_response_model_structure():
    """Test that the response model includes meta fields."""
    print("\n🧪 Testing response model structure...")
    
    try:
        # Check RephrasedContent model schema
        schema = RephrasedContent.model_json_schema()
        properties = schema.get('properties', {})
        
        print("✅ RephrasedContent model loaded")
        
        # Check for meta fields
        has_meta_title = 'meta_title' in properties
        has_meta_description = 'meta_description' in properties
        
        print(f"📋 Has meta_title field: {has_meta_title}")
        print(f"📄 Has meta_description field: {has_meta_description}")
        
        if has_meta_title and has_meta_description:
            print("✅ Response model includes meta fields")
            
            # Check field descriptions
            meta_title_desc = properties['meta_title'].get('description', '')
            meta_desc_desc = properties['meta_description'].get('description', '')
            
            print(f"📋 Meta title description: {meta_title_desc}")
            print(f"📄 Meta description description: {meta_desc_desc}")
            
            return True
        else:
            print("❌ Response model missing meta fields")
            return False
        
    except Exception as e:
        print(f"❌ Error testing response model: {e}")
        return False

def test_prompt_includes_meta_instructions():
    """Test that prompts include meta data instructions."""
    print("\n🧪 Testing prompt meta instructions...")
    
    try:
        from src.rephrasing.templates.prompts import NewsRephrasingPrompts
        
        # Check main rephrase prompt
        rephrase_prompt = NewsRephrasingPrompts.REPHRASE_ARTICLE.template
        
        print("✅ Rephrase prompt loaded")
        
        # Check for meta-related instructions
        has_meta_title = 'meta_title' in rephrase_prompt.lower()
        has_meta_description = 'meta_description' in rephrase_prompt.lower()
        has_seo_instructions = 'seo' in rephrase_prompt.lower()
        
        print(f"📋 Mentions meta_title: {has_meta_title}")
        print(f"📄 Mentions meta_description: {has_meta_description}")
        print(f"🔍 Mentions SEO: {has_seo_instructions}")
        
        if has_meta_title and has_meta_description:
            print("✅ Prompt includes meta data instructions")
            return True
        else:
            print("❌ Prompt missing meta data instructions")
            return False
        
    except Exception as e:
        print(f"❌ Error testing prompt: {e}")
        return False

async def main():
    """Run all meta generation tests."""
    print("🚀 Testing AI meta data generation")
    print("=" * 50)
    
    tests = [
        test_response_model_structure,
        test_prompt_includes_meta_instructions,
        test_ai_meta_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! AI should generate meta data.")
        print("\n💡 If existing files lack meta data, they were created before")
        print("   the meta generation was properly configured.")
        return 0
    else:
        print("❌ Some tests failed. Meta data generation may not work.")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
