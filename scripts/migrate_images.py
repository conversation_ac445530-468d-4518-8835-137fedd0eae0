#!/usr/bin/env python3
"""
Migration script to add images from raw articles to existing rephrased articles.

This script copies image data from raw scraped articles to rephrased articles
that were created before the images field was added to the RephrasedArticle model.
"""

import json
import logging
import sys
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.article import ScrapedArticle

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_json_file(file_path: Path) -> Dict[str, Any]:
    """Load JSON file safely."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Failed to load {file_path}: {e}")
        return {}


def save_json_file(file_path: Path, data: Dict[str, Any]) -> bool:
    """Save JSON file safely."""
    try:
        # Create backup
        backup_path = file_path.with_suffix('.json.backup')
        if file_path.exists():
            file_path.rename(backup_path)
            logger.info(f"Created backup: {backup_path}")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved updated file: {file_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to save {file_path}: {e}")
        return False


def create_image_mapping(raw_articles: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    """Create mapping from article ID to first image only."""
    image_mapping = {}

    for article_data in raw_articles:
        # Create ScrapedArticle to generate consistent ID
        article = ScrapedArticle.from_dict(article_data)
        images = article_data.get('images', [])

        if article.id and images:
            # Only take the first image as requested
            image_mapping[article.id] = images[0]

    return image_mapping


def migrate_images_for_date(date_str: str) -> bool:
    """Migrate images for a specific date."""
    logger.info(f"Migrating images for date: {date_str}")
    
    # File paths
    year = date_str[:4]
    raw_file = Path(f"data/raw/macaodaily/{year}/{date_str}.json")
    rephrased_file = Path(f"data/processed/rephrased/macaodaily/{year}/{date_str}.json")
    
    # Check if files exist
    if not raw_file.exists():
        logger.warning(f"Raw file not found: {raw_file}")
        return False
    
    if not rephrased_file.exists():
        logger.warning(f"Rephrased file not found: {rephrased_file}")
        return False
    
    # Load data
    raw_data = load_json_file(raw_file)
    rephrased_data = load_json_file(rephrased_file)
    
    if not raw_data or not rephrased_data:
        return False
    
    # Create image mapping
    image_mapping = create_image_mapping(raw_data.get('articles', []))
    logger.info(f"Found images for {len(image_mapping)} articles in raw data")
    
    # Update rephrased articles
    updated_count = 0
    rephrased_articles = rephrased_data.get('articles', [])
    
    for article in rephrased_articles:
        original_id = article.get('original_article_id')
        
        if original_id and original_id in image_mapping:
            # Add images if not already present
            if not article.get('images'):
                # Wrap single image in a list as expected by the model
                article['images'] = [image_mapping[original_id]]
                updated_count += 1
                logger.debug(f"Added image to article: {article.get('title', 'Unknown')[:50]}...")
    
    logger.info(f"Updated {updated_count} articles with images")
    
    # Save updated file if changes were made
    if updated_count > 0:
        return save_json_file(rephrased_file, rephrased_data)
    else:
        logger.info("No updates needed")
        return True


def main():
    """Main migration function."""
    logger.info("Starting image migration for rephrased articles")
    
    # Get all rephrased files
    rephrased_dir = Path("data/processed/rephrased/macaodaily")
    
    if not rephrased_dir.exists():
        logger.error(f"Rephrased directory not found: {rephrased_dir}")
        return
    
    # Find all rephrased files
    rephrased_files = list(rephrased_dir.rglob("*.json"))
    logger.info(f"Found {len(rephrased_files)} rephrased files")
    
    success_count = 0
    
    for file_path in rephrased_files:
        # Extract date from filename
        date_str = file_path.stem
        
        try:
            # Validate date format
            datetime.strptime(date_str, "%Y%m%d")
            
            if migrate_images_for_date(date_str):
                success_count += 1
            
        except ValueError:
            logger.warning(f"Invalid date format in filename: {file_path}")
            continue
        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
            continue
    
    logger.info(f"Migration completed: {success_count}/{len(rephrased_files)} files processed successfully")


if __name__ == "__main__":
    main()
