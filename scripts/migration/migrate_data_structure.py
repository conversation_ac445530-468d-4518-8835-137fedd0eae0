#!/usr/bin/env python3
"""
Migration script to reorganize existing data files into the new hierarchical structure.
"""

import os
import re
import shutil
import json
import sys
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))


def migrate_scraped_data(dry_run: bool = False) -> Dict[str, Any]:
    """
    Migrate existing scraped data to new structure.
    
    Args:
        dry_run: If True, only show what would be done without making changes
    
    Returns:
        Dictionary with migration statistics
    """
    
    stats = {
        'files_processed': 0,
        'files_moved': 0,
        'files_skipped': 0,
        'errors': []
    }
    
    # Source and destination paths
    old_data_dir = Path("data")
    new_data_dir = Path("data/raw")
    
    print(f"Migrating data files from {old_data_dir} to {new_data_dir}")
    print(f"Dry run: {dry_run}")
    print("-" * 50)
    
    # Create new directory structure if not dry run
    if not dry_run:
        new_data_dir.mkdir(parents=True, exist_ok=True)
    
    # Pattern to match existing data files
    file_pattern = re.compile(r'^(\w+)_(\d{4})(\d{2})(\d{2})\.json$')
    
    # Process files in the old data directory
    for file_path in old_data_dir.glob("*.json"):
        stats['files_processed'] += 1
        
        try:
            match = file_pattern.match(file_path.name)
            if not match:
                print(f"⚠ Skipping file with unexpected format: {file_path.name}")
                stats['files_skipped'] += 1
                continue
            
            source, year, month, day = match.groups()

            # Create new directory structure: data/raw/source/year/
            new_dir = new_data_dir / source / year
            # Remove source prefix from filename: macaodaily_20250210.json -> 20250210.json
            new_filename = f"{year}{month}{day}.json"
            new_file_path = new_dir / new_filename
            
            print(f"📁 {file_path.name} -> {new_file_path}")
            
            if not dry_run:
                # Create directory structure
                new_dir.mkdir(parents=True, exist_ok=True)
                
                # Move file
                shutil.move(str(file_path), str(new_file_path))
                stats['files_moved'] += 1
            else:
                stats['files_moved'] += 1
                
        except Exception as e:
            error_msg = f"Error processing {file_path.name}: {e}"
            print(f"✗ {error_msg}")
            stats['errors'].append(error_msg)
    
    return stats


def update_file_references(dry_run: bool = False) -> Dict[str, Any]:
    """
    Update file references in documentation and scripts.
    
    Args:
        dry_run: If True, only show what would be done without making changes
    
    Returns:
        Dictionary with update statistics
    """
    
    stats = {
        'files_updated': 0,
        'references_updated': 0,
        'errors': []
    }
    
    print("\nUpdating file references in documentation...")
    print("-" * 50)
    
    # Files to update and their patterns
    files_to_update = [
        {
            'path': Path('README.md'),
            'patterns': [
                (r'data/macaodaily_\d{8}\.json', 'data/raw/macaodaily/YYYY/MM/macaodaily_YYYYMMDD.json'),
            ]
        },
        {
            'path': Path('docs/project_structure.md'),
            'patterns': [
                (r'data/macaodaily_YYYYMMDD\.json', 'data/raw/macaodaily/YYYY/MM/macaodaily_YYYYMMDD.json'),
            ]
        }
    ]
    
    for file_info in files_to_update:
        file_path = file_info['path']
        
        if not file_path.exists():
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            for old_pattern, new_pattern in file_info['patterns']:
                if re.search(old_pattern, content):
                    content = re.sub(old_pattern, new_pattern, content)
                    stats['references_updated'] += 1
                    print(f"📝 Updated pattern in {file_path}: {old_pattern} -> {new_pattern}")
            
            if content != original_content:
                if not dry_run:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                stats['files_updated'] += 1
                print(f"✓ Updated {file_path}")
            
        except Exception as e:
            error_msg = f"Error updating {file_path}: {e}"
            print(f"✗ {error_msg}")
            stats['errors'].append(error_msg)
    
    return stats


def create_legacy_symlinks(dry_run: bool = False) -> Dict[str, Any]:
    """
    Create symlinks for backward compatibility.
    
    Args:
        dry_run: If True, only show what would be done without making changes
    
    Returns:
        Dictionary with symlink statistics
    """
    
    stats = {
        'symlinks_created': 0,
        'errors': []
    }
    
    print("\nCreating legacy compatibility symlinks...")
    print("-" * 50)
    
    # Create symlink from old data directory to new structure
    old_data_path = Path("data_legacy")
    new_data_path = Path("data/raw")
    
    if old_data_path.exists():
        print(f"⚠ Legacy path {old_data_path} already exists, skipping symlink creation")
        return stats
    
    try:
        if not dry_run:
            old_data_path.symlink_to(new_data_path, target_is_directory=True)
        
        stats['symlinks_created'] += 1
        print(f"🔗 Created symlink: {old_data_path} -> {new_data_path}")
        
    except Exception as e:
        error_msg = f"Error creating symlink: {e}"
        print(f"✗ {error_msg}")
        stats['errors'].append(error_msg)
    
    return stats


def validate_migration() -> Dict[str, Any]:
    """
    Validate the migration by checking file integrity.
    
    Returns:
        Dictionary with validation statistics
    """
    
    stats = {
        'files_validated': 0,
        'files_valid': 0,
        'files_invalid': 0,
        'errors': []
    }
    
    print("\nValidating migrated files...")
    print("-" * 50)
    
    new_data_dir = Path("data/raw")
    
    if not new_data_dir.exists():
        print("⚠ New data directory doesn't exist, skipping validation")
        return stats
    
    for file_path in new_data_dir.rglob("*.json"):
        stats['files_validated'] += 1
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Basic validation
            required_fields = ['scraper_name', 'article_count', 'articles']
            if all(field in data for field in required_fields):
                stats['files_valid'] += 1
                print(f"✓ Valid: {file_path.relative_to(Path.cwd())}")
            else:
                stats['files_invalid'] += 1
                print(f"⚠ Invalid structure: {file_path.relative_to(Path.cwd())}")
                
        except Exception as e:
            stats['files_invalid'] += 1
            error_msg = f"Error validating {file_path}: {e}"
            print(f"✗ {error_msg}")
            stats['errors'].append(error_msg)
    
    return stats


def main():
    """Main migration function."""
    
    import argparse
    
    parser = argparse.ArgumentParser(description='Migrate data structure to new format')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be done without making changes')
    parser.add_argument('--skip-data', action='store_true',
                       help='Skip data file migration')
    parser.add_argument('--skip-refs', action='store_true',
                       help='Skip reference updates')
    parser.add_argument('--skip-symlinks', action='store_true',
                       help='Skip symlink creation')
    parser.add_argument('--validate-only', action='store_true',
                       help='Only validate existing migration')
    
    args = parser.parse_args()
    
    print("Data Structure Migration Tool")
    print("=" * 40)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    total_stats = {
        'data_migration': {},
        'reference_updates': {},
        'symlink_creation': {},
        'validation': {}
    }
    
    if args.validate_only:
        total_stats['validation'] = validate_migration()
    else:
        if not args.skip_data:
            total_stats['data_migration'] = migrate_scraped_data(dry_run=args.dry_run)
        
        if not args.skip_refs:
            total_stats['reference_updates'] = update_file_references(dry_run=args.dry_run)
        
        if not args.skip_symlinks:
            total_stats['symlink_creation'] = create_legacy_symlinks(dry_run=args.dry_run)
        
        # Always validate after migration
        if not args.dry_run:
            total_stats['validation'] = validate_migration()
    
    # Print summary
    print("\nMigration Summary")
    print("=" * 30)
    
    for operation, stats in total_stats.items():
        if not stats:
            continue
            
        print(f"\n{operation.replace('_', ' ').title()}:")
        for key, value in stats.items():
            if key != 'errors':
                print(f"  {key.replace('_', ' ').title()}: {value}")
        
        if stats.get('errors'):
            print(f"  Errors: {len(stats['errors'])}")
            for error in stats['errors']:
                print(f"    - {error}")
    
    print(f"\nMigration {'simulation' if args.dry_run else 'completed'} successfully!")


if __name__ == '__main__':
    main()
