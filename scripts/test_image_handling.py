#!/usr/bin/env python3
"""
Test script to verify image handling in the rephrase process.
"""

import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.scraping.utils.storage import load_articles
from src.core.models.rephrased_article import RephrasedArticle

def test_original_articles_have_images():
    """Test that original scraped articles contain images."""
    print("🧪 Testing original articles for images...")
    
    # Check a recent raw file
    raw_file = Path("data/raw/macaodaily/2025/20250713.json")
    
    if not raw_file.exists():
        print(f"❌ Raw file not found: {raw_file}")
        return False
    
    try:
        # Load articles using the same method as rephrase script
        articles = load_articles(str(raw_file))
        
        print(f"📁 Loaded {len(articles)} articles from {raw_file}")
        
        # Check for images
        articles_with_images = 0
        total_images = 0
        
        for i, article in enumerate(articles[:10]):  # Check first 10 articles
            if hasattr(article, 'images') and article.images:
                articles_with_images += 1
                total_images += len(article.images)
                print(f"✅ Article {i+1}: {len(article.images)} images")
                print(f"   📷 First image: {article.images[0].src}")
                print(f"   📝 Description: {article.images[0].description}")
            else:
                print(f"❌ Article {i+1}: No images")
        
        print(f"\n📊 Summary:")
        print(f"   📰 Articles with images: {articles_with_images}/10")
        print(f"   📷 Total images: {total_images}")
        
        return articles_with_images > 0
        
    except Exception as e:
        print(f"❌ Error loading articles: {e}")
        return False

def test_rephrased_articles_have_images():
    """Test that rephrased articles contain images."""
    print("\n🧪 Testing rephrased articles for images...")
    
    # Check a recent rephrased file
    rephrased_file = Path("data/processed/rephrased/macaodaily/2025/20250713.json")
    
    if not rephrased_file.exists():
        print(f"❌ Rephrased file not found: {rephrased_file}")
        return False
    
    try:
        with open(rephrased_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        articles_data = data.get('articles', [])
        print(f"📁 Loaded {len(articles_data)} rephrased articles from {rephrased_file}")
        
        # Check for images
        articles_with_images = 0
        total_images = 0
        
        for i, article_data in enumerate(articles_data[:10]):  # Check first 10 articles
            images = article_data.get('images', [])
            if images:
                articles_with_images += 1
                total_images += len(images)
                print(f"✅ Article {i+1}: {len(images)} images")
                print(f"   📷 First image: {images[0].get('src', 'NO SRC')}")
                print(f"   📝 Description: {images[0].get('description', 'NO DESC')}")
            else:
                print(f"❌ Article {i+1}: No images")
        
        print(f"\n📊 Summary:")
        print(f"   📰 Articles with images: {articles_with_images}/10")
        print(f"   📷 Total images: {total_images}")
        
        return articles_with_images > 0
        
    except Exception as e:
        print(f"❌ Error loading rephrased articles: {e}")
        return False

def test_image_transfer_logic():
    """Test the image transfer logic used in AI client."""
    print("\n🧪 Testing image transfer logic...")
    
    # Load a raw article with images
    raw_file = Path("data/raw/macaodaily/2025/20250713.json")
    
    if not raw_file.exists():
        print(f"❌ Raw file not found: {raw_file}")
        return False
    
    try:
        articles = load_articles(str(raw_file))
        
        # Find an article with images
        article_with_images = None
        for article in articles:
            if hasattr(article, 'images') and article.images:
                article_with_images = article
                break
        
        if not article_with_images:
            print("❌ No articles with images found")
            return False
        
        print(f"✅ Found article with images: {article_with_images.title[:50]}...")
        print(f"📷 Original images count: {len(article_with_images.images)}")
        
        # Test the logic used in AI client
        first_image_logic = [getattr(article_with_images, 'images', [])[0]] if getattr(article_with_images, 'images', []) else []
        
        print(f"🔧 First image logic result: {len(first_image_logic)} images")
        
        if first_image_logic:
            first_image = first_image_logic[0]
            print(f"✅ First image extracted successfully:")
            print(f"   📷 Source: {first_image.src}")
            print(f"   📝 Description: {first_image.description}")
            print(f"   🏷️  Type: {type(first_image)}")
            
            # Test conversion to dict (for saving)
            if hasattr(first_image, 'to_dict'):
                image_dict = first_image.to_dict()
                print(f"✅ Image converts to dict: {image_dict}")
            elif hasattr(first_image, '__dict__'):
                image_dict = first_image.__dict__
                print(f"✅ Image dict representation: {image_dict}")
            else:
                print(f"❌ Cannot convert image to dict")
                return False
            
            return True
        else:
            print("❌ First image logic failed")
            return False
        
    except Exception as e:
        print(f"❌ Error testing image transfer logic: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rephrased_article_model():
    """Test that RephrasedArticle model handles images correctly."""
    print("\n🧪 Testing RephrasedArticle model with images...")
    
    try:
        from src.core.models.article import ArticleImage
        from datetime import datetime
        
        # Create a sample image
        sample_image = ArticleImage(
            src="https://example.com/image.jpg",
            description="Test image description",
            alt_text="Test alt text",
            width=800,
            height=600
        )
        
        # Create a RephrasedArticle with images
        rephrased_article = RephrasedArticle(
            id="test-123",
            title="Test Article",
            slug="test-article",
            content="Test content",
            summary="Test summary",
            original_article_id="original-123",
            source_url="https://example.com",
            source_site_name="Test Site",
            ai_model_used="gemini-2.5-flash",
            processing_timestamp=datetime.now(),
            processing_metadata={},
            categories=["Test"],
            tags=["test"],
            keywords=["test"],
            priority_score=0.8,
            quality_score=0.9,
            meta_title="Test Meta Title",
            meta_description="Test Meta Description",
            images=[sample_image]  # Include image
        )
        
        print("✅ RephrasedArticle created with images")
        print(f"📷 Images count: {len(rephrased_article.images)}")
        
        # Test to_dict conversion
        article_dict = rephrased_article.to_dict()
        
        if 'images' in article_dict and article_dict['images']:
            print("✅ Images included in to_dict() output")
            print(f"📷 Images in dict: {len(article_dict['images'])}")
            print(f"📝 First image dict: {article_dict['images'][0]}")
            return True
        else:
            print("❌ Images missing in to_dict() output")
            return False
        
    except Exception as e:
        print(f"❌ Error testing RephrasedArticle model: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all image handling tests."""
    print("🚀 Testing image handling in rephrase process")
    print("=" * 60)
    
    tests = [
        test_original_articles_have_images,
        test_rephrased_articles_have_images,
        test_image_transfer_logic,
        test_rephrased_article_model
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Image handling is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Image handling may have issues.")
        print("\n💡 If rephrased articles are missing images, the issue is likely")
        print("   in the AI client image transfer logic or the rephrase process.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
