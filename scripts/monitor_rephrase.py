#!/usr/bin/env python3
"""
Rephrase processing status monitor.

This script provides real-time monitoring of the rephrase processing status,
including quota usage, progress tracking, and file statistics.
"""

import os
import sys
import json
import time
from datetime import datetime, date
from pathlib import Path
from typing import Dict, List, Tuple

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import get_settings
from src.rephrasing.utils.rate_limiter import GeminiRateLimiter


class RephraseMonitor:
    """Monitor rephrase processing status and progress."""
    
    def __init__(self, source: str = "macaodaily"):
        self.source = source
        self.settings = get_settings()
        
    def get_quota_status(self) -> Dict:
        """Get current API quota status."""
        try:
            rate_limiter = GeminiRateLimiter()
            return rate_limiter.get_quota_status()
        except Exception as e:
            return {"error": str(e)}
    
    def scan_data_files(self) -> Dict:
        """Scan and analyze data files."""
        stats = {
            "raw_files": 0,
            "pre_rephrase_files": 0,
            "rephrased_files": 0,
            "date_range": {"start": None, "end": None},
            "years": set(),
            "total_raw_articles": 0,
            "total_rephrased_articles": 0,
            "processing_progress": 0.0
        }
        
        # Scan raw files
        raw_path = self.settings.data.raw_data_path / self.source
        if raw_path.exists():
            raw_files = list(raw_path.rglob("*.json"))
            stats["raw_files"] = len(raw_files)
            
            # Extract dates and count articles
            dates = []
            for file_path in raw_files:
                try:
                    # Extract date from filename (YYYYMMDD.json)
                    date_str = file_path.stem
                    if len(date_str) == 8 and date_str.isdigit():
                        file_date = datetime.strptime(date_str, "%Y%m%d").date()
                        dates.append(file_date)
                        stats["years"].add(file_date.year)
                        
                        # Count articles in file
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            stats["total_raw_articles"] += data.get("article_count", 0)
                            
                except Exception:
                    continue
            
            if dates:
                stats["date_range"]["start"] = min(dates)
                stats["date_range"]["end"] = max(dates)
        
        # Scan pre-rephrase files
        pre_rephrase_path = self.settings.data.processed_data_path / "pre-rephrase" / self.source
        if pre_rephrase_path.exists():
            stats["pre_rephrase_files"] = len(list(pre_rephrase_path.rglob("*.json")))
        
        # Scan rephrased files
        rephrased_path = self.settings.data.processed_data_path / "rephrased" / self.source
        if rephrased_path.exists():
            rephrased_files = list(rephrased_path.rglob("*.json"))
            stats["rephrased_files"] = len(rephrased_files)
            
            # Count rephrased articles
            for file_path in rephrased_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        stats["total_rephrased_articles"] += data.get("article_count", 0)
                except Exception:
                    continue
        
        # Calculate progress
        if stats["raw_files"] > 0:
            stats["processing_progress"] = (stats["rephrased_files"] / stats["raw_files"]) * 100
        
        stats["years"] = sorted(list(stats["years"]))
        
        return stats
    
    def get_recent_logs(self, lines: int = 20) -> List[str]:
        """Get recent log entries."""
        log_file = self.settings.data.log_path / f"rephrase_{datetime.now().strftime('%Y%m%d')}.log"
        
        if not log_file.exists():
            return ["No log file found for today"]
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return [line.strip() for line in all_lines[-lines:]]
        except Exception as e:
            return [f"Error reading log file: {e}"]
    
    def estimate_completion_time(self, stats: Dict, quota_status: Dict) -> Dict:
        """Estimate completion time based on current progress and quota."""
        if stats["raw_files"] == 0 or quota_status.get("error"):
            return {"error": "Cannot estimate completion time"}
        
        remaining_files = stats["raw_files"] - stats["rephrased_files"]
        daily_limit = quota_status.get("daily_limit", 250)
        daily_remaining = quota_status.get("daily_remaining", 0)
        
        # Estimate files per day (assuming average batch processing)
        avg_batches_per_file = 3  # Rough estimate
        files_per_day = daily_limit // avg_batches_per_file
        
        if files_per_day > 0:
            days_remaining = remaining_files / files_per_day
            completion_date = date.today()
            
            # Add days (rough calculation)
            import datetime
            completion_date += datetime.timedelta(days=int(days_remaining))
            
            return {
                "remaining_files": remaining_files,
                "estimated_days": round(days_remaining, 1),
                "estimated_completion": completion_date.strftime("%Y-%m-%d"),
                "files_per_day": files_per_day
            }
        
        return {"error": "Cannot estimate with current quota"}
    
    def display_status(self):
        """Display comprehensive status information."""
        print("=" * 80)
        print(f"🔍 REPHRASE PROCESSING STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # API Quota Status
        print("\n📊 API QUOTA STATUS")
        print("-" * 40)
        quota_status = self.get_quota_status()
        
        if quota_status.get("error"):
            print(f"❌ Error: {quota_status['error']}")
        else:
            print(f"Daily requests: {quota_status['daily_requests']}/{quota_status['daily_limit']}")
            print(f"Daily remaining: {quota_status['daily_remaining']}")
            print(f"Minute requests: {quota_status['minute_requests']}/{quota_status['minute_limit']}")
            print(f"Tokens used today: {quota_status['tokens_used_today']:,}")
            print(f"Can make request: {'✅ Yes' if quota_status['can_make_request'] else '❌ No'}")
            
            if quota_status.get('daily_reset_time'):
                reset_time = datetime.fromisoformat(quota_status['daily_reset_time'])
                print(f"Quota resets: {reset_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # File Statistics
        print("\n📁 FILE STATISTICS")
        print("-" * 40)
        stats = self.scan_data_files()
        
        print(f"Source: {self.source}")
        print(f"Raw files: {stats['raw_files']:,}")
        print(f"Pre-rephrase files: {stats['pre_rephrase_files']:,}")
        print(f"Rephrased files: {stats['rephrased_files']:,}")
        print(f"Processing progress: {stats['processing_progress']:.1f}%")
        
        if stats['date_range']['start'] and stats['date_range']['end']:
            print(f"Date range: {stats['date_range']['start']} to {stats['date_range']['end']}")
        
        if stats['years']:
            print(f"Years available: {', '.join(map(str, stats['years']))}")
        
        print(f"Total raw articles: {stats['total_raw_articles']:,}")
        print(f"Total rephrased articles: {stats['total_rephrased_articles']:,}")
        
        # Completion Estimate
        print("\n⏱️  COMPLETION ESTIMATE")
        print("-" * 40)
        estimate = self.estimate_completion_time(stats, quota_status)
        
        if estimate.get("error"):
            print(f"❌ {estimate['error']}")
        else:
            print(f"Remaining files: {estimate['remaining_files']:,}")
            print(f"Estimated days: {estimate['estimated_days']}")
            print(f"Estimated completion: {estimate['estimated_completion']}")
            print(f"Files per day (estimated): {estimate['files_per_day']}")
        
        # Recent Logs
        print("\n📋 RECENT LOG ENTRIES")
        print("-" * 40)
        recent_logs = self.get_recent_logs(10)
        
        for log_line in recent_logs:
            # Highlight important log levels
            if "ERROR" in log_line:
                print(f"❌ {log_line}")
            elif "WARNING" in log_line:
                print(f"⚠️  {log_line}")
            elif "INFO" in log_line and any(keyword in log_line for keyword in ["Progress:", "Successfully", "completed"]):
                print(f"✅ {log_line}")
            else:
                print(f"   {log_line}")
        
        print("\n" + "=" * 80)
    
    def watch_mode(self, interval: int = 30):
        """Continuous monitoring mode."""
        print(f"🔄 Starting watch mode (refresh every {interval} seconds)")
        print("Press Ctrl+C to exit")
        
        try:
            while True:
                # Clear screen (works on most terminals)
                os.system('clear' if os.name == 'posix' else 'cls')
                
                self.display_status()
                print(f"\n⏳ Next refresh in {interval} seconds... (Ctrl+C to exit)")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped")


def main():
    """Main function with command line argument parsing."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Monitor rephrase processing status and progress",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Show current status
  python monitor_rephrase.py
  
  # Continuous monitoring (refresh every 30 seconds)
  python monitor_rephrase.py --watch
  
  # Custom refresh interval
  python monitor_rephrase.py --watch --interval 60
  
  # Monitor specific source
  python monitor_rephrase.py --source macaodaily
        """
    )
    
    parser.add_argument('--source', type=str, default='macaodaily', 
                       help='Source to monitor (default: macaodaily)')
    parser.add_argument('--watch', action='store_true', 
                       help='Continuous monitoring mode')
    parser.add_argument('--interval', type=int, default=30,
                       help='Refresh interval in seconds for watch mode (default: 30)')
    
    args = parser.parse_args()
    
    # Create monitor
    monitor = RephraseMonitor(source=args.source)
    
    if args.watch:
        monitor.watch_mode(interval=args.interval)
    else:
        monitor.display_status()


if __name__ == "__main__":
    main()
