#!/usr/bin/env python3
"""
Continuous rephrase processing script for 24/7 operation.

This script provides a convenient wrapper around the CLI rephrase continuous command
with additional monitoring and restart capabilities.
"""

import os
import sys
import time
import signal
import logging
import subprocess
from datetime import datetime, date
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import get_settings


class ContinuousRephraseProcessor:
    """Manages continuous rephrase processing with monitoring and restart capabilities."""
    
    def __init__(self, source: str = "macaodaily", year: int = None,
                 start_date: str = None, end_date: str = None,
                 priority_threshold: float = 0.6,
                 batch_size: int = 8, log_level: str = "INFO"):
        self.source = source
        self.year = year
        self.start_date = start_date
        self.end_date = end_date
        self.priority_threshold = priority_threshold
        self.batch_size = batch_size
        self.log_level = log_level
        
        self.running = False
        self.process = None
        
        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging for the continuous processor."""
        settings = get_settings()
        log_dir = settings.data.log_path
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create logger
        self.logger = logging.getLogger("continuous_processor")
        self.logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # File handler
        log_file = log_dir / f"continuous_rephrase_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def build_command(self) -> list:
        """Build the CLI command for continuous processing."""
        cmd = [
            sys.executable, "-m", "cli", "rephrase", "continuous",
            "--source", self.source,
            "--priority-threshold", str(self.priority_threshold),
            "--batch-size", str(self.batch_size),
            "--log-level", self.log_level,
            "--skip-existing",
            "--create-pre-rephrase"
        ]
        
        if self.year:
            cmd.extend(["--year", str(self.year)])
        elif self.start_date and self.end_date:
            cmd.extend(["--start-date", self.start_date, "--end-date", self.end_date])
        elif self.start_date:
            cmd.extend(["--start-date", self.start_date])
        else:
            # Default to current year
            current_year = date.today().year
            cmd.extend(["--year", str(current_year)])
            
        return cmd
    
    def start(self):
        """Start the continuous processing."""
        if self.running:
            self.logger.warning("Processor is already running")
            return
        
        # Check API key
        settings = get_settings()
        if not settings.rephrasing.api_key:
            self.logger.error("❌ Gemini API key not configured. Set GEMINI_API_KEY environment variable.")
            return
        
        self.logger.info("🚀 Starting continuous rephrase processor")
        self.logger.info(f"📊 Configuration:")
        self.logger.info(f"  Source: {self.source}")
        self.logger.info(f"  Year: {self.year}")
        self.logger.info(f"  Date range: {self.start_date} to {self.end_date}")
        self.logger.info(f"  Priority threshold: {self.priority_threshold}")
        self.logger.info(f"  Batch size: {self.batch_size}")
        
        self.running = True
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Start processing loop
        self._processing_loop()
    
    def _processing_loop(self):
        """Main processing loop with restart capability."""
        restart_count = 0
        max_restarts = 10
        
        while self.running and restart_count < max_restarts:
            try:
                cmd = self.build_command()
                self.logger.info(f"🔄 Starting processing (attempt {restart_count + 1})")
                self.logger.debug(f"Command: {' '.join(cmd)}")
                
                # Start the process
                self.process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1
                )
                
                # Monitor the process
                while self.running and self.process.poll() is None:
                    line = self.process.stdout.readline()
                    if line:
                        # Forward output to our logger
                        self.logger.info(f"[PROCESSOR] {line.strip()}")
                    time.sleep(0.1)
                
                # Process finished
                return_code = self.process.returncode
                
                if return_code == 0:
                    self.logger.info("✅ Processing completed successfully")
                    break
                else:
                    self.logger.error(f"❌ Processing failed with return code {return_code}")
                    restart_count += 1
                    
                    if restart_count < max_restarts:
                        wait_time = min(60 * restart_count, 300)  # Wait 1-5 minutes
                        self.logger.info(f"⏳ Waiting {wait_time} seconds before restart...")
                        time.sleep(wait_time)
                    
            except Exception as e:
                self.logger.error(f"💥 Unexpected error: {e}")
                restart_count += 1
                
                if restart_count < max_restarts:
                    self.logger.info("⏳ Waiting 60 seconds before restart...")
                    time.sleep(60)
        
        if restart_count >= max_restarts:
            self.logger.error(f"❌ Maximum restart attempts ({max_restarts}) reached. Stopping.")
        
        self.running = False
    
    def stop(self):
        """Stop the continuous processing."""
        self.logger.info("🛑 Stopping continuous processor...")
        self.running = False
        
        if self.process and self.process.poll() is None:
            self.logger.info("🔄 Terminating subprocess...")
            self.process.terminate()
            
            # Wait for graceful shutdown
            try:
                self.process.wait(timeout=30)
            except subprocess.TimeoutExpired:
                self.logger.warning("⚠️  Subprocess didn't terminate gracefully, killing...")
                self.process.kill()
                self.process.wait()
        
        self.logger.info("✅ Continuous processor stopped")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"📡 Received signal {signum}, shutting down...")
        self.stop()


def main():
    """Main function with command line argument parsing."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Continuous rephrase processing with monitoring and restart capabilities",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process entire year 2024
  python continuous_rephrase.py --year 2024
  
  # Process date range
  python continuous_rephrase.py --start-date 2024-01-01 --end-date 2024-12-31
  
  # Process from specific date to today
  python continuous_rephrase.py --start-date 2024-06-01
  
  # Custom settings
  python continuous_rephrase.py --year 2024 --batch-size 10 --priority-threshold 0.7
        """
    )
    
    # Date specification
    date_group = parser.add_mutually_exclusive_group()
    date_group.add_argument('--year', type=int, help='Year to process (e.g., 2024)')
    date_group.add_argument('--start-date', type=str, help='Start date (YYYY-MM-DD)')
    
    parser.add_argument('--end-date', type=str, help='End date (YYYY-MM-DD)')
    parser.add_argument('--source', type=str, default='macaodaily', help='Source name')

    parser.add_argument('--priority-threshold', type=float, default=0.6, help='Priority threshold (0.6=high priority only)')
    parser.add_argument('--batch-size', type=int, default=15, help='Batch size for API requests')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='Logging level')
    
    args = parser.parse_args()
    
    # Create and start processor
    processor = ContinuousRephraseProcessor(
        source=args.source,
        year=args.year,
        start_date=args.start_date,
        end_date=args.end_date,
        priority_threshold=args.priority_threshold,
        batch_size=args.batch_size,
        log_level=args.log_level
    )
    
    try:
        processor.start()
    except KeyboardInterrupt:
        processor.stop()


if __name__ == "__main__":
    main()
