#!/usr/bin/env python3
"""
Test script to verify that the rephrase script produces consistent meta format.
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.models.rephrased_article import RephrasedArticle

def test_rephrased_article_to_dict():
    """Test that RephrasedArticle.to_dict() produces the new meta format."""
    print("🧪 Testing RephrasedArticle.to_dict() meta format...")
    
    # Create a sample rephrased article
    article = RephrasedArticle(
        id="test-123",
        title="澳門政府推出新經濟政策支持中小企業發展",
        slug="macau-government-new-economic-policy-sme-support",
        content="# 澳門政府推出新經濟政策\n\n澳門特別行政區政府今日宣布推出一系列新的經濟政策措施...",
        summary="澳門政府宣布新經濟政策，支持中小企業發展，促進經濟多元化。",
        original_article_id="original-123",
        source_url="https://example.com/article",
        source_site_name="澳門日報",
        ai_model_used="gemini-2.5-flash",
        processing_timestamp=datetime.now(),
        processing_metadata={
            "original_title": "政府推新政策",
            "original_content_length": 500,
            "rephrased_content_length": 600,
            "batch_size": 8,
            "batch_index": 1
        },
        categories=["經濟財經", "政府政策"],
        tags=["A01", "澳聞"],
        keywords=["澳門", "經濟政策", "中小企業", "政府支持"],
        priority_score=0.8,
        quality_score=0.9,
        meta_title="澳門新經濟政策 | 中小企業支持措施 2025",
        meta_description="澳門政府推出全新經濟政策，重點支持中小企業發展和創新，為澳門經濟注入新活力，促進多元化發展。",
        images=[{
            "src": "https://example.com/image.jpg",
            "description": "政府官員宣布新政策",
            "alt_text": "澳門政府新聞發布會"
        }]
    )
    
    # Convert to dict
    article_dict = article.to_dict()
    
    print("✅ Article converted to dict successfully")
    
    # Check meta format
    if 'meta' in article_dict:
        meta = article_dict['meta']
        print(f"✅ Found meta object: {meta}")
        
        if isinstance(meta, dict) and 'title' in meta and 'description' in meta:
            print("✅ Meta object has correct structure (title and description)")
            print(f"   📋 Meta title: {meta['title']}")
            print(f"   📄 Meta description: {meta['description'][:50]}...")
        else:
            print("❌ Meta object has incorrect structure")
            return False
    else:
        print("❌ No meta object found in article dict")
        return False
    
    # Check that old format fields are NOT present
    old_fields = ['meta_title', 'meta_description']
    old_found = [field for field in old_fields if field in article_dict]
    
    if old_found:
        print(f"❌ Found old format fields: {old_found}")
        return False
    else:
        print("✅ No old format fields found")
    
    # Check that images are included
    if 'images' in article_dict and article_dict['images']:
        print("✅ Images field included in dict")
        print(f"   📷 Images count: {len(article_dict['images'])}")
    else:
        print("❌ Images field missing or empty")
        return False
    
    # Check all required fields are present
    required_fields = [
        'id', 'title', 'slug', 'content', 'summary', 'original_article_id',
        'source_url', 'source_site_name', 'ai_model_used', 'processing_timestamp',
        'processing_metadata', 'categories', 'tags', 'keywords', 'priority_score',
        'quality_score', 'meta', 'is_published', 'publish_date', 'status', 'images'
    ]
    
    missing_fields = [field for field in required_fields if field not in article_dict]
    
    if missing_fields:
        print(f"❌ Missing required fields: {missing_fields}")
        return False
    else:
        print("✅ All required fields present")
    
    return True

def test_json_serialization():
    """Test that the dict can be properly serialized to JSON."""
    print("\n🧪 Testing JSON serialization...")
    
    # Create a sample article
    article = RephrasedArticle(
        id="test-456",
        title="測試文章",
        slug="test-article",
        content="測試內容",
        summary="測試摘要",
        original_article_id="original-456",
        source_url="https://test.com",
        source_site_name="測試網站",
        ai_model_used="gemini-2.5-flash",
        processing_timestamp=datetime.now(),
        processing_metadata={},
        categories=["測試分類"],
        tags=["測試"],
        keywords=["測試關鍵詞"],
        priority_score=0.7,
        quality_score=0.8,
        meta_title="測試SEO標題",
        meta_description="測試SEO描述",
        images=[]
    )
    
    try:
        # Convert to dict and serialize to JSON
        article_dict = article.to_dict()
        json_str = json.dumps(article_dict, ensure_ascii=False, indent=2, default=str)
        
        print("✅ JSON serialization successful")
        
        # Parse back to verify
        parsed_data = json.loads(json_str)
        
        # Check meta structure in parsed JSON
        if 'meta' in parsed_data and isinstance(parsed_data['meta'], dict):
            meta = parsed_data['meta']
            if 'title' in meta and 'description' in meta:
                print("✅ Meta structure preserved in JSON")
                print(f"   📋 JSON Meta title: {meta['title']}")
                print(f"   📄 JSON Meta description: {meta['description']}")
                return True
            else:
                print("❌ Meta structure corrupted in JSON")
                return False
        else:
            print("❌ Meta object missing in parsed JSON")
            return False
            
    except Exception as e:
        print(f"❌ JSON serialization failed: {e}")
        return False

def test_from_dict_compatibility():
    """Test that from_dict can handle the new format produced by to_dict."""
    print("\n🧪 Testing from_dict compatibility...")
    
    # Create original article
    original = RephrasedArticle(
        id="test-789",
        title="兼容性測試",
        slug="compatibility-test",
        content="測試內容",
        summary="測試摘要",
        original_article_id="original-789",
        source_url="https://test.com",
        source_site_name="測試網站",
        ai_model_used="gemini-2.5-flash",
        processing_timestamp=datetime.now(),
        processing_metadata={},
        categories=["測試"],
        tags=["測試"],
        keywords=["測試"],
        priority_score=0.6,
        quality_score=0.7,
        meta_title="原始SEO標題",
        meta_description="原始SEO描述",
        images=[]
    )
    
    try:
        # Convert to dict (new format)
        article_dict = original.to_dict()
        
        # Create new article from dict
        restored = RephrasedArticle.from_dict(article_dict)
        
        print("✅ from_dict successfully processed new format")
        
        # Check that meta data was correctly extracted
        if restored.meta_title == original.meta_title and restored.meta_description == original.meta_description:
            print("✅ Meta data correctly extracted from new format")
            print(f"   📋 Restored meta_title: {restored.meta_title}")
            print(f"   📄 Restored meta_description: {restored.meta_description}")
            return True
        else:
            print("❌ Meta data not correctly extracted")
            print(f"   Original meta_title: {original.meta_title}")
            print(f"   Restored meta_title: {restored.meta_title}")
            return False
            
    except Exception as e:
        print(f"❌ from_dict compatibility test failed: {e}")
        return False

def main():
    """Run all consistency tests."""
    print("🚀 Testing RephrasedArticle meta format consistency")
    print("=" * 60)
    
    tests = [
        test_rephrased_article_to_dict,
        test_json_serialization,
        test_from_dict_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! RephrasedArticle produces consistent meta format.")
        print("\n📋 Summary:")
        print("   • to_dict() produces new meta object format")
        print("   • JSON serialization preserves meta structure")
        print("   • from_dict() handles both old and new formats")
        print("   • All required fields are included")
        print("   • Images field is properly included")
        return 0
    else:
        print("❌ Some tests failed. Meta format may be inconsistent.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
