#!/usr/bin/env python3
"""
Bulk scraping script for Macao Daily news across date ranges.
Supports scraping specific dates, date ranges, or entire years with random delays.
"""

import argparse
import logging
import random
import time
import sys
from datetime import datetime, date, timedelta
from pathlib import Path
from typing import List, Optional

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from scrapers.macaodaily_scraper import MacaoDailyScraper
from utils.storage import DataStorage


def setup_logging(verbose: bool = False) -> logging.Logger:
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('bulk_scrape.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def parse_date(date_str: str) -> date:
    """Parse date string in YYYY-MM-DD format."""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        raise argparse.ArgumentTypeError(f"Invalid date format: {date_str}. Use YYYY-MM-DD")


def generate_date_range(start_date: date, end_date: date) -> List[date]:
    """Generate list of dates between start and end (inclusive)."""
    dates = []
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date)
        current_date += timedelta(days=1)
    return dates


def random_delay(min_seconds: float = 1.0, max_seconds: float = 5.0):
    """Add random delay between requests."""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)


def scrape_single_date(target_date: date, storage: DataStorage, logger: logging.Logger,
                      min_delay: float = 1.0, max_delay: float = 5.0) -> Optional[str]:
    """
    Scrape articles for a single date.
    Returns the filepath if successful, None if failed.
    """
    date_str = target_date.strftime('%Y-%m-%d')
    logger.info(f"🗓️  Scraping Macao Daily for date: {date_str}")
    
    try:
        # Create scraper instance with target date
        scraper = MacaoDailyScraper(target_date=date_str)

        # Add random delay before checking
        logger.debug(f"⏳ Adding random delay before checking...")
        random_delay(min_delay, max_delay)

        # Check if we should skip full scraping
        should_skip, reason, current_count = storage.should_skip_full_scraping(scraper, date_str)

        if should_skip:
            logger.info(f"📁 {current_count} articles for {date_str}: {reason}")
            # Return the existing file path
            existing_info = storage.get_existing_file_info('macaodaily', date_str)
            return existing_info['filepath'] if existing_info else None

        logger.info(f"🔄 {reason}")

        # Proceed with full scraping
        articles = scraper.scrape()

        if not articles:
            logger.warning(f"❌ No articles found for {date_str}")
            return None
        
        # Save articles using the improved DataStorage logic
        filepath = storage.save_articles(articles, 'macaodaily', date_str)

        if filepath:
            if filepath.startswith("SKIPPED:"):
                actual_path = filepath[8:]  # Remove "SKIPPED:" prefix
                logger.info(f"✅ Successfully scraped {len(articles)} articles for {date_str}")
                logger.info(f"📁 File already exists with same article count: {actual_path}")
            else:
                logger.info(f"✅ Successfully scraped {len(articles)} articles for {date_str}")
                logger.info(f"💾 Saved to: {filepath}")
        else:
            logger.warning(f"❌ Failed to save articles for {date_str}")
        
        return filepath
        
    except Exception as e:
        logger.error(f"❌ Error scraping {date_str}: {e}")
        return None


def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(
        description="Bulk scrape Macao Daily news across date ranges",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Scrape a single date
  python bulk_scrape.py --date 2025-05-28
  
  # Scrape a date range
  python bulk_scrape.py --start-date 2025-05-01 --end-date 2025-05-31
  
  # Scrape entire year 2024
  python bulk_scrape.py --year 2024
  
  # Scrape with custom delays (2-10 seconds between requests)
  python bulk_scrape.py --year 2025 --min-delay 2 --max-delay 10
  
  # Dry run to see what dates would be scraped
  python bulk_scrape.py --year 2025 --dry-run
        """
    )
    
    # Date specification (mutually exclusive)
    date_group = parser.add_mutually_exclusive_group(required=True)
    date_group.add_argument('--date', type=parse_date, 
                           help='Single date to scrape (YYYY-MM-DD)')
    date_group.add_argument('--start-date', type=parse_date,
                           help='Start date for range scraping (YYYY-MM-DD)')
    date_group.add_argument('--year', type=int,
                           help='Year to scrape (e.g., 2024)')
    
    # End date for range scraping
    parser.add_argument('--end-date', type=parse_date,
                       help='End date for range scraping (YYYY-MM-DD)')
    
    # Delay configuration
    parser.add_argument('--min-delay', type=float, default=1.0,
                       help='Minimum delay between requests in seconds (default: 1.0)')
    parser.add_argument('--max-delay', type=float, default=5.0,
                       help='Maximum delay between requests in seconds (default: 5.0)')
    
    # Other options
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be scraped without actually scraping')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--skip-existing', action='store_true', default=True,
                       help='Skip dates that already have data files (default: True)')
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.start_date and not args.end_date:
        parser.error("--end-date is required when using --start-date")
    
    if args.end_date and not args.start_date:
        parser.error("--start-date is required when using --end-date")
    
    # Setup logging
    logger = setup_logging(args.verbose)
    
    # Determine date range
    if args.date:
        dates_to_scrape = [args.date]
    elif args.start_date and args.end_date:
        dates_to_scrape = generate_date_range(args.start_date, args.end_date)
    elif args.year:
        start_date = date(args.year, 1, 1)
        end_date = date(args.year, 12, 31)
        dates_to_scrape = generate_date_range(start_date, end_date)
    
    logger.info(f"🚀 Bulk scraping Macao Daily")
    logger.info(f"📅 Date range: {len(dates_to_scrape)} dates from {dates_to_scrape[0]} to {dates_to_scrape[-1]}")
    logger.info(f"⏱️  Delay range: {args.min_delay}-{args.max_delay} seconds")
    
    if args.dry_run:
        logger.info("🔍 DRY RUN - No actual scraping will be performed")
        logger.info("📋 Dates that would be scraped:")
        for i, scrape_date in enumerate(dates_to_scrape, 1):
            logger.info(f"  {i:3d}. {scrape_date.strftime('%Y-%m-%d')}")
        return
    
    # Initialize storage
    storage = DataStorage()
    
    # Track results
    successful_dates = []
    failed_dates = []
    skipped_dates = []
    
    # Scrape each date
    for i, scrape_date in enumerate(dates_to_scrape, 1):
        logger.info(f"📊 Progress: {i}/{len(dates_to_scrape)} ({i/len(dates_to_scrape)*100:.1f}%)")
        
        # Check if file already exists
        if args.skip_existing:
            expected_filename = f"macaodaily_{scrape_date.strftime('%Y%m%d')}.json"
            expected_filepath = Path("data") / expected_filename
            if expected_filepath.exists():
                logger.info(f"⏭️  Skipping {scrape_date.strftime('%Y-%m-%d')} (file exists)")
                skipped_dates.append(scrape_date)
                continue
        
        # Scrape the date
        result = scrape_single_date(scrape_date, storage, logger, args.min_delay, args.max_delay)
        
        if result:
            successful_dates.append(scrape_date)
        else:
            failed_dates.append(scrape_date)
    
    # Final summary
    logger.info(f"🎉 Bulk scraping completed!")
    logger.info(f"✅ Successful: {len(successful_dates)} dates")
    logger.info(f"⏭️  Skipped: {len(skipped_dates)} dates")
    logger.info(f"❌ Failed: {len(failed_dates)} dates")
    
    if failed_dates:
        logger.warning(f"Failed dates: {[d.strftime('%Y-%m-%d') for d in failed_dates]}")


if __name__ == "__main__":
    main()
