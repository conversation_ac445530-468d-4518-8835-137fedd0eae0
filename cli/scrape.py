"""
CLI commands for scraping operations.
"""

import click
import sys
from datetime import datetime, date, timedelta
from pathlib import Path
from typing import Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.scraping.scrapers.macaodaily_scraper import MacaoDailyScraper
from src.scraping.utils.storage import DataStorage
from config.settings import get_settings


@click.group()
def scrape_cli():
    """Scraping operations for news sources."""
    pass


@scrape_cli.command()
@click.option('--date', type=click.DateTime(formats=['%Y-%m-%d']), 
              help='Specific date to scrape (YYYY-MM-DD)')
@click.option('--output-dir', type=click.Path(), 
              help='Custom output directory')
@click.option('--force', is_flag=True, 
              help='Overwrite existing files')
@click.pass_context
def macaodaily(ctx, date, output_dir, force):
    """Scrape Macao Daily news articles."""
    
    # Get target date
    target_date = date.date() if date else datetime.now().date()
    
    # Set up scraper
    scraper = MacaoDailyScraper(target_date=target_date.strftime('%Y-%m-%d'))
    
    # Set up storage
    storage = DataStorage(output_dir=output_dir)
    
    if ctx.obj.get('dry_run'):
        click.echo(f"[DRY RUN] Would scrape Macao Daily for date: {target_date}")
        click.echo(f"[DRY RUN] Target URL: {scraper.get_index_url()}")
        return
    
    try:
        date_str = target_date.strftime('%Y-%m-%d')
        click.echo(f"Scraping Macao Daily for date: {target_date}")

        # Check if we should skip full scraping by comparing article counts
        should_skip, reason, current_count = storage.should_skip_full_scraping(scraper, date_str)

        if should_skip:
            click.echo(f"Found {current_count} articles on index page")
            click.echo(f"Skipping full scraping: {reason}")
            return

        click.echo(f"Found {current_count} articles on index page")
        click.echo(f"{reason}")

        # Perform full scraping of individual articles
        articles = scraper.scrape()

        if not articles:
            click.echo("No articles found during full scraping.")
            return

        # Save articles
        output_file = storage.save_articles(articles, 'macaodaily', date_str)

        if output_file:
            if output_file.startswith("SKIPPED:"):
                # This shouldn't happen since we already checked counts, but handle it
                actual_path = output_file[8:]  # Remove "SKIPPED:" prefix
                click.echo(f"Successfully scraped {len(articles)} articles")
                click.echo(f"File already exists with same article count - skipped saving: {actual_path}")
            else:
                click.echo(f"Successfully scraped {len(articles)} articles")
                click.echo(f"Saved to: {output_file}")
        else:
            click.echo("Failed to save articles")
        
    except Exception as e:
        click.echo(f"Error scraping Macao Daily: {e}", err=True)
        sys.exit(1)


@scrape_cli.command()
@click.option('--start-date', type=click.DateTime(formats=['%Y-%m-%d']), 
              required=True, help='Start date (YYYY-MM-DD)')
@click.option('--end-date', type=click.DateTime(formats=['%Y-%m-%d']), 
              required=True, help='End date (YYYY-MM-DD)')
@click.option('--source', type=click.Choice(['macaodaily']), 
              default='macaodaily', help='News source to scrape')
@click.option('--delay', type=float, default=2.0, 
              help='Delay between requests in seconds')
@click.option('--output-dir', type=click.Path(), 
              help='Custom output directory')
@click.pass_context
def bulk(ctx, start_date, end_date, source, delay, output_dir):
    """Bulk scrape articles across a date range."""
    
    start = start_date.date()
    end = end_date.date()
    
    if start > end:
        click.echo("Start date must be before end date", err=True)
        sys.exit(1)
    
    # Calculate total days
    total_days = (end - start).days + 1
    
    if ctx.obj.get('dry_run'):
        click.echo(f"[DRY RUN] Would scrape {source} from {start} to {end}")
        click.echo(f"[DRY RUN] Total days: {total_days}")
        click.echo(f"[DRY RUN] Delay between requests: {delay}s")
        return
    
    click.echo(f"Starting bulk scrape of {source}")
    click.echo(f"Date range: {start} to {end} ({total_days} days)")
    click.echo(f"Delay between requests: {delay}s")
    
    # Set up storage
    storage = DataStorage(output_dir=output_dir)
    
    success_count = 0
    error_count = 0
    
    current_date = start
    while current_date <= end:
        try:
            click.echo(f"\nScraping {current_date}...")
            
            if source == 'macaodaily':
                date_str = current_date.strftime('%Y-%m-%d')
                scraper = MacaoDailyScraper(target_date=date_str)

                # Check if we should skip full scraping
                should_skip, reason, current_count = storage.should_skip_full_scraping(scraper, date_str)

                if should_skip:
                    click.echo(f"  📁 {current_count} articles (skipped): {reason}")
                    success_count += 1
                    continue

                # Proceed with full scraping
                articles = scraper.scrape()

                if articles:
                    output_file = storage.save_articles(articles, 'macaodaily', date_str)

                    if output_file:
                        click.echo(f"  ✓ Saved {len(articles)} articles to {output_file}")
                        success_count += 1
                    else:
                        click.echo(f"  ✗ Failed to save articles for {current_date}")
                else:
                    click.echo(f"  ⚠ No articles found for {current_date}")
            
            # Add delay between requests
            if current_date < end:
                import time
                time.sleep(delay)
                
        except Exception as e:
            click.echo(f"  ✗ Error scraping {current_date}: {e}")
            error_count += 1
        
        current_date += timedelta(days=1)
    
    click.echo(f"\nBulk scraping completed!")
    click.echo(f"Successful: {success_count} days")
    click.echo(f"Errors: {error_count} days")


@scrape_cli.command()
@click.option('--output-dir', type=click.Path(), 
              help='Custom output directory')
@click.pass_context
def today(ctx, output_dir):
    """Scrape today's news from all enabled sources."""
    
    target_date = datetime.now().date()
    
    if ctx.obj.get('dry_run'):
        click.echo(f"[DRY RUN] Would scrape today's news: {target_date}")
        return
    
    click.echo(f"Scraping today's news: {target_date}")
    
    # Set up storage
    storage = DataStorage(output_dir=output_dir)
    
    try:
        # Scrape Macao Daily
        date_str = target_date.strftime('%Y-%m-%d')
        scraper = MacaoDailyScraper(target_date=date_str)

        # Check if we should skip full scraping
        should_skip, reason, current_count = storage.should_skip_full_scraping(scraper, date_str)

        if should_skip:
            click.echo(f"Macao Daily: {current_count} articles (skipped) - {reason}")
            return

        # Proceed with full scraping
        articles = scraper.scrape()

        if articles:
            output_file = storage.save_articles(articles, 'macaodaily', date_str)

            if output_file:
                click.echo(f"Macao Daily: {len(articles)} articles saved to {output_file}")
            else:
                click.echo("Macao Daily: Failed to save articles")
        else:
            click.echo("Macao Daily: No articles found")
            
    except Exception as e:
        click.echo(f"Error scraping today's news: {e}", err=True)
        sys.exit(1)


@scrape_cli.command()
@click.option('--output-dir', type=click.Path(), 
              help='Custom output directory')
@click.pass_context
def yesterday(ctx, output_dir):
    """Scrape yesterday's news from all enabled sources."""
    
    target_date = datetime.now().date() - timedelta(days=1)
    
    if ctx.obj.get('dry_run'):
        click.echo(f"[DRY RUN] Would scrape yesterday's news: {target_date}")
        return
    
    click.echo(f"Scraping yesterday's news: {target_date}")
    
    # Set up storage
    storage = DataStorage(output_dir=output_dir)
    
    try:
        # Scrape Macao Daily
        date_str = target_date.strftime('%Y-%m-%d')
        scraper = MacaoDailyScraper(target_date=date_str)

        # Check if we should skip full scraping
        should_skip, reason, current_count = storage.should_skip_full_scraping(scraper, date_str)

        if should_skip:
            click.echo(f"Macao Daily: {current_count} articles (skipped) - {reason}")
            return

        # Proceed with full scraping
        articles = scraper.scrape()

        if articles:
            output_file = storage.save_articles(articles, 'macaodaily', date_str)

            if output_file:
                click.echo(f"Macao Daily: {len(articles)} articles saved to {output_file}")
            else:
                click.echo("Macao Daily: Failed to save articles")
        else:
            click.echo("Macao Daily: No articles found")
            
    except Exception as e:
        click.echo(f"Error scraping yesterday's news: {e}", err=True)
        sys.exit(1)
