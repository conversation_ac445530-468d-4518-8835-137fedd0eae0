"""
Main CLI entry point for the scraping and rephrasing system.
"""

import click
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from cli.scrape import scrape_cli
from cli.status import status_cli
from cli.rephrase import rephrase_cli
from cli.upload import upload


@click.group()
@click.option('--config', type=click.Path(exists=True), help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.option('--dry-run', is_flag=True, help='Preview operations without execution')
@click.pass_context
def cli(ctx, config, verbose, dry_run):
    """
    Scrape-Rephrase-News: A comprehensive news scraping and AI rephrasing system.
    """
    # Ensure context object exists
    ctx.ensure_object(dict)
    
    # Store global options in context
    ctx.obj['config'] = config
    ctx.obj['verbose'] = verbose
    ctx.obj['dry_run'] = dry_run
    
    # Set up logging based on verbose flag
    import logging
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


# Add subcommands
cli.add_command(scrape_cli, name='scrape')
cli.add_command(status_cli, name='status')
cli.add_command(rephrase_cli, name='rephrase')
cli.add_command(upload, name='upload')


if __name__ == '__main__':
    cli()
