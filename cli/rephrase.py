"""
CLI commands for AI article rephrasing.
"""

import asyncio
import json
import click
import logging
import calendar
from pathlib import Path
from datetime import datetime, date, timedelta
from typing import List, Optional

from src.core.models.article import ScrapedArticle
from src.core.models.rephrased_article import RephrasingJob
from src.rephrasing.ai_clients.gemini_client import GeminiClient, GeminiQuotaExceededError, GeminiRateLimitError, GeminiAPIError
from src.rephrasing.processors.article_filter import ArticleFilter
from src.scraping.utils.storage import load_articles
from config.settings import get_settings


def parse_month_specification(month_spec: str) -> List[date]:
    """
    Parse month specification and return list of dates for the entire month(s).

    Supports multiple formats:
    - YYYY-MM: "2025-04" (April 2025)
    - MM: "04" (April current year)
    - Month names: "apr-2025", "april-2025", "apr", "april"
    - Multiple months: "2025-04,2025-05" or "apr-2025,may-2025"

    Args:
        month_spec: Month specification string

    Returns:
        List[date]: List of (start_date, end_date) tuples for each month

    Examples:
        parse_month_specification("2025-04") -> [(2025-04-01, 2025-04-30)]
        parse_month_specification("feb-2025") -> [(2025-02-01, 2025-02-28)]
        parse_month_specification("04") -> [(2025-04-01, 2025-04-30)]  # current year
        parse_month_specification("2025-04,2025-05") -> [(2025-04-01, 2025-04-30), (2025-05-01, 2025-05-31)]
    """
    current_year = date.today().year
    month_ranges = []

    # Month name mapping
    month_names = {
        'jan': 1, 'january': 1,
        'feb': 2, 'february': 2,
        'mar': 3, 'march': 3,
        'apr': 4, 'april': 4,
        'may': 5,
        'jun': 6, 'june': 6,
        'jul': 7, 'july': 7,
        'aug': 8, 'august': 8,
        'sep': 9, 'september': 9,
        'oct': 10, 'october': 10,
        'nov': 11, 'november': 11,
        'dec': 12, 'december': 12
    }

    # Split multiple months
    month_specs = [spec.strip() for spec in month_spec.split(',')]

    for spec in month_specs:
        try:
            year = None
            month = None

            # Format: YYYY-MM
            if '-' in spec and len(spec.split('-')) == 2:
                parts = spec.split('-')
                if parts[0].isdigit() and len(parts[0]) == 4:
                    # YYYY-MM format
                    year = int(parts[0])
                    if parts[1].isdigit():
                        month = int(parts[1])
                    else:
                        # YYYY-month_name format
                        month_name = parts[1].lower()
                        if month_name in month_names:
                            month = month_names[month_name]
                        else:
                            raise ValueError(f"Invalid month name: {parts[1]}")
                elif parts[1].isdigit() and len(parts[1]) == 4:
                    # month_name-YYYY format
                    year = int(parts[1])
                    month_name = parts[0].lower()
                    if month_name in month_names:
                        month = month_names[month_name]
                    else:
                        raise ValueError(f"Invalid month name: {parts[0]}")
                else:
                    raise ValueError(f"Invalid format: {spec}")

            # Format: MM (current year)
            elif spec.isdigit():
                year = current_year
                month = int(spec)

            # Format: month_name (current year)
            elif spec.lower() in month_names:
                year = current_year
                month = month_names[spec.lower()]

            else:
                raise ValueError(f"Invalid month specification: {spec}")

            # Validate year and month
            if year < 1900 or year > 2100:
                raise ValueError(f"Invalid year: {year}. Must be between 1900 and 2100")
            if month < 1 or month > 12:
                raise ValueError(f"Invalid month: {month}. Must be between 1 and 12")

            # Get first and last day of the month
            first_day = date(year, month, 1)
            last_day_of_month = calendar.monthrange(year, month)[1]
            last_day = date(year, month, last_day_of_month)

            month_ranges.append((first_day, last_day))

            # Log the parsed month for user awareness
            month_name = calendar.month_name[month]
            print(f"📅 Parsed month: {spec} → {month_name} {year} ({first_day} to {last_day})")

        except (ValueError, IndexError) as e:
            raise ValueError(f"Invalid month specification '{spec}': {str(e)}")

    return month_ranges


def smart_parse_date(date_str: str) -> date:
    """
    Parse a date string with smart adjustment for invalid dates.

    If the specified day doesn't exist in the month (e.g., 2025-04-31),
    automatically adjusts to the last valid day of that month (e.g., 2025-04-30).

    Args:
        date_str: Date string in YYYY-MM-DD format

    Returns:
        date: Parsed and adjusted date object

    Examples:
        smart_parse_date("2025-04-31") -> 2025-04-30 (April has only 30 days)
        smart_parse_date("2025-02-29") -> 2025-02-28 (2025 is not a leap year)
        smart_parse_date("2025-07-15") -> 2025-07-15 (valid date, no adjustment)
    """
    try:
        # Try to parse the date normally first
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError as e:
        # If parsing fails, try to extract year, month, day and adjust
        try:
            parts = date_str.split('-')
            if len(parts) != 3:
                raise ValueError(f"Invalid date format: {date_str}. Expected YYYY-MM-DD")

            year = int(parts[0])
            month = int(parts[1])
            day = int(parts[2])

            # Validate year and month
            if year < 1900 or year > 2100:
                raise ValueError(f"Invalid year: {year}. Must be between 1900 and 2100")
            if month < 1 or month > 12:
                raise ValueError(f"Invalid month: {month}. Must be between 1 and 12")

            # Get the last valid day of the month
            last_day_of_month = calendar.monthrange(year, month)[1]

            if day > last_day_of_month:
                # Adjust to last valid day of the month
                adjusted_day = last_day_of_month
                adjusted_date = date(year, month, adjusted_day)

                # Log the adjustment for user awareness
                month_names = {
                    1: "January", 2: "February", 3: "March", 4: "April",
                    5: "May", 6: "June", 7: "July", 8: "August",
                    9: "September", 10: "October", 11: "November", 12: "December"
                }

                print(f"📅 Smart date adjustment: {date_str} → {adjusted_date}")
                print(f"   💡 {month_names[month]} {year} only has {last_day_of_month} days")

                return adjusted_date
            else:
                # Day is valid but there might be another issue
                raise ValueError(f"Invalid date: {date_str}. {str(e)}")

        except (ValueError, IndexError) as parse_error:
            raise ValueError(f"Invalid date format: {date_str}. Expected YYYY-MM-DD. Error: {str(parse_error)}")


def setup_enhanced_logging(log_level: str = "INFO") -> logging.Logger:
    """Setup enhanced logging for continuous processing."""
    settings = get_settings()

    # Create logs directory
    log_dir = settings.data.log_path
    log_dir.mkdir(parents=True, exist_ok=True)

    # Create logger
    logger = logging.getLogger("rephrase_processor")

    # Only setup if not already configured
    if not logger.handlers:
        logger.setLevel(getattr(logging, log_level.upper()))

        # File handler with rotation
        log_file = log_dir / f"rephrase_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, log_level.upper()))

        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Add handlers
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        # Prevent propagation to root logger to avoid duplicates
        logger.propagate = False

    return logger


@click.group()
def rephrase_cli():
    """AI-powered article rephrasing commands."""
    pass


@rephrase_cli.command()
@click.option('--file', '-f', 'file_path', type=click.Path(exists=True), 
              help='JSON file containing scraped articles')
@click.option('--source', '-s', type=str, help='Source name (e.g., macaodaily)')
@click.option('--date', '-d', type=str, help='Target date (YYYY-MM-DD)')
@click.option('--priority-threshold', '-p', type=float, default=0.5,
              help='Minimum priority score (0.0-1.0)')
@click.option('--batch-size', '-b', type=int, default=5,
              help='Number of articles to process in each API request (1-5)')
@click.option('--test-connection', is_flag=True,
              help='Test API connection before processing')
@click.option('--dry-run', is_flag=True,
              help='Show what would be processed without making API calls')
def process(file_path: Optional[str], source: Optional[str], date: Optional[str],
           priority_threshold: float, batch_size: int, test_connection: bool, dry_run: bool):
    """Process articles for AI rephrasing."""
    
    settings = get_settings()
    
    # Validate API key
    if not settings.rephrasing.api_key and not dry_run:
        click.echo("❌ Gemini API key not configured. Set GEMINI_API_KEY environment variable.")
        return
    
    # Determine input file
    if file_path:
        input_file = Path(file_path)
    elif source and date:
        # Build file path from source and date
        date_obj = smart_parse_date(date)
        date_str = date_obj.strftime('%Y%m%d')
        year = date_obj.strftime('%Y')
        input_file = settings.data.raw_data_path / source / year / f"{date_str}.json"
    else:
        click.echo("❌ Either --file or both --source and --date must be specified")
        return
    
    if not input_file.exists():
        click.echo(f"❌ Input file not found: {input_file}")
        return
    
    click.echo(f"📖 Loading articles from: {input_file}")
    
    try:
        # Load articles
        articles = load_articles(str(input_file))
        if not articles:
            click.echo("❌ No articles found in input file")
            return
        
        click.echo(f"📊 Loaded {len(articles)} articles")
        
        # Filter articles
        filter_obj = ArticleFilter()
        filter_obj.criteria.min_content_length = settings.rephrasing.min_content_length
        
        filtered_articles = filter_obj.filter_articles(articles)
        
        # Apply priority threshold
        priority_articles = [
            article for article in filtered_articles
            if filter_obj.calculate_priority_score(article) >= priority_threshold
        ]
        
        click.echo(f"🎯 Selected {len(priority_articles)} articles for processing")
        
        if dry_run:
            click.echo("\n🔍 DRY RUN - Articles that would be processed:")
            for i, article in enumerate(priority_articles[:10], 1):
                priority = filter_obj.calculate_priority_score(article)
                click.echo(f"  {i}. {article.title[:60]}... (Priority: {priority:.2f})")
            
            if len(priority_articles) > 10:
                click.echo(f"  ... and {len(priority_articles) - 10} more articles")
            return
        
        # Test connection if requested
        if test_connection:
            click.echo("🔗 Testing API connection...")
            asyncio.run(_test_connection())
        
        # Validate batch size
        if batch_size < 1 or batch_size > 5:
            click.echo("❌ Batch size must be between 1 and 5")
            return

        # Process articles using the enhanced function
        result = asyncio.run(_process_single_date(
            source=input_file.parent.parent.name,
            target_date=date.fromisoformat(input_file.stem[:4] + '-' + input_file.stem[4:6] + '-' + input_file.stem[6:8]),
            priority_threshold=priority_threshold,
            batch_size=batch_size,
            create_pre_rephrase=True,
            logger=logging.getLogger(__name__),
            settings=settings
        ))

        if result['success']:
            click.echo(f"✅ Successfully processed {result['articles_processed']} articles")
        else:
            click.echo(f"❌ Processing failed: {result['error']}")
        
    except Exception as e:
        click.echo(f"❌ Error: {e}")


@rephrase_cli.command()
def quota():
    """Check current API quota status."""
    
    settings = get_settings()
    
    if not settings.rephrasing.api_key:
        click.echo("❌ Gemini API key not configured")
        return
    
    try:
        from src.rephrasing.utils.rate_limiter import GeminiRateLimiter
        
        rate_limiter = GeminiRateLimiter()
        status = rate_limiter.get_quota_status()
        
        click.echo("📊 Gemini API Quota Status")
        click.echo("=" * 40)
        click.echo(f"Daily requests: {status['daily_requests']}/{status['daily_limit']}")
        click.echo(f"Daily remaining: {status['daily_remaining']}")
        click.echo(f"Minute requests: {status['minute_requests']}/{status['minute_limit']}")
        click.echo(f"Tokens used today: {status['tokens_used_today']:,}")
        click.echo(f"Tokens used this minute: {status['tokens_used_this_minute']:,}")
        
        if status['daily_reset_time']:
            reset_time = datetime.fromisoformat(status['daily_reset_time'])
            click.echo(f"Daily quota resets: {reset_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if status['consecutive_errors'] > 0:
            click.echo(f"⚠️  Consecutive errors: {status['consecutive_errors']}")
        
        if status['can_make_request']:
            click.echo("✅ Can make requests")
        else:
            click.echo("🚫 Cannot make requests (quota exceeded or rate limited)")
        
    except Exception as e:
        click.echo(f"❌ Error checking quota: {e}")


@rephrase_cli.command()
def test():
    """Test Gemini API connection."""

    settings = get_settings()

    if not settings.rephrasing.api_key:
        click.echo("❌ Gemini API key not configured. Set GEMINI_API_KEY environment variable.")
        return

    asyncio.run(_test_connection())


@rephrase_cli.command()
@click.option('--source', '-s', type=str, default='macaodaily', help='Source name (e.g., macaodaily)')
@click.option('--start-date', '-sd', type=str, help='Start date (YYYY-MM-DD)')
@click.option('--end-date', '-ed', type=str, help='End date (YYYY-MM-DD)')
@click.option('--month', '-m', type=str, help='Process specific month(s): 2025-04, apr-2025, 04, apr, or 2025-04,2025-05')
@click.option('--year', '-y', type=int, help='Process entire year (e.g., 2024)')
@click.option('--today', is_flag=True, help='Process today only')
@click.option('--priority-threshold', '-p', type=float, default=0.6, help='Minimum priority score (0.0-1.0, 0.6=high priority only)')
@click.option('--batch-size', '-b', type=int, default=8, help='Articles per API request (1-15)')
@click.option('--log-level', '-l', type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR']),
              default='INFO', help='Logging level')
@click.option('--dry-run', is_flag=True, help='Show what would be processed without API calls')
@click.option('--skip-existing', is_flag=True, default=True, help='Skip files that already exist')
@click.option('--create-pre-rephrase', is_flag=True, default=True, help='Create pre-rephrase filtered files')
def continuous(source: str, start_date: Optional[str], end_date: Optional[str], month: Optional[str],
               year: Optional[int], today: bool, priority_threshold: float, batch_size: int, log_level: str,
               dry_run: bool, skip_existing: bool, create_pre_rephrase: bool):
    """Continuous processing of articles with date range support."""

    logger = setup_enhanced_logging(log_level)
    settings = get_settings()

    # Validate API key
    if not settings.rephrasing.api_key and not dry_run:
        click.echo("❌ Gemini API key not configured. Set GEMINI_API_KEY environment variable.")
        return

    # Determine date range
    dates_to_process = _get_date_range(start_date, end_date, month, year, today, logger)
    if not dates_to_process:
        click.echo("❌ No valid date range specified. Use --start-date/--end-date, --month, --year, or --today")
        return

    logger.info(f"🚀 Starting continuous rephrase processing")
    logger.info(f"📅 Date range: {len(dates_to_process)} dates from {dates_to_process[-1]} to {dates_to_process[0]} (newest first)")
    logger.info(f"📊 Settings: priority_threshold={priority_threshold}, batch_size={batch_size}")
    logger.info(f"🔧 Options: skip_existing={skip_existing}, create_pre_rephrase={create_pre_rephrase}")

    if dry_run:
        logger.info("🔍 DRY RUN - No actual processing will be performed")
        _show_processing_plan(dates_to_process, source, logger)
        return

    # Start continuous processing
    asyncio.run(_continuous_processing(
        dates_to_process, source, priority_threshold,
        batch_size, skip_existing, create_pre_rephrase, logger
    ))


def _get_date_range(start_date: Optional[str], end_date: Optional[str], month: Optional[str],
                   year: Optional[int], today: bool, logger: logging.Logger) -> List[date]:
    """Get list of dates to process."""

    if today:
        # Process today only
        today_date = date.today()
        logger.info(f"📅 Processing today only: {today_date}")
        return [today_date]
    elif month:
        # Process specific month(s)
        month_ranges = parse_month_specification(month)
        all_dates = []

        for start_month, end_month in month_ranges:
            # Generate all dates in the month
            current = start_month
            while current <= end_month:
                all_dates.append(current)
                current += timedelta(days=1)

        # Remove duplicates and sort (newest first)
        unique_dates = sorted(set(all_dates), reverse=True)
        logger.info(f"📅 Processing {len(month_ranges)} month(s) with {len(unique_dates)} total dates")
        return unique_dates
    elif year:
        # Process entire year
        start = date(year, 1, 1)
        end = date(year, 12, 31)
        logger.info(f"📅 Processing entire year {year}")
    elif start_date and end_date:
        # Process date range
        start = smart_parse_date(start_date)
        end = smart_parse_date(end_date)
        logger.info(f"📅 Processing date range {start} to {end}")
    elif start_date:
        # Process from start date to today
        start = smart_parse_date(start_date)
        end = date.today()
        logger.info(f"📅 Processing from {start} to today ({end})")
    else:
        return []

    # Generate date list (newest first for news prioritization)
    dates = []
    current = start
    while current <= end:
        dates.append(current)
        current += timedelta(days=1)

    # Reverse the list to process newest dates first
    dates.reverse()
    logger.info(f"📅 Processing order: newest to oldest (latest news prioritized)")

    return dates


def _show_processing_plan(dates: List[date], source: str, logger: logging.Logger):
    """Show what would be processed in dry run."""
    logger.info(f"📋 Processing plan for {source}:")
    logger.info(f"  Total dates: {len(dates)}")
    logger.info(f"  Processing order: {dates[0]} to {dates[-1]} (newest to oldest)")

    # Show first 10 and last 10 dates
    if len(dates) <= 20:
        for i, d in enumerate(dates, 1):
            logger.info(f"  {i:3d}. {d}")
    else:
        for i, d in enumerate(dates[:10], 1):
            logger.info(f"  {i:3d}. {d}")
        logger.info(f"  ... ({len(dates) - 20} more dates)")
        for i, d in enumerate(dates[-10:], len(dates) - 9):
            logger.info(f"  {i:3d}. {d}")


def _is_already_processed(source: str, target_date: date, settings, logger: logging.Logger = None) -> bool:
    """Check if a date has already been completely processed."""
    date_str = target_date.strftime('%Y%m%d')
    year = target_date.strftime('%Y')

    # Check if rephrased file exists
    rephrased_file = settings.data.processed_data_path / "rephrased" / source / year / f"{date_str}.json"
    if not rephrased_file.exists():
        return False

    # Check if processing is complete by comparing actual vs expected article count
    try:
        with open(rephrased_file, 'r', encoding='utf-8') as f:
            rephrased_data = json.load(f)

        # Get actual articles processed
        actual_processed = len(rephrased_data.get('articles', []))

        # Get expected article count (from metadata or job history)
        expected_count = rephrased_data.get('expected_article_count')

        # If no expected count in metadata, try to get from job history
        if expected_count is None:
            job_history = rephrased_data.get('job_history', [])
            if not job_history and 'job_info' in rephrased_data:
                job_history = [rephrased_data['job_info']]

            if job_history:
                # Use the total_articles from the most recent job
                expected_count = job_history[-1].get('total_articles', 0)

        # Consider complete only if we have processed all expected articles
        if expected_count and expected_count > 0:
            completion_percentage = (actual_processed / expected_count) * 100
            is_complete = actual_processed >= expected_count

            if not is_complete and logger:
                logger.info(f"📊 Incomplete processing found for {target_date}: {actual_processed}/{expected_count} articles processed ({completion_percentage:.1f}%)")
        else:
            # Fallback: if no expected count, consider complete if we have any articles
            is_complete = actual_processed > 0
            if logger:
                logger.warning(f"⚠️  No expected article count found for {target_date}, using fallback logic")

        return is_complete

    except Exception as e:
        if logger:
            logger.warning(f"Error checking processing status for {target_date}: {e}")
        return False


def _get_unprocessed_articles(source: str, target_date: date, all_articles: List, settings, logger: logging.Logger) -> List:
    """Get articles that haven't been processed yet."""
    date_str = target_date.strftime('%Y%m%d')
    year = target_date.strftime('%Y')

    # Check if rephrased file exists
    rephrased_file = settings.data.processed_data_path / "rephrased" / source / year / f"{date_str}.json"
    if not rephrased_file.exists():
        logger.info(f"📝 No existing rephrased file found for {target_date}, processing all articles")
        return all_articles

    try:
        with open(rephrased_file, 'r', encoding='utf-8') as f:
            rephrased_data = json.load(f)

        # Get list of already processed article IDs
        processed_article_ids = set()
        for article in rephrased_data.get('articles', []):
            original_id = article.get('original_article_id')
            if original_id:
                processed_article_ids.add(original_id)

        # Filter out already processed articles
        unprocessed_articles = [
            article for article in all_articles
            if article.id not in processed_article_ids
        ]

        logger.info(f"📊 Resume processing: {len(unprocessed_articles)} unprocessed articles out of {len(all_articles)} total")
        return unprocessed_articles

    except Exception as e:
        logger.warning(f"Error reading existing rephrased file for {target_date}: {e}")
        logger.info(f"📝 Processing all articles due to error reading existing file")
        return all_articles


async def _process_single_date(source: str, target_date: date,
                             priority_threshold: float, batch_size: int,
                             create_pre_rephrase: bool, logger: logging.Logger, settings) -> dict:
    """Process articles for a single date."""

    date_str = target_date.strftime('%Y-%m-%d')
    date_filename = target_date.strftime('%Y%m%d')
    year = target_date.strftime('%Y')

    # Build input file path
    input_file = settings.data.raw_data_path / source / year / f"{date_filename}.json"

    if not input_file.exists():
        return {'success': False, 'error': f'Input file not found: {input_file}', 'articles_processed': 0}

    try:
        # Load articles
        articles = load_articles(str(input_file))
        if not articles:
            return {'success': False, 'error': 'No articles found in input file', 'articles_processed': 0}

        logger.info(f"📖 Loaded {len(articles)} articles from {input_file.name}")

        # Filter articles
        filter_obj = ArticleFilter()
        filter_obj.criteria.min_content_length = settings.rephrasing.min_content_length

        filtered_articles = filter_obj.filter_articles(articles)

        # Apply priority threshold
        priority_articles = [
            article for article in filtered_articles
            if filter_obj.calculate_priority_score(article) >= priority_threshold
        ]

        logger.info(f"🔍 Filtered to {len(priority_articles)} high-priority articles (threshold: {priority_threshold})")

        if not priority_articles:
            return {'success': True, 'error': None, 'articles_processed': 0}

        # Get only unprocessed articles (resume functionality)
        unprocessed_articles = _get_unprocessed_articles(source, target_date, priority_articles, settings, logger)

        if not unprocessed_articles:
            logger.info(f"✅ All articles already processed for {date_str}")
            return {'success': True, 'error': None, 'articles_processed': 0}

        # Create pre-rephrase file if requested (use all priority articles for visibility)
        if create_pre_rephrase:
            await _create_pre_rephrase_file(priority_articles, source, target_date, settings, logger)

        # Process with AI (use only unprocessed articles)
        # ALWAYS get expected_article_count from pre-rephrase file to ensure consistency
        pre_rephrase_file = settings.data.processed_data_path / "pre-rephrase" / source / target_date.strftime('%Y') / f"{target_date.strftime('%Y%m%d')}.json"

        # Get expected count from pre-rephrase file (source of truth)
        total_filtered_count = None
        if pre_rephrase_file.exists():
            try:
                with open(pre_rephrase_file, 'r', encoding='utf-8') as f:
                    pre_rephrase_data = json.load(f)
                total_filtered_count = len(pre_rephrase_data.get('articles', []))
                logger.info(f"📊 Expected article count from pre-rephrase file: {total_filtered_count}")
            except Exception as e:
                logger.warning(f"⚠️  Could not read pre-rephrase file: {e}, using current filtered count")

        # If no pre-rephrase file or failed to read, use current filtered count as fallback
        if total_filtered_count is None:
            total_filtered_count = len(priority_articles)
            logger.info(f"📊 Expected article count from current filtering: {total_filtered_count}")

        job_result = await _process_articles_enhanced(unprocessed_articles, input_file, settings, batch_size, logger, total_filtered_count)

        # Check if quota was exhausted
        quota_exhausted = job_result.get('quota_exhausted', False)

        return {
            'success': not quota_exhausted,  # Success = False if quota exhausted
            'error': 'Daily quota exhausted' if quota_exhausted else None,
            'articles_processed': len(unprocessed_articles),
            'quota_exhausted': quota_exhausted
        }

    except Exception as e:
        logger.error(f"Error processing {date_str}: {e}")
        return {'success': False, 'error': str(e), 'articles_processed': 0}


async def _create_pre_rephrase_file(articles: List[ScrapedArticle], source: str,
                                  target_date: date, settings, logger: logging.Logger):
    """Create pre-rephrase filtered file for better visibility."""
    date_filename = target_date.strftime('%Y%m%d')
    year = target_date.strftime('%Y')

    # Create pre-rephrase directory
    pre_rephrase_dir = settings.data.processed_data_path / "pre-rephrase" / source / year
    pre_rephrase_dir.mkdir(parents=True, exist_ok=True)

    pre_rephrase_file = pre_rephrase_dir / f"{date_filename}.json"

    # Create pre-rephrase data
    pre_rephrase_data = {
        "source": source,
        "target_date": target_date.strftime('%Y-%m-%d'),
        "filtered_at": datetime.now().isoformat(),
        "original_article_count": len(articles),
        "filter_criteria": {
            "min_content_length": settings.rephrasing.min_content_length,
            "max_content_length": settings.rephrasing.max_content_length,
            "priority_threshold": 0.5  # This should be passed as parameter
        },
        "articles": []
    }

    # Add articles with priority scores - SLIM VERSION for efficiency
    filter_obj = ArticleFilter()
    for article in articles:
        priority_score = filter_obj.calculate_priority_score(article)

        # Create slim article data with only essential fields
        article_data = {
            'id': article.id,
            'original_url': getattr(article, 'original_url', '') or article.source_url,
            'title': article.title,
            'author': article.author,
            'publish_date': str(article.publish_date) if article.publish_date else None,
            'created_at': str(article.created_at) if hasattr(article, 'created_at') else None,
            'content_markdown': getattr(article, 'content_markdown', '') or article.content,
            'images': [
                {
                    'src': img.src if hasattr(img, 'src') else img.get('src', ''),
                    'description': img.description if hasattr(img, 'description') else img.get('description', '')
                } for img in (article.images or [])
            ],
            'tags': article.tags or [],
            'priority_score': priority_score
        }

        pre_rephrase_data["articles"].append(article_data)

    # Save pre-rephrase file
    with open(pre_rephrase_file, 'w', encoding='utf-8') as f:
        json.dump(pre_rephrase_data, f, ensure_ascii=False, indent=2, default=str)

    logger.info(f"📋 Created pre-rephrase file: {pre_rephrase_file}")


async def _save_batch_progress(output_file: Path, batch_results: List, job: RephrasingJob,
                             settings, total_filtered_count: int, batch_size: int, logger: logging.Logger):
    """Save progress after each successful batch to prevent data loss."""
    try:
        # Load existing data if file exists
        existing_articles = []
        existing_job_history = []

        if output_file.exists():
            try:
                with open(output_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                existing_articles = existing_data.get('articles', [])

                # Handle both old format (job_info) and new format (job_history)
                if 'job_history' in existing_data:
                    existing_job_history = existing_data['job_history']
                elif 'job_info' in existing_data:
                    # Convert old format to new format
                    existing_job_history = [existing_data['job_info']]

            except Exception as e:
                logger.warning(f"Error reading existing file for batch save: {e}")

        # Merge new batch results with existing articles (avoid duplicates by ID)
        existing_ids = {article.get('original_article_id') for article in existing_articles}
        new_articles_data = [article.to_dict() for article in batch_results]

        # Add only new articles (not already processed)
        merged_articles = existing_articles.copy()
        new_count = 0
        for new_article in new_articles_data:
            if new_article.get('original_article_id') not in existing_ids:
                merged_articles.append(new_article)
                new_count += 1

        # Update job history with current progress (create a progress entry)
        progress_job_info = {
            "id": job.id,
            "started_at": job.started_at,
            "last_batch_at": datetime.now().isoformat(),
            "status": "in_progress",
            "total_articles": job.total_articles,
            "successful_articles": job.successful_articles,
            "failed_articles": job.failed_articles,
            "ai_model": settings.rephrasing.model_name,
            "batch_size": batch_size
        }

        # Update or add the current job in history
        job_updated = False
        for i, existing_job in enumerate(existing_job_history):
            if existing_job.get('id') == job.id:
                existing_job_history[i] = progress_job_info
                job_updated = True
                break

        if not job_updated:
            existing_job_history.append(progress_job_info)

        # Create output data
        output_data = {
            "source": output_file.parent.parent.name,
            "target_date": job.target_date,
            "processed_at": datetime.now().isoformat(),
            "ai_model": settings.rephrasing.model_name,
            "article_count": len(merged_articles),
            "expected_article_count": total_filtered_count,
            "job_history": existing_job_history,
            "articles": merged_articles
        }

        # Save the updated file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2, default=str)

        logger.debug(f"💾 Batch progress saved: {new_count} new articles, total: {len(merged_articles)}")

    except Exception as e:
        logger.error(f"❌ Error saving batch progress: {e}")
        # Don't raise the exception - batch processing should continue even if save fails


async def _finalize_job_status(output_file: Path, job: RephrasingJob, settings,
                             total_filtered_count: int, batch_size: int,
                             total_rephrased: int, logger: logging.Logger):
    """Finalize job status and update the file with completion information."""
    try:
        # Load current data
        if not output_file.exists():
            logger.warning("Output file doesn't exist for finalization")
            return

        with open(output_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Update job status to completed (unless already set to quota_exhausted)
        job.completed_at = datetime.now().isoformat()
        if job.status != "quota_exhausted":
            if total_rephrased > 0:
                job.status = "completed"
            else:
                job.status = "no_new_articles"

        # Create final job record
        final_job_info = {
            "id": job.id,
            "started_at": job.started_at,
            "completed_at": job.completed_at,
            "status": job.status,
            "total_articles": job.total_articles,
            "successful_articles": job.successful_articles,
            "failed_articles": job.failed_articles,
            "ai_model": settings.rephrasing.model_name,
            "batch_size": batch_size
        }

        # Add error message only if there was an error
        if job.error_message:
            final_job_info["error_message"] = job.error_message

        # Add quota exhausted time if applicable
        if job.quota_exhausted_time:
            final_job_info["quota_exhausted_time"] = job.quota_exhausted_time.isoformat()

        # Update the job history - replace the in_progress entry with completed
        job_history = data.get('job_history', [])
        job_updated = False
        for i, existing_job in enumerate(job_history):
            if existing_job.get('id') == job.id:
                job_history[i] = final_job_info
                job_updated = True
                break

        if not job_updated:
            job_history.append(final_job_info)

        # Update the data
        data['job_history'] = job_history
        data['processed_at'] = datetime.now().isoformat()

        # Save the finalized file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)

        logger.info(f"✅ Job finalized: {job.status} - {job.successful_articles} successful, {job.failed_articles} failed")
        logger.info(f"📊 Total articles in file: {data['article_count']}")

        # Special message for quota exhaustion
        if job.status == "quota_exhausted":
            logger.warning(f"🚫 Processing stopped due to daily quota exhaustion")
            logger.warning(f"💡 Resume processing tomorrow or upgrade your Gemini API plan")
            logger.warning(f"📊 Progress saved: {job.successful_articles} articles completed")

    except Exception as e:
        logger.error(f"❌ Error finalizing job status: {e}")


async def _process_articles_enhanced(articles: List[ScrapedArticle], input_file: Path,
                                   settings, batch_size: int, logger: logging.Logger, total_filtered_count: int = None):
    """Enhanced article processing with better logging and error handling."""

    # Create output directory
    output_dir = settings.data.processed_data_path / "rephrased" / input_file.parent.parent.name / input_file.parent.name
    output_dir.mkdir(parents=True, exist_ok=True)

    output_file = output_dir / input_file.name

    # Create rephrasing job
    job = RephrasingJob(
        source_file=str(input_file),
        target_date=input_file.stem,
        total_articles=len(articles),
        status="running",
        started_at=datetime.now()
    )

    rephrased_articles = []

    try:
        async with GeminiClient() as client:
            logger.info(f"🤖 Starting AI rephrasing with {client.model} (batch size: {batch_size})")

            # Process articles in batches
            total_batches = (len(articles) + batch_size - 1) // batch_size
            logger.info(f"📦 Processing {len(articles)} articles in {total_batches} batches")

            batch_idx = 0
            while batch_idx < len(articles):
                batch = articles[batch_idx:batch_idx + batch_size]
                batch_num = (batch_idx // batch_size) + 1

                logger.info(f"🔄 Processing batch {batch_num}/{total_batches} ({len(batch)} articles)")

                try:
                    # Check quota before processing batch
                    quota_status = client.get_quota_status()
                    logger.debug(f"📊 Quota status: {quota_status['daily_requests']}/{quota_status['daily_limit']} daily, "
                               f"{quota_status['minute_requests']}/{quota_status['minute_limit']} per minute, "
                               f"can_make_request: {quota_status['can_make_request']}")

                    if not quota_status['can_make_request']:
                        # Check if it's just a rate limit (need to wait) vs quota exhausted
                        if quota_status['daily_requests'] >= quota_status['daily_limit']:
                            logger.warning(f"⚠️  Daily quota exhausted ({quota_status['daily_requests']}/{quota_status['daily_limit']})")
                            job.status = "paused"
                            break
                        else:
                            # It's a rate limit - wait and retry the SAME batch
                            wait_time = client.get_wait_time()
                            if wait_time > 0:
                                logger.info(f"⏳ Rate limit reached, waiting {wait_time:.1f} seconds...")
                                await asyncio.sleep(wait_time + 1)  # Add 1 second buffer
                                continue  # Retry the same batch
                            else:
                                logger.warning(f"⚠️  Unknown quota issue: {quota_status}")
                                job.status = "paused"
                                break

                    # Process batch
                    if batch_size == 1:
                        # Single article processing
                        rephrased = await client.rephrase_article(batch[0])
                        batch_results = [rephrased]
                    else:
                        # Batch processing
                        batch_results = await client.rephrase_articles_batch(batch)

                    if batch_results:
                        rephrased_articles.extend(batch_results)
                        job.successful_articles += len(batch_results)
                        logger.info(f"✅ Batch {batch_num} completed: {len(batch_results)} articles processed")

                        # Save progress after each successful batch
                        await _save_batch_progress(output_file, batch_results, job, settings,
                                                 total_filtered_count, batch_size, logger)
                    else:
                        job.failed_articles += len(batch)
                        logger.error(f"❌ Batch {batch_num} FAILED: no results returned")
                        logger.error(f"🔍 Batch details: {len(batch)} articles, batch size {batch_size}")

                        # Log first few article titles for debugging
                        sample_titles = [article.title[:50] + "..." if len(article.title) > 50 else article.title
                                       for article in batch[:3]]
                        logger.error(f"📄 Sample articles in failed batch: {sample_titles}")

                except (GeminiQuotaExceededError, GeminiRateLimitError) as e:
                    error_msg = str(e).lower()

                    # Extract retry delay from error message
                    import re
                    retry_match = re.search(r"retrydelay.*?(\d+)s", error_msg)
                    retry_delay = int(retry_match.group(1)) if retry_match else 60

                    # Check if it's a daily quota exhaustion (should stop) vs rate limit (should wait)
                    quota_indicators = [
                        "quota" in error_msg and "250" in error_msg,
                        "resource_exhausted" in error_msg,
                        "exceeded your current quota" in error_msg,
                        "generativelanguage.googleapis.com/generate_content_free_tier_requests" in error_msg
                    ]

                    if any(quota_indicators):
                        logger.warning(f"🚫 Daily quota exhausted during batch {batch_num}")
                        logger.warning(f"📊 API suggests waiting {retry_delay} seconds (quota reset)")
                        logger.warning(f"💡 Processing stopped to preserve quota. Resume tomorrow or upgrade plan.")
                        job.status = "quota_exhausted"
                        job.quota_exhausted_time = datetime.now()
                        break
                    else:
                        # It's a rate limit - wait and retry
                        logger.warning(f"⏳ Rate limit hit during batch {batch_num}")
                        logger.info(f"⏳ API suggests waiting {retry_delay} seconds, waiting {retry_delay + 5} seconds...")
                        await asyncio.sleep(retry_delay + 5)  # Add 5 second buffer
                        continue  # Retry the same batch

                except GeminiAPIError as e:
                    logger.error(f"❌ API error during batch {batch_num}: {e}")
                    job.failed_articles += len(batch)
                    # Move to next batch on API errors
                    batch_idx += batch_size
                    continue

                except Exception as e:
                    error_msg = str(e).lower()

                    # Check if this is actually a quota exhaustion error that wasn't caught above
                    quota_indicators = [
                        "quota" in error_msg and "250" in error_msg,
                        "resource_exhausted" in error_msg,
                        "quotavalue" in error_msg,
                        "exceeded your current quota" in error_msg,
                        "generativelanguage.googleapis.com/generate_content_free_tier_requests" in error_msg
                    ]

                    if any(quota_indicators):
                        # Extract retry delay from error message
                        import re
                        retry_match = re.search(r"retrydelay.*?(\d+)s", error_msg)
                        retry_delay = int(retry_match.group(1)) if retry_match else 60

                        logger.warning(f"🚫 Daily quota exhausted during batch {batch_num} (caught as generic exception)")
                        logger.warning(f"📊 API suggests waiting {retry_delay} seconds (quota reset)")
                        logger.warning(f"💡 Processing stopped to preserve quota. Resume tomorrow or upgrade plan.")
                        job.status = "quota_exhausted"
                        job.quota_exhausted_time = datetime.now()
                        break
                    else:
                        logger.error(f"❌ Unexpected error processing batch {batch_num}: {e}")
                        job.failed_articles += len(batch)
                        # Move to next batch even on error to avoid infinite loop
                        batch_idx += batch_size
                        continue

                job.processed_articles += len(batch)

                # Log progress
                progress = (job.processed_articles / job.total_articles) * 100
                logger.info(f"📊 Progress: {job.processed_articles}/{job.total_articles} ({progress:.1f}%)")

                # Move to next batch
                batch_idx += batch_size

        # Final save - update job status to completed
        await _finalize_job_status(output_file, job, settings, total_filtered_count,
                                 batch_size, len(rephrased_articles), logger)

        # Update job status
        if job.status == "running":
            job.status = "completed"
        job.completed_at = datetime.now()

        # Log summary
        logger.info(f"📊 Processing Summary:")
        logger.info(f"  Total articles: {job.total_articles}")
        logger.info(f"  Successfully processed: {job.successful_articles}")
        logger.info(f"  Failed: {job.failed_articles}")
        logger.info(f"  Status: {job.status}")

        return {
            'success': True,
            'quota_exhausted': job.status == "quota_exhausted",
            'status': job.status
        }

    except Exception as e:
        job.status = "failed"
        job.error_message = str(e)
        job.completed_at = datetime.now()
        logger.error(f"💥 Processing failed: {e}")
        return {
            'success': False,
            'quota_exhausted': False,
            'status': "failed"
        }


async def _test_connection():
    """Test API connection."""
    try:
        async with GeminiClient() as client:
            click.echo("🔗 Testing Gemini API connection...")
            success = await client.test_connection()
            
            if success:
                click.echo("✅ API connection successful!")
                
                # Show quota status
                status = client.get_quota_status()
                click.echo(f"📊 Daily quota: {status['daily_requests']}/{status['daily_limit']}")
            else:
                click.echo("❌ API connection failed")
    
    except Exception as e:
        click.echo(f"❌ Connection test failed: {e}")


async def _continuous_processing(dates: List[date], source: str,
                               priority_threshold: float, batch_size: int, skip_existing: bool,
                               create_pre_rephrase: bool, logger: logging.Logger):
    """Main continuous processing loop."""
    settings = get_settings()

    # Statistics tracking
    total_files = len(dates)
    processed_files = 0
    successful_files = 0
    failed_files = 0
    skipped_files = 0
    total_articles_processed = 0

    logger.info(f"🎯 Starting processing of {total_files} files")

    for i, target_date in enumerate(dates, 1):
        date_str = target_date.strftime('%Y-%m-%d')
        logger.info(f"📊 Progress: {i}/{total_files} ({i/total_files*100:.1f}%) - Processing {date_str}")

        try:
            # Check if already processed
            if skip_existing and _is_already_processed(source, target_date, settings, logger):
                logger.info(f"⏭️  Skipping {date_str} (already completed)")
                skipped_files += 1
                continue

            # Process single date
            result = await _process_single_date(
                source, target_date, priority_threshold,
                batch_size, create_pre_rephrase, logger, settings
            )

            processed_files += 1
            if result['success']:
                successful_files += 1
                total_articles_processed += result['articles_processed']
                logger.info(f"✅ Successfully processed {date_str}: {result['articles_processed']} articles")
            else:
                failed_files += 1
                logger.error(f"❌ Failed to process {date_str}: {result['error']}")

                # Check if failure was due to quota exhaustion
                if result.get('quota_exhausted', False):
                    logger.warning(f"🚫 Stopping continuous processing due to quota exhaustion")
                    logger.warning(f"💡 Resume processing tomorrow or upgrade your Gemini API plan")
                    logger.warning(f"📊 Processed {successful_files}/{total_files} files before quota exhaustion")
                    break

        except Exception as e:
            failed_files += 1
            logger.error(f"💥 Unexpected error processing {date_str}: {e}")

        # Log progress summary every 10 files
        if i % 10 == 0:
            logger.info(f"📈 Progress Summary: {successful_files} successful, {failed_files} failed, {skipped_files} skipped")
            logger.info(f"📊 Total articles processed: {total_articles_processed}")

    # Final summary
    logger.info(f"🏁 Processing completed!")
    logger.info(f"📊 Final Statistics:")
    logger.info(f"  Total files: {total_files}")
    logger.info(f"  Processed: {processed_files}")
    logger.info(f"  Successful: {successful_files}")
    logger.info(f"  Failed: {failed_files}")
    logger.info(f"  Skipped: {skipped_files}")
    logger.info(f"  Total articles processed: {total_articles_processed}")





if __name__ == '__main__':
    rephrase_cli()
