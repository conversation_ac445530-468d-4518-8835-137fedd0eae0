"""
CLI commands for system status and statistics.
"""

import click
import json
import sys
from pathlib import Path
from datetime import datetime, timedelta
from collections import defaultdict

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.settings import get_settings


def filter_stats_data(stats_data, source_filter, days_filter):
    """Filter stats data based on source and days parameters."""
    if days_filter is None and source_filter == 'all':
        return stats_data

    from datetime import datetime, timedelta

    filtered_stats = {
        "total_files": 0,
        "total_articles": 0,
        "total_days": 0,
        "average_articles_per_day": 0.0,
        "date_range": {"earliest": None, "latest": None},
        "sources": {},
        "files_by_month": {},
        "articles_by_tag": {},
        "daily_counts": {},
        "files": []
    }

    # Calculate date filter if specified
    end_date = None
    start_date = None
    if days_filter is not None:
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days_filter-1)

    # Filter files
    for file_info in stats_data.get('files', []):
        # Apply source filter
        if source_filter != 'all' and file_info.get('source', 'unknown') != source_filter:
            continue

        # Apply date filter
        if days_filter is not None and file_info.get('target_date'):
            try:
                file_date = datetime.strptime(file_info['target_date'], '%Y-%m-%d').date()
                if file_date < start_date or file_date > end_date:
                    continue
            except:
                continue

        # Include this file
        filtered_stats['files'].append(file_info)
        filtered_stats['total_files'] += 1
        filtered_stats['total_articles'] += file_info.get('article_count', 0)

        # Update date range
        target_date = file_info.get('target_date')
        if target_date:
            if not filtered_stats['date_range']['earliest'] or target_date < filtered_stats['date_range']['earliest']:
                filtered_stats['date_range']['earliest'] = target_date
            if not filtered_stats['date_range']['latest'] or target_date > filtered_stats['date_range']['latest']:
                filtered_stats['date_range']['latest'] = target_date

            # Update daily counts
            filtered_stats['daily_counts'][target_date] = filtered_stats['daily_counts'].get(target_date, 0) + file_info.get('article_count', 0)

            # Update monthly counts
            month_key = target_date[:7]
            filtered_stats['files_by_month'][month_key] = filtered_stats['files_by_month'].get(month_key, 0) + 1

        # Update source stats
        source_name = file_info.get('source', 'unknown')
        if source_name not in filtered_stats['sources']:
            filtered_stats['sources'][source_name] = {
                "files": 0,
                "articles": 0,
                "date_range": {"earliest": None, "latest": None}
            }

        filtered_stats['sources'][source_name]['files'] += 1
        filtered_stats['sources'][source_name]['articles'] += file_info.get('article_count', 0)

        if target_date:
            if not filtered_stats['sources'][source_name]['date_range']['earliest'] or target_date < filtered_stats['sources'][source_name]['date_range']['earliest']:
                filtered_stats['sources'][source_name]['date_range']['earliest'] = target_date
            if not filtered_stats['sources'][source_name]['date_range']['latest'] or target_date > filtered_stats['sources'][source_name]['date_range']['latest']:
                filtered_stats['sources'][source_name]['date_range']['latest'] = target_date

    # Calculate derived statistics
    filtered_stats['total_days'] = len(filtered_stats['daily_counts'])
    if filtered_stats['total_days'] > 0:
        filtered_stats['average_articles_per_day'] = filtered_stats['total_articles'] / filtered_stats['total_days']

    # Extract tags from the filtered files by re-reading the actual articles
    from pathlib import Path
    import json

    raw_data_dir = Path("data/raw")
    for file_info in filtered_stats['files']:
        try:
            file_path = raw_data_dir / file_info['filepath']
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Extract tags from articles in this file
            for article in data.get('articles', []):
                tags = article.get('tags', [])
                for tag in tags:
                    filtered_stats['articles_by_tag'][tag] = filtered_stats['articles_by_tag'].get(tag, 0) + 1
        except Exception as e:
            continue

    return filtered_stats


def print_comprehensive_stats(stats_data, source_filter=None, days_filter=None):
    """Print comprehensive formatted statistics with optional filtering info."""

    # Header with filter info
    if days_filter is not None:
        click.echo(f"📊 Comprehensive News Scraping Statistics (Last {days_filter} days)")
    else:
        click.echo("📊 Comprehensive News Scraping Statistics")

    if source_filter and source_filter != 'all':
        click.echo(f"🔍 Filtered by source: {source_filter}")

    click.echo("=" * 60)

    # Overall statistics
    click.echo("📈 OVERALL SUMMARY")
    click.echo("-" * 30)
    click.echo(f"📁 Total files: {stats_data['total_files']:,}")
    click.echo(f"📰 Total articles: {stats_data['total_articles']:,}")
    click.echo(f"📅 Total days with data: {stats_data['total_days']:,}")
    click.echo(f"📊 Average articles per day: {stats_data['average_articles_per_day']:.1f}")

    if stats_data['date_range']['earliest'] and stats_data['date_range']['latest']:
        click.echo(f"🗓️  Date range: {stats_data['date_range']['earliest']} to {stats_data['date_range']['latest']}")

        # Calculate date span
        from datetime import datetime
        try:
            start_date = datetime.strptime(stats_data['date_range']['earliest'], '%Y-%m-%d')
            end_date = datetime.strptime(stats_data['date_range']['latest'], '%Y-%m-%d')
            total_span_days = (end_date - start_date).days + 1
            coverage_percentage = (stats_data['total_days'] / total_span_days) * 100
            click.echo(f"📊 Coverage: {stats_data['total_days']}/{total_span_days} days ({coverage_percentage:.1f}%)")
        except:
            pass

    # Source breakdown
    if stats_data['sources']:
        click.echo(f"\n📰 BY NEWS SOURCE")
        click.echo("-" * 30)
        for source, source_stats in sorted(stats_data['sources'].items()):
            click.echo(f"🏢 {source}:")
            click.echo(f"   📁 Files: {source_stats['files']:,}")
            click.echo(f"   📰 Articles: {source_stats['articles']:,}")
            if source_stats['date_range']['earliest'] and source_stats['date_range']['latest']:
                click.echo(f"   📅 Range: {source_stats['date_range']['earliest']} to {source_stats['date_range']['latest']}")
            avg_per_file = source_stats['articles'] / max(source_stats['files'], 1)
            click.echo(f"   📊 Avg per file: {avg_per_file:.1f}")
            click.echo()

    # Monthly breakdown (limit to recent months if filtered)
    if stats_data['files_by_month']:
        click.echo("📅 BY MONTH")
        click.echo("-" * 30)
        months_to_show = sorted(stats_data['files_by_month'].items())
        if days_filter is not None and len(months_to_show) > 6:
            months_to_show = months_to_show[-6:]  # Show last 6 months for filtered view

        for month, count in months_to_show:
            # Calculate articles for this month
            month_articles = sum(
                article_count for date, article_count in stats_data['daily_counts'].items()
                if date and date.startswith(month)
            )
            click.echo(f"  {month}: {count:,} files, {month_articles:,} articles")

    # Top tags - show top 50 with total count
    if stats_data['articles_by_tag']:
        total_unique_tags = len(stats_data['articles_by_tag'])
        click.echo(f"\n🏷️  TOP ARTICLE TAGS (Total: {total_unique_tags:,} unique tags)")
        click.echo("-" * 60)
        sorted_tags = sorted(stats_data['articles_by_tag'].items(), key=lambda x: x[1], reverse=True)
        tags_to_show = 50  # Always show top 50 tags

        for i, (tag, count) in enumerate(sorted_tags[:tags_to_show], 1):
            percentage = (count / stats_data['total_articles']) * 100 if stats_data['total_articles'] > 0 else 0
            click.echo(f"  {i:2d}. {tag}: {count:,} articles ({percentage:.1f}%)")

        if len(sorted_tags) > tags_to_show:
            remaining_tags = len(sorted_tags) - tags_to_show
            remaining_articles = sum(count for _, count in sorted_tags[tags_to_show:])
            click.echo(f"  ... and {remaining_tags:,} more tags with {remaining_articles:,} articles")

    # Recent activity
    files_to_show = 5 if days_filter is not None else 10
    click.echo(f"\n📋 RECENT FILES (Last {files_to_show})")
    click.echo("-" * 30)
    for file_info in stats_data['files'][-files_to_show:]:
        size_mb = file_info['file_size'] / (1024 * 1024)
        click.echo(f"  📄 {file_info['filepath']}")
        click.echo(f"     📅 {file_info['target_date']} | 📰 {file_info['article_count']} articles | 💾 {size_mb:.1f}MB")

    # Daily activity summary
    if stats_data['daily_counts']:
        click.echo(f"\n📊 DAILY ACTIVITY SUMMARY")
        click.echo("-" * 30)
        daily_values = list(stats_data['daily_counts'].values())
        click.echo(f"📈 Highest day: {max(daily_values):,} articles")
        click.echo(f"📉 Lowest day: {min(daily_values):,} articles")
        click.echo(f"📊 Median: {sorted(daily_values)[len(daily_values)//2]:,} articles")

        # Show top productive days
        top_days = sorted(stats_data['daily_counts'].items(), key=lambda x: x[1], reverse=True)[:5]
        click.echo(f"\n🏆 TOP 5 MOST PRODUCTIVE DAYS:")
        for i, (date, count) in enumerate(top_days, 1):
            click.echo(f"  {i}. {date}: {count:,} articles")


@click.group()
def status_cli():
    """System status and statistics."""
    pass


@status_cli.command()
@click.option('--source', type=click.Choice(['macaodaily', 'all']),
              default='all', help='News source to check')
@click.option('--days', type=int, default=None,
              help='Number of recent days to analyze (default: all data)')
def stats(source, days):
    """Show comprehensive scraping statistics."""

    # Import the improved stats function
    from scripts.scrape_utils import get_scraping_stats

    # Always use the comprehensive function, but with filtering
    stats_data = get_scraping_stats()

    if "error" in stats_data:
        click.echo(f"❌ {stats_data['error']}")
        return

    # Apply filtering if specified
    if days is not None or source != 'all':
        stats_data = filter_stats_data(stats_data, source, days)

    # Print comprehensive formatted output
    print_comprehensive_stats(stats_data, source, days)


@status_cli.command()
@click.option('--limit', default=10, help='Number of files to show')
def files(limit):
    """Show recent data files."""
    settings = get_settings()
    data_dir = settings.data.raw_data_path

    if not data_dir.exists():
        click.echo(f"Data directory not found: {data_dir}")
        return

    click.echo(f"📁 Recent Data Files (Last {limit})")
    click.echo("=" * 50)

    # Collect file information
    files_info = []

    for file_path in data_dir.rglob("*.json"):
        try:
            stat = file_path.stat()
            size_mb = stat.st_size / (1024 * 1024)
            modified = datetime.fromtimestamp(stat.st_mtime)

            # Try to determine source and article count
            source = "unknown"
            articles = 0

            # Determine source from path
            if 'macaodaily' in str(file_path):
                source = 'macaodaily'
            elif 'shimindaily' in str(file_path):
                source = 'shimindaily'

            # Try to get article count
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, dict):
                        articles = data.get('article_count', len(data.get('articles', [])))
                    elif isinstance(data, list):
                        articles = len(data)
            except:
                pass

            files_info.append({
                'path': file_path,
                'source': source,
                'size_mb': size_mb,
                'articles': articles,
                'modified': modified
            })

        except Exception:
            continue

    # Sort by modification time (newest first)
    files_info.sort(key=lambda x: x['modified'], reverse=True)

    if not files_info:
        click.echo("No data files found.")
        return

    for i, file_info in enumerate(files_info[:limit]):
        click.echo(f"{i+1:2d}. {file_info['path'].name}")
        click.echo(f"     Source: {file_info['source']}")
        click.echo(f"     Size: {file_info['size_mb']:.2f} MB")
        click.echo(f"     Articles: {file_info['articles']}")
        click.echo(f"     Modified: {file_info['modified'].strftime('%Y-%m-%d %H:%M:%S')}")
        click.echo()
