"""
CLI commands for uploading rephrased articles to API server.
"""

import asyncio
import click
import logging
from datetime import datetime, date
from pathlib import Path
from typing import Optional

from src.api.services.upload_service import ApiUploadService
from src.api.clients.http_client import ApiHttpClient
from config.settings import get_settings

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@click.group()
def upload():
    """Upload rephrased articles to API server."""
    pass


@upload.command()
@click.option('--source', default='macaodaily', help='Source name (default: macaodaily)')
@click.option('--date', type=click.DateTime(formats=['%Y-%m-%d', '%Y%m%d']),
              default=None, help='Date to upload (YYYY-MM-DD or YYYYMMDD)')
@click.option('--file', type=click.Path(exists=True), help='Specific file to upload')
@click.option('--limit', type=int, default=None, help='Maximum number of articles to upload')
@click.option('--dry-run', is_flag=True, help='Show what would be uploaded without actually uploading')
def single(source: str, date: Optional[datetime], file: Optional[str], limit: Optional[int], dry_run: bool):
    """Upload articles for a single date or file."""
    
    async def _upload_single():
        settings = get_settings()
        
        # Validate API configuration
        if not settings.api.api_url:
            click.echo("❌ API URL not configured. Set NEWS_API_URL environment variable.", err=True)
            return

        if not settings.api.token:
            click.echo("❌ API authentication not configured. Set NEWS_API_AUTH_TOKEN environment variable.", err=True)
            return
        
        upload_service = ApiUploadService()
        
        if file:
            # Upload specific file
            file_path = Path(file)
            click.echo(f"📁 Uploading file: {file_path}")
            
            if dry_run:
                click.echo("🔍 DRY RUN - Would upload this file")
                if limit:
                    click.echo(f"🔢 Would limit to {limit} articles")
                return

            result = await upload_service.upload_rephrased_file(file_path, limit=limit)
            _display_upload_result(result)
            
        else:
            # Upload by date
            target_date = date.date() if date else datetime.now().date()
            date_str = target_date.strftime('%Y%m%d')
            year = target_date.strftime('%Y')
            
            file_path = (
                settings.data.processed_data_path / 
                "rephrased" / source / year / f"{date_str}.json"
            )
            
            if not file_path.exists():
                click.echo(f"❌ No rephrased file found for {target_date}: {file_path}", err=True)
                return
            
            click.echo(f"📅 Uploading {source} articles for {target_date}")
            
            if dry_run:
                click.echo("🔍 DRY RUN - Would upload articles from this date")
                if limit:
                    click.echo(f"🔢 Would limit to {limit} articles")
                return

            result = await upload_service.upload_rephrased_file(file_path, limit=limit)
            _display_upload_result(result)
    
    asyncio.run(_upload_single())


@upload.command()
@click.option('--source', default='macaodaily', help='Source name (default: macaodaily)')
@click.option('--start-date', type=click.DateTime(formats=['%Y-%m-%d', '%Y%m%d']), 
              required=True, help='Start date (YYYY-MM-DD or YYYYMMDD)')
@click.option('--end-date', type=click.DateTime(formats=['%Y-%m-%d', '%Y%m%d']), 
              required=True, help='End date (YYYY-MM-DD or YYYYMMDD)')
@click.option('--dry-run', is_flag=True, help='Show what would be uploaded without actually uploading')
def range(source: str, start_date: datetime, end_date: datetime, dry_run: bool):
    """Upload articles for a date range."""
    
    async def _upload_range():
        settings = get_settings()
        
        # Validate API configuration
        if not settings.api.api_url:
            click.echo("❌ API URL not configured. Set NEWS_API_URL environment variable.", err=True)
            return

        if not settings.api.token:
            click.echo("❌ API authentication not configured. Set NEWS_API_AUTH_TOKEN environment variable.", err=True)
            return
        
        start = start_date.date()
        end = end_date.date()
        
        if start > end:
            click.echo("❌ Start date must be before or equal to end date", err=True)
            return
        
        click.echo(f"📅 Uploading {source} articles from {start} to {end}")
        
        if dry_run:
            click.echo("🔍 DRY RUN - Would upload articles from this date range")
            return
        
        upload_service = ApiUploadService()
        results = await upload_service.upload_date_range(source, start, end)
        
        # Display summary
        total_successful = sum(r.successful_uploads for r in results)
        total_failed = sum(r.failed_uploads for r in results)
        total_skipped = sum(r.skipped_uploads for r in results)
        
        click.echo(f"\n📊 Upload Summary:")
        click.echo(f"   ✅ Successful: {total_successful}")
        click.echo(f"   ❌ Failed: {total_failed}")
        click.echo(f"   ⏭️  Skipped: {total_skipped}")
        click.echo(f"   📁 Files processed: {len(results)}")
    
    asyncio.run(_upload_range())


@upload.command()
@click.option('--test-only', is_flag=True, help='Only test connection without uploading')
def test(test_only: bool):
    """Test API connection and configuration."""
    
    async def _test_connection():
        settings = get_settings()
        
        click.echo("🔧 Testing API configuration...")
        
        # Check configuration
        if not settings.api.api_url:
            click.echo("❌ API URL not configured", err=True)
            return

        if not settings.api.token:
            click.echo("❌ API authentication not configured", err=True)
            return
        
        click.echo(f"🌐 API URL: {settings.api.full_url}")
        click.echo(f"📦 Batch Size: {settings.api.batch_size}")
        
        # Test connection
        async with ApiHttpClient() as client:
            success = await client.test_connection()
            
            if success:
                click.echo("✅ API connection successful!")
            else:
                click.echo("❌ API connection failed", err=True)
    
    asyncio.run(_test_connection())


@upload.command()
def status():
    """Show upload status and statistics."""
    upload_service = ApiUploadService()
    stats = upload_service.get_upload_statistics()
    
    click.echo("📊 Upload Statistics:")
    click.echo(f"   📄 Total articles: {stats['total_articles']}")
    click.echo(f"   ✅ Uploaded: {stats['uploaded']}")
    click.echo(f"   ❌ Failed: {stats['failed']}")
    click.echo(f"   ⏳ Pending: {stats['pending']}")
    click.echo(f"   📈 Success rate: {stats['success_rate']:.1f}%")


@upload.command()
@click.confirmation_option(prompt='Are you sure you want to reset all failed uploads?')
def reset_failed():
    """Reset failed uploads to pending status."""
    upload_service = ApiUploadService()
    reset_count = upload_service.reset_failed_uploads()
    
    if reset_count > 0:
        click.echo(f"✅ Reset {reset_count} failed uploads to pending")
    else:
        click.echo("ℹ️  No failed uploads to reset")


def _display_upload_result(result):
    """Display upload result in a formatted way."""
    click.echo(f"\n📊 Upload Result:")
    click.echo(f"   📄 Total articles: {result.total_articles}")
    click.echo(f"   ✅ Successful: {result.successful_uploads}")
    click.echo(f"   ❌ Failed: {result.failed_uploads}")
    click.echo(f"   ⏭️  Skipped: {result.skipped_uploads}")
    
    if result.errors:
        click.echo(f"   🚨 Errors:")
        for error in result.errors:
            click.echo(f"      • {error}")
    
    duration = (result.completed_at - result.started_at).total_seconds()
    click.echo(f"   ⏱️  Duration: {duration:.1f} seconds")


if __name__ == '__main__':
    upload()
