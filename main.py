#!/usr/bin/env python3
"""
Main execution script for the news scraping project.
Reads configuration and runs all enabled scrapers.
"""

import logging
import sys
import importlib
from datetime import datetime
from typing import List

from config.settings import get_settings
from src.core.models.article import ScrapedArticle
from src.scraping.utils.storage import DataStorage


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('scraper.log', encoding='utf-8')
        ]
    )


def load_scraper_class(module_name: str, class_name: str):
    """
    Dynamically load a scraper class from module.
    """
    try:
        module = importlib.import_module(module_name)
        scraper_class = getattr(module, class_name)
        return scraper_class
    except (ImportError, AttributeError) as e:
        logging.error(f"Failed to load scraper {class_name} from {module_name}: {e}")
        return None


def run_scraper(scraper_config: dict, storage: DataStorage) -> List[ScrapedArticle]:
    """
    Run a single scraper based on configuration.
    """
    scraper_name = scraper_config['name']
    logger = logging.getLogger(f"main.{scraper_name}")
    
    try:
        # Load scraper class
        scraper_class = load_scraper_class(
            scraper_config['module'],
            scraper_config['class_name']
        )
        
        if not scraper_class:
            return []
        
        # Create scraper instance with parameters
        if scraper_config['name'] == 'macaodaily':
            # Pass target_date parameter for Macao Daily scraper
            target_date = scraper_config.get('target_date')
            scraper = scraper_class(target_date=target_date)
        else:
            scraper = scraper_class()
        
        # Get existing URLs to avoid duplicates
        existing_urls = storage.get_existing_urls(scraper_name)
        logger.info(f"Found {len(existing_urls)} existing URLs for {scraper_name}")
        
        # Run scraper
        logger.info(f"Starting scraper: {scraper_name}")
        if scraper_config['name'] == 'macaodaily':
            # Macao Daily scraper uses date-based URL generation
            articles = scraper.scrape()
        else:
            # Other scrapers use provided URL
            articles = scraper.scrape(scraper_config.get('url'))
        
        # Filter out duplicates
        new_articles = []
        for article in articles:
            if article.original_url not in existing_urls:
                new_articles.append(article)
            else:
                logger.debug(f"Skipping duplicate URL: {article.original_url}")
        
        logger.info(f"Scraped {len(new_articles)} new articles (filtered {len(articles) - len(new_articles)} duplicates)")
        
        # Save articles
        if new_articles:
            filepath = storage.save_articles(new_articles, scraper_name)
            logger.info(f"Saved articles to: {filepath}")
        else:
            logger.info("No new articles to save")
        
        return new_articles
        
    except Exception as e:
        logger.error(f"Error running scraper {scraper_name}: {e}")
        return []


def main():
    """Main execution function."""
    setup_logging()
    logger = logging.getLogger("main")
    
    logger.info("Starting news scraping session")

    # Get settings and scraper configurations
    settings = get_settings()
    from config.settings import load_scraper_config
    scrapers_config = load_scraper_config()

    logger.info(f"Configuration: {len(scrapers_config)} scrapers configured")

    # Initialize storage
    storage = DataStorage()

    # Track results
    total_articles = 0
    successful_scrapers = 0

    # Run each enabled scraper
    for scraper_config in scrapers_config:
        if not scraper_config.get('enabled', True):
            logger.info(f"Skipping disabled scraper: {scraper_config['name']}")
            continue
        
        try:
            articles = run_scraper(scraper_config, storage)
            total_articles += len(articles)
            if articles:
                successful_scrapers += 1
                
        except Exception as e:
            logger.error(f"Unexpected error with scraper {scraper_config['name']}: {e}")
            continue
    
    # Summary
    logger.info(f"Scraping session completed:")
    logger.info(f"  - Total new articles: {total_articles}")
    logger.info(f"  - Successful scrapers: {successful_scrapers}/{len([s for s in scrapers_config if s.get('enabled', True)])}")
    
    # Cleanup old files (keep last 10 files per scraper)
    try:
        storage.cleanup_old_files(keep_count=10)
    except Exception as e:
        logger.warning(f"Error during cleanup: {e}")
    
    return total_articles


def list_data_files():
    """List all scraped data files."""
    storage = DataStorage()
    files = storage.list_all_files()
    
    print("\nScraped Data Files:")
    print("-" * 80)
    print(f"{'Filename':<30} {'Scraper':<15} {'Articles':<8} {'Date':<20}")
    print("-" * 80)
    
    for file_info in files:
        timestamp = file_info['scrape_timestamp']
        if timestamp:
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                date_str = dt.strftime('%Y-%m-%d %H:%M')
            except:
                date_str = timestamp[:16]
        else:
            date_str = 'Unknown'
        
        print(f"{file_info['filename']:<30} {file_info['scraper_name']:<15} {file_info['article_count']:<8} {date_str:<20}")
    
    print(f"\nTotal files: {len(files)}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "list":
        list_data_files()
    else:
        main()
