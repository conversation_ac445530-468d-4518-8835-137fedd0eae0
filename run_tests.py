#!/usr/bin/env python3
"""
Test runner script for the news scraping system.
"""

import sys
import subprocess
from pathlib import Path


def run_tests():
    """Run the test suite with different configurations."""
    
    print("🧪 Running News Scraping System Test Suite")
    print("=" * 50)
    
    # Test configurations
    test_configs = [
        {
            "name": "Unit Tests",
            "command": ["python", "-m", "pytest", "tests/unit/", "-v", "--tb=short"],
            "description": "Fast unit tests for core functionality"
        },
        {
            "name": "Integration Tests", 
            "command": ["python", "-m", "pytest", "tests/integration/", "-v", "--tb=short"],
            "description": "Integration tests for system components"
        },
        {
            "name": "All Tests with Coverage",
            "command": ["python", "-m", "pytest", "tests/", "-v", "--cov=src", "--cov=cli", "--cov-report=term-missing"],
            "description": "Complete test suite with coverage report"
        },
        {
            "name": "Quick Test",
            "command": ["python", "-m", "pytest", "tests/unit/test_models.py", "-v"],
            "description": "Quick smoke test for models"
        }
    ]
    
    # Check if pytest is available
    try:
        subprocess.run(["python", "-m", "pytest", "--version"], 
                      capture_output=True, check=True)
    except subprocess.CalledProcessError:
        print("❌ pytest is not installed. Please install it with:")
        print("   pip install pytest pytest-cov pytest-mock")
        return False
    
    # Run tests based on command line argument
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == "unit":
            config = test_configs[0]
        elif test_type == "integration":
            config = test_configs[1]
        elif test_type == "coverage":
            config = test_configs[2]
        elif test_type == "quick":
            config = test_configs[3]
        else:
            print(f"❌ Unknown test type: {test_type}")
            print("Available options: unit, integration, coverage, quick")
            return False
        
        configs_to_run = [config]
    else:
        # Run all tests by default
        configs_to_run = test_configs[:3]  # Skip quick test in full run
    
    # Run selected test configurations
    all_passed = True
    
    for config in configs_to_run:
        print(f"\n🔍 {config['name']}")
        print(f"   {config['description']}")
        print("-" * 50)
        
        try:
            result = subprocess.run(config["command"], cwd=Path.cwd())
            
            if result.returncode == 0:
                print(f"✅ {config['name']} passed!")
            else:
                print(f"❌ {config['name']} failed!")
                all_passed = False
                
        except Exception as e:
            print(f"❌ Error running {config['name']}: {e}")
            all_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed!")
        return True
    else:
        print("💥 Some tests failed!")
        return False


def main():
    """Main entry point."""
    success = run_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
