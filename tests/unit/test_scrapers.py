"""
Unit tests for scraper functionality.
"""

import pytest
from datetime import date
from bs4 import BeautifulSoup

from src.scraping.scrapers.base import BaseScraper
from src.core.exceptions import ScrapingException
from src.scraping.scrapers.macaodaily_scraper import MacaoDailyScraper


@pytest.mark.unit
class TestBaseScraper:
    """Test cases for BaseScraper."""

    def test_base_scraper_cannot_instantiate(self):
        """Test that BaseScraper cannot be instantiated directly."""
        # BaseScraper is abstract and cannot be instantiated
        with pytest.raises(TypeError):
            BaseScraper("test_source", "https://example.com")

    def test_base_scraper_abstract_methods(self):
        """Test that abstract methods must be implemented."""
        # BaseScraper is abstract, so we can't instantiate it directly
        # This test verifies the abstract nature
        from abc import ABC
        assert issubclass(BaseScraper, ABC)

    def test_scraper_exception_handling(self):
        """Test scraper exception handling."""
        # Test that ScrapingException can be raised
        with pytest.raises(ScrapingException):
            raise ScrapingException("Test error")


@pytest.mark.unit
class TestMacaoDailyScraper:
    """Test cases for MacaoDailyScraper."""

    def test_macaodaily_scraper_initialization(self):
        """Test MacaoDailyScraper initialization."""
        scraper = MacaoDailyScraper()

        assert scraper.source_site_name == "澳門日報"
        assert scraper.source_site_url == "http://www.macaodaily.com/"
        assert scraper.target_date == date.today()

    def test_macaodaily_scraper_with_date(self):
        """Test MacaoDailyScraper initialization with specific date."""
        scraper = MacaoDailyScraper("2025-07-01")

        assert scraper.target_date == date(2025, 7, 1)

    def test_get_index_url(self):
        """Test index URL generation for different dates."""
        scraper = MacaoDailyScraper("2025-07-01")

        url = scraper.get_index_url()
        expected_url = "http://www.macaodaily.com/html/2025-07/01/node_1.htm"
        assert url == expected_url

    def test_get_index_url_different_dates(self):
        """Test index URL generation for various dates."""
        test_cases = [
            ("2025-01-01", "http://www.macaodaily.com/html/2025-01/01/node_1.htm"),
            ("2025-12-31", "http://www.macaodaily.com/html/2025-12/31/node_1.htm"),
            ("2024-02-29", "http://www.macaodaily.com/html/2024-02/29/node_1.htm"),
        ]

        for date_str, expected_url in test_cases:
            scraper = MacaoDailyScraper(date_str)
            url = scraper.get_index_url()
            assert url == expected_url
    
    def test_invalid_date_handling(self):
        """Test handling of invalid date format."""
        scraper = MacaoDailyScraper("invalid-date")

        # Should fall back to today's date
        assert scraper.target_date == date.today()
    
    def test_html_parsing_basics(self):
        """Test basic HTML parsing functionality."""
        # Test that BeautifulSoup parsing works as expected
        html = """
        <div id="all_article_list">
            <h4>A01：澳聞</h4>
            <a href="/article1.htm">Article 1</a>
            <h4>A02：要聞</h4>
            <a href="/article2.htm">Article 2</a>
        </div>
        """

        soup = BeautifulSoup(html, 'html.parser')

        # Test category parsing logic
        categories = soup.find_all('h4')
        assert len(categories) == 2
        assert "A01：澳聞" in categories[0].text
        assert "A02：要聞" in categories[1].text

        # Test link extraction logic
        links = soup.find_all('a')
        assert len(links) == 2
        assert links[0]['href'] == "/article1.htm"
        assert links[1]['href'] == "/article2.htm"
