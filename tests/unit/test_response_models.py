"""
Unit tests for structured response models.
"""

import pytest
from pydantic import ValidationError
from src.rephrasing.models.response_models import (
    RephrasedContent, ArticleSummary, ArticleClassification,
    QualityAssessment, TitleOptimization
)


def test_rephrased_content():
    """Test RephrasedContent model."""
    
    data = {
        "title": "澳門經濟發展迎來新機遇",
        "slug": "macau-economic-development-opportunities-2025",
        "summary": "澳門特別行政區政府宣布新的經濟發展措施，促進中小企業發展並推動科技創新。",
        "content": "# 澳門經濟發展迎來新機遇\n\n澳門特別行政區政府今日正式宣布一系列全新的經濟發展措施，旨在促進本地經濟多元化發展。",
        "keywords": ["澳門", "經濟發展", "政府政策", "中小企業", "科技創新"],
        "categories": ["economy", "politics"],
        "meta_title": "澳門經濟發展新政策 | 中小企業創新機遇 2025",
        "meta_description": "澳門政府推出全新經濟發展措施，重點支持中小企業發展和科技創新，為澳門經濟注入新活力，促進多元化發展。"
    }
    
    # Test creation from dict
    rephrased = RephrasedContent(**data)
    assert rephrased.title == "澳門經濟發展迎來新機遇"
    assert rephrased.slug == "macau-economic-development-opportunities-2025"
    assert len(rephrased.keywords) == 5
    assert len(rephrased.categories) == 2
    assert rephrased.meta_title
    assert rephrased.meta_description

    # Test JSON serialization
    json_str = rephrased.model_dump_json()
    assert len(json_str) > 0

    # Test deserialization
    rephrased_2 = RephrasedContent.model_validate_json(json_str)
    assert rephrased_2.title == rephrased.title
    assert rephrased_2.slug == rephrased.slug


def test_article_summary():
    """Test ArticleSummary model."""
    data = {
        "summary": "澳門政府推出新經濟政策，支持中小企業發展和科技創新。",
        "key_points": [
            "政府宣布新經濟發展措施",
            "重點支持中小企業發展",
            "推動科技創新項目",
            "預計為經濟注入新活力"
        ]
    }

    summary = ArticleSummary(**data)
    assert summary.summary == "澳門政府推出新經濟政策，支持中小企業發展和科技創新。"
    assert len(summary.key_points) == 4


def test_article_classification():
    """Test ArticleClassification model."""
    data = {
        "primary_category": "經濟",
        "secondary_categories": ["政治", "澳聞"],
        "confidence_score": 0.9,
        "reasoning": "文章主要討論政府經濟政策，涉及澳門本地新聞"
    }

    classification = ArticleClassification(**data)
    assert classification.primary_category == "經濟"
    assert classification.confidence_score == 0.9
    assert len(classification.secondary_categories) == 2


def test_quality_assessment():
    """Test QualityAssessment model."""
    data = {
        "factual_accuracy": 9,
        "language_fluency": 8,
        "structural_clarity": 8,
        "readability_improvement": 7,
        "overall_quality": 8,
        "feedback": "改寫質量良好，保持了事實準確性，語言更加流暢。建議進一步優化段落結構。"
    }

    assessment = QualityAssessment(**data)
    assert assessment.overall_quality == 8
    assert assessment.factual_accuracy == 9
    assert assessment.feedback


def test_title_optimization():
    """Test TitleOptimization model."""
    data = {
        "optimized_title": "澳門推出重磅經濟新政，中小企業迎發展良機",
        "alternative_titles": [
            "澳門經濟新政策正式啟動，助力企業創新發展",
            "政府力推經濟改革，澳門中小企業受惠"
        ],
        "improvement_notes": "增加了吸引力詞彙，突出了政策影響和受益群體"
    }

    optimization = TitleOptimization(**data)
    assert optimization.optimized_title
    assert len(optimization.alternative_titles) == 2


def test_validation_errors():
    """Test validation errors."""

    # Test invalid confidence score
    with pytest.raises(ValidationError):
        ArticleClassification(
            primary_category="經濟",
            secondary_categories=["政治"],
            confidence_score=1.5,  # Invalid: > 1.0
            reasoning="Test"
        )

    # Test invalid quality score
    with pytest.raises(ValidationError):
        QualityAssessment(
            factual_accuracy=11,  # Invalid: > 10
            language_fluency=8,
            structural_clarity=8,
            readability_improvement=7,
            overall_quality=8,
            feedback="Test"
        )
