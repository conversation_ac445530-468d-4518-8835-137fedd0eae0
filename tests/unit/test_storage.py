"""
Unit tests for storage utilities.
"""

import pytest
import json
from pathlib import Path
from datetime import date

from src.scraping.utils.storage import save_articles, load_articles, get_existing_urls


@pytest.mark.unit
class TestStorage:
    """Test cases for storage utilities."""
    
    def test_save_articles(self, temp_dir, sample_articles, mock_settings):
        """Test saving articles to file."""
        source = "test_source"
        target_date = "2025-07-01"

        file_path_str = save_articles(sample_articles, source, target_date)
        file_path = Path(file_path_str)

        # Check file was created
        assert file_path.exists()
        assert file_path.name == "20250701.json"

        # Check file content
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        assert data["scraper_name"] == source
        assert data["target_date"] == target_date
        assert data["article_count"] == len(sample_articles)
        assert len(data["articles"]) == len(sample_articles)

        # Check first article
        first_article = data["articles"][0]
        assert first_article["title"] == "Test Article 1"
        assert first_article["source_site_name"] == "test_source"
    
    def test_save_articles_creates_directory(self, sample_articles, mock_settings):
        """Test that save_articles creates necessary directories."""
        source = "new_source"
        target_date = "2025-07-01"

        file_path_str = save_articles(sample_articles, source, target_date)
        file_path = Path(file_path_str)

        # File should be created
        assert file_path.exists()
    
    def test_load_articles(self, sample_json_file):
        """Test loading articles from file."""
        articles = load_articles(sample_json_file)
        
        assert len(articles) == 3
        assert articles[0].title == "Test Article 1"
        assert articles[0].source_site_name == "test_source"
        assert articles[1].title == "Test Article 2"
        assert articles[2].title == "Test Article 3"
    
    def test_load_articles_nonexistent_file(self, temp_dir):
        """Test loading articles from non-existent file."""
        nonexistent_file = temp_dir / "nonexistent.json"

        # Should raise FileNotFoundError for non-existent file
        with pytest.raises(FileNotFoundError):
            load_articles(str(nonexistent_file))

    def test_load_articles_invalid_json(self, temp_dir):
        """Test loading articles from invalid JSON file."""
        invalid_file = temp_dir / "invalid.json"
        with open(invalid_file, 'w') as f:
            f.write("invalid json content")

        # Should raise JSONDecodeError for invalid JSON
        with pytest.raises(json.JSONDecodeError):
            load_articles(str(invalid_file))
    
    def test_get_existing_urls_no_files(self, mock_settings):
        """Test getting existing URLs when no files exist."""
        existing_urls = get_existing_urls("nonexistent_source")
        assert existing_urls == set()

    def test_save_empty_articles_list(self, mock_settings):
        """Test saving empty articles list."""
        source = "test_source"
        target_date = "2025-07-01"

        file_path_str = save_articles([], source, target_date)

        # Should return None for empty list (no file created)
        assert file_path_str is None

    def test_storage_functions_exist(self):
        """Test that storage functions are importable and callable."""
        # Test that the functions exist and are callable
        assert callable(save_articles)
        assert callable(load_articles)
        assert callable(get_existing_urls)
