"""
Unit tests for data models.
"""

import pytest
from datetime import datetime, date

from src.core.models.article import ScrapedArticle, ArticleImage


@pytest.mark.unit
class TestScrapedArticle:
    """Test cases for ScrapedArticle model."""

    def test_create_valid_article(self):
        """Test creating a valid article."""
        article = ScrapedArticle(
            title="Test Article",
            content="This is test content.",
            source_url="https://example.com/test",
            author="Test Author",
            publish_date=datetime(2025, 7, 1, 10, 0, 0),
            source_site_name="test_source",
            tags=["test", "article"],
            images=[]
        )

        assert article.title == "Test Article"
        assert article.content == "This is test content."
        assert article.source_url == "https://example.com/test"
        assert article.author == "Test Author"
        assert article.source_site_name == "test_source"
        assert article.tags == ["test", "article"]
        assert article.images == []
        assert article.id is not None
    
    def test_article_with_images(self):
        """Test creating an article with images."""
        images = [
            ArticleImage(
                src="https://example.com/image1.jpg",
                description="Test image 1"
            ),
            ArticleImage(
                src="https://example.com/image2.jpg",
                description="Test image 2"
            )
        ]

        article = ScrapedArticle(
            title="Test Article",
            content="This is test content.",
            source_url="https://example.com/test",
            author="Test Author",
            publish_date=datetime(2025, 7, 1, 10, 0, 0),
            source_site_name="test_source",
            tags=["test"],
            images=images
        )

        assert len(article.images) == 2
        assert article.images[0].src == "https://example.com/image1.jpg"
        assert article.images[0].description == "Test image 1"
    
    def test_article_default_values(self):
        """Test article creation with default values."""
        # Dataclasses allow creation with defaults
        article = ScrapedArticle()

        assert article.title == ""
        assert article.content == ""
        assert article.source_url == ""
        assert article.author is None
        assert article.source_site_name == ""
        assert article.tags == []
        assert article.images == []
        assert article.id is not None
    
    def test_article_serialization(self):
        """Test article serialization to dict."""
        article = ScrapedArticle(
            title="Test Article",
            content="This is test content.",
            source_url="https://example.com/test",
            author="Test Author",
            publish_date=datetime(2025, 7, 1, 10, 0, 0),
            source_site_name="test_source",
            tags=["test", "article"],
            images=[]
        )

        data = article.to_dict()

        assert data["title"] == "Test Article"
        assert data["content"] == "This is test content."
        assert data["source_url"] == "https://example.com/test"
        assert data["source_site_name"] == "test_source"
        assert data["tags"] == ["test", "article"]
    
    def test_article_from_dict(self):
        """Test creating article from dictionary using from_dict method."""
        data = {
            "title": "Test Article",
            "content": "This is test content.",
            "source_url": "https://example.com/test",
            "author": "Test Author",
            "publish_date": "2025-07-01T10:00:00",
            "source_site_name": "test_source",
            "tags": ["test", "article"],
            "images": []
        }

        article = ScrapedArticle.from_dict(data)

        assert article.title == "Test Article"
        assert article.content == "This is test content."
        assert article.source_url == "https://example.com/test"
        assert article.source_site_name == "test_source"
    
    def test_article_content_conversion(self):
        """Test automatic content format conversion."""
        article = ScrapedArticle(
            title="Test Article",
            content_html="<p>This is <strong>test</strong> content.</p>",
            source_url="https://example.com/test",
            author="Test Author",
            publish_date=datetime(2025, 7, 1, 10, 0, 0),
            source_site_name="test_source",
            tags=["test"]
        )

        # Should automatically convert HTML to markdown and text
        assert article.content_markdown != ""
        assert article.content_text != ""
        assert "test" in article.content_text


@pytest.mark.unit
class TestArticleImage:
    """Test cases for ArticleImage model."""
    
    def test_create_valid_image(self):
        """Test creating a valid image."""
        image = ArticleImage(
            src="https://example.com/image.jpg",
            description="Test image"
        )
        
        assert image.src == "https://example.com/image.jpg"
        assert image.description == "Test image"
    
    def test_image_creation_with_required_field(self):
        """Test image creation requires src field."""
        # ArticleImage requires src parameter
        try:
            ArticleImage()
            assert False, "Should require src parameter"
        except TypeError:
            pass  # Expected behavior for dataclass with required field

    def test_image_serialization(self):
        """Test image serialization."""
        image = ArticleImage(
            src="https://example.com/image.jpg",
            description="Test image"
        )

        # Test dataclass fields
        assert image.src == "https://example.com/image.jpg"
        assert image.description == "Test image"
        assert image.alt_text is None
        assert image.width is None
        assert image.height is None
