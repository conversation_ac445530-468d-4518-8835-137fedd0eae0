"""
Unit tests for API integration components.
"""

import pytest
import json
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

from src.api.models.api_schemas import (
    PostSchema, RequestSchema, FeaturedImageSchema
)
from src.api.services.data_transformer import DataTransformer
from src.api.clients.http_client import ApiHttpClient
from src.api.services.upload_service import ApiUploadService
from src.core.models.rephrased_article import RephrasedArticle


class TestDataTransformer:
    """Test data transformation functionality."""
    
    def test_transform_basic_article(self):
        """Test basic article transformation."""
        transformer = DataTransformer()
        
        # Create a mock rephrased article
        article = RephrasedArticle(
            id="test-123",
            title="Test Article",
            slug="test-article",
            content="# Test Content\n\nThis is test content.",
            summary="Test summary",
            source_url="https://example.com/article",
            source_site_name="Test Site",
            processing_timestamp=datetime.now(),
            categories=["news", "test"],
            meta_title="Test Meta Title",
            meta_description="Test meta description"
        )
        
        # Transform article
        post = transformer.transform_article(article)
        
        # Assertions
        assert isinstance(post, PostSchema)
        assert post.title == "Test Article"
        assert post.slug == "test-article"
        assert post.content == "# Test Content\n\nThis is test content."
        assert post.summary == "Test summary"
        assert post.source_url == "https://example.com/article"
        assert post.source_site_name == "Test Site"
        assert post.categories == ["news", "test"]
        assert post.status == "draft"
        assert post.meta.title == "Test Meta Title"
        assert post.meta.description == "Test meta description"
    
    def test_transform_article_with_images(self):
        """Test article transformation with images."""
        transformer = DataTransformer()
        
        article = RephrasedArticle(
            id="test-123",
            title="Test Article",
            slug="test-article",
            content="Test content"
        )
        # Add images attribute dynamically
        article.images = [
            {"src": "https://example.com/image.jpg", "alt": "Test image"}
        ]
        
        with patch.object(transformer.image_processor, 'process_image') as mock_process:
            mock_process.return_value = FeaturedImageSchema(
                type="external",
                data="https://example.com/image.jpg",
                alt="Test image"
            )
            
            post = transformer.transform_article(article)
            
            assert post.featuredImage is not None
            assert post.featuredImage.type == "external"
            assert post.featuredImage.data == "https://example.com/image.jpg"
            assert post.featuredImage.alt == "Test image"

    def test_transform_article_with_base64_images(self):
        """Test article transformation with base64 images."""
        transformer = DataTransformer()

        article = RephrasedArticle(
            id="test-123",
            title="Test Article",
            slug="test-article",
            content="Test content"
        )
        # Add images attribute dynamically
        article.images = [
            {"src": "https://example.com/image.jpg", "alt": "Test image"}
        ]

        with patch.object(transformer.image_processor, 'process_image') as mock_process:
            mock_process.return_value = FeaturedImageSchema(
                type="base64",
                data="data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAA...",
                filename="test-article.jpg",
                alt="Test image"
            )

            post = transformer.transform_article(article)

            # Verify the process_image method was called with the slug
            mock_process.assert_called_once_with({"src": "https://example.com/image.jpg", "alt": "Test image"}, "test-article")

            assert post.featuredImage is not None
            assert post.featuredImage.type == "base64"
            assert post.featuredImage.data.startswith("data:image/jpeg;base64,")
            assert post.featuredImage.filename == "test-article.jpg"
            assert post.featuredImage.alt == "Test image"
    
    def test_transform_articles_batch(self):
        """Test batch transformation."""
        transformer = DataTransformer()
        
        articles = [
            RephrasedArticle(
                id="test-1",
                title="Article 1",
                slug="article-1",
                content="Content 1"
            ),
            RephrasedArticle(
                id="test-2",
                title="Article 2",
                slug="article-2",
                content="Content 2"
            )
        ]
        
        request = transformer.transform_articles_batch(articles)
        
        assert isinstance(request, RequestSchema)
        assert len(request.posts) == 2
        assert request.posts[0].title == "Article 1"
        assert request.posts[1].title == "Article 2"


class TestApiHttpClient:
    """Test HTTP client functionality."""
    
    @pytest.mark.asyncio
    async def test_successful_upload(self):
        """Test successful API upload."""
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # Mock successful response
            mock_response = Mock()
            mock_response.is_success = True
            mock_response.status_code = 200
            mock_response.json.return_value = {"message": "Success", "data": {"created": 2}}
            mock_client.request.return_value = mock_response
            
            client = ApiHttpClient()
            await client._initialize_client()
            
            request_data = RequestSchema(posts=[
                PostSchema(title="Test", slug="test", content="Test content")
            ])
            
            response = await client.upload_posts(request_data)
            
            assert response.success is True
            assert response.message == "Success"
            assert response.data["created"] == 2
    
    @pytest.mark.asyncio
    async def test_failed_upload(self):
        """Test failed API upload."""
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # Mock error response
            mock_response = Mock()
            mock_response.is_success = False
            mock_response.status_code = 400
            mock_response.json.return_value = {
                "message": "Validation error",
                "errors": ["Invalid slug"]
            }
            mock_client.request.return_value = mock_response
            
            client = ApiHttpClient()
            await client._initialize_client()
            
            request_data = RequestSchema(posts=[
                PostSchema(title="Test", slug="", content="Test content")
            ])
            
            response = await client.upload_posts(request_data)
            
            assert response.success is False
            assert response.message == "Validation error"
            assert "Invalid slug" in response.errors


class TestApiUploadService:
    """Test upload service functionality."""
    
    def test_load_rephrased_articles(self, tmp_path):
        """Test loading rephrased articles from file."""
        service = ApiUploadService()
        
        # Create test data
        test_data = {
            "source": "test",
            "target_date": "20240101",
            "articles": [
                {
                    "id": "test-1",
                    "title": "Test Article 1",
                    "slug": "test-article-1",
                    "content": "Test content 1",
                    "summary": "Test summary 1",
                    "source_url": "https://example.com/1",
                    "source_site_name": "Test Site",
                    "processing_timestamp": "2024-01-01T12:00:00",
                    "categories": ["news"],
                    "tags": ["test"],
                    "keywords": ["test"],
                    "priority_score": 0.8,
                    "quality_score": 0.9,
                    "meta_title": "Test Meta 1",
                    "meta_description": "Test meta description 1"
                },
                {
                    "id": "test-2",
                    "title": "Test Article 2",
                    "slug": "test-article-2",
                    "content": "Test content 2",
                    "summary": "Test summary 2",
                    "source_url": "https://example.com/2",
                    "source_site_name": "Test Site",
                    "processing_timestamp": "2024-01-01T12:00:00",
                    "categories": ["tech"],
                    "tags": ["test"],
                    "keywords": ["test"],
                    "priority_score": 0.7,
                    "quality_score": 0.8,
                    "meta_title": "Test Meta 2",
                    "meta_description": "Test meta description 2"
                }
            ]
        }
        
        # Write test file
        test_file = tmp_path / "test.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, default=str)
        
        # Load articles
        articles = service._load_rephrased_articles(test_file)
        
        assert len(articles) == 2
        assert articles[0].id == "test-1"
        assert articles[0].title == "Test Article 1"
        assert articles[1].id == "test-2"
        assert articles[1].title == "Test Article 2"
    
    def test_filter_pending_articles(self):
        """Test filtering pending articles."""
        service = ApiUploadService()
        
        # Mock upload status
        service.upload_status = {
            "test-1": Mock(status="uploaded"),
            "test-2": Mock(status="failed"),
            "test-3": Mock(status="pending")
        }
        
        articles = [
            Mock(id="test-1"),  # Already uploaded
            Mock(id="test-2"),  # Failed (should be included)
            Mock(id="test-3"),  # Pending (should be included)
            Mock(id="test-4")   # Not in status (should be included)
        ]
        
        pending = service._filter_pending_articles(articles)
        
        assert len(pending) == 3
        assert pending[0].id == "test-2"
        assert pending[1].id == "test-3"
        assert pending[2].id == "test-4"


class TestImageProcessor:
    """Test image processing functionality."""
    
    def test_extract_image_url_from_dict(self):
        """Test extracting image URL from dictionary."""
        from src.api.utils.image_processor import ImageProcessor
        
        processor = ImageProcessor()
        
        # Test with 'src' field
        image_data = {"src": "https://example.com/image.jpg", "alt": "Test"}
        url = processor._extract_image_url(image_data)
        assert url == "https://example.com/image.jpg"
        
        # Test with 'url' field
        image_data = {"url": "https://example.com/image.png"}
        url = processor._extract_image_url(image_data)
        assert url == "https://example.com/image.png"
        
        # Test with string
        url = processor._extract_image_url("https://example.com/image.gif")
        assert url == "https://example.com/image.gif"
    
    def test_extract_alt_text(self):
        """Test extracting alt text from image data."""
        from src.api.utils.image_processor import ImageProcessor
        
        processor = ImageProcessor()
        
        # Test with 'alt' field
        image_data = {"src": "test.jpg", "alt": "Test image"}
        alt = processor._extract_alt_text(image_data)
        assert alt == "Test image"
        
        # Test with 'description' field
        image_data = {"src": "test.jpg", "description": "Test description"}
        alt = processor._extract_alt_text(image_data)
        assert alt == "Test description"
        
        # Test with no alt text
        image_data = {"src": "test.jpg"}
        alt = processor._extract_alt_text(image_data)
        assert alt == ""
    
    def test_is_valid_image_url(self):
        """Test image URL validation."""
        from src.api.utils.image_processor import ImageProcessor
        
        processor = ImageProcessor()
        
        # Valid URLs
        assert processor._is_valid_image_url("https://example.com/image.jpg")
        assert processor._is_valid_image_url("http://example.com/photo.png")
        assert processor._is_valid_image_url("https://example.com/pic.gif")
        
        # Invalid URLs
        assert not processor._is_valid_image_url("not-a-url")
        assert not processor._is_valid_image_url("ftp://example.com/image.jpg")
        assert not processor._is_valid_image_url("")


if __name__ == "__main__":
    pytest.main([__file__])
