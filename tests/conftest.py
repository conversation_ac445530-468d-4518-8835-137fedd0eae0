"""
Pytest configuration and shared fixtures.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, date
from unittest.mock import Mock, patch
import json

from src.core.models.article import ScrapedArticle
from config.settings import get_settings


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path)


@pytest.fixture
def sample_article():
    """Create a sample article for testing."""
    return ScrapedArticle(
        title="Test Article",
        content="This is test content for the article.",
        source_url="https://example.com/test-article",
        author="Test Author",
        publish_date=datetime(2025, 7, 1, 10, 0, 0),
        source_site_name="test_source",
        tags=["test", "article"],
        images=[]
    )


@pytest.fixture
def sample_articles():
    """Create multiple sample articles for testing."""
    return [
        ScrapedArticle(
            title=f"Test Article {i}",
            content=f"This is test content for article {i}.",
            source_url=f"https://example.com/test-article-{i}",
            author=f"Test Author {i}",
            publish_date=datetime(2025, 7, 1, 10, i, 0),
            source_site_name="test_source",
            tags=["test", f"article{i}"],
            images=[]
        )
        for i in range(1, 4)
    ]


@pytest.fixture
def mock_settings(temp_dir):
    """Mock settings with temporary directories."""
    with patch('config.settings.get_settings') as mock_get_settings:
        mock_settings = Mock()
        mock_settings.data.raw_data_path = temp_dir / "raw"
        mock_settings.data.processed_data_path = temp_dir / "processed"
        mock_settings.data.export_data_path = temp_dir / "exports"
        mock_settings.data.log_path = temp_dir / "logs"
        
        mock_settings.scraping.rate_limit = 1.0
        mock_settings.scraping.max_retries = 2
        mock_settings.scraping.timeout = 10
        
        mock_settings.rephrasing.model = "test-model"
        mock_settings.rephrasing.max_tokens = 1000
        mock_settings.rephrasing.temperature = 0.3
        
        # Create directories
        for path in [
            mock_settings.data.raw_data_path,
            mock_settings.data.processed_data_path,
            mock_settings.data.export_data_path,
            mock_settings.data.log_path
        ]:
            path.mkdir(parents=True, exist_ok=True)
        
        mock_get_settings.return_value = mock_settings
        yield mock_settings


@pytest.fixture
def sample_json_file(temp_dir, sample_articles):
    """Create a sample JSON file with articles."""
    file_path = temp_dir / "test_articles.json"
    
    data = {
        "source": "test_source",
        "target_date": "2025-07-01",
        "scraped_at": "2025-07-01T10:00:00",
        "article_count": len(sample_articles),
        "articles": [article.to_dict() for article in sample_articles]
    }
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2, default=str)
    
    return file_path


@pytest.fixture
def mock_requests():
    """Mock requests for HTTP calls."""
    with patch('requests.get') as mock_get:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = """
        <html>
            <body>
                <div id="all_article_list">
                    <h4>A01：澳聞</h4>
                    <a href="/article1.htm">Test Article 1</a>
                    <a href="/article2.htm">Test Article 2</a>
                </div>
            </body>
        </html>
        """
        mock_response.content = mock_response.text.encode('utf-8')
        mock_get.return_value = mock_response
        yield mock_get


@pytest.fixture
def mock_playwright():
    """Mock playwright for browser automation."""
    with patch('playwright.sync_api.sync_playwright') as mock_playwright:
        mock_context = Mock()
        mock_page = Mock()
        mock_browser = Mock()
        
        mock_page.content.return_value = """
        <html>
            <body>
                <div id="ozoom">
                    <h1>Test Article</h1>
                    <p>Test content</p>
                </div>
            </body>
        </html>
        """
        
        mock_browser.new_page.return_value = mock_page
        mock_context.chromium.launch.return_value = mock_browser
        mock_playwright.return_value.__enter__.return_value = mock_context
        
        yield mock_playwright


@pytest.fixture(autouse=True)
def setup_test_environment(mock_settings):
    """Automatically set up test environment for all tests."""
    # This fixture runs automatically for all tests
    pass


# Markers for different test types
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.slow = pytest.mark.slow
pytest.mark.scraper = pytest.mark.scraper
pytest.mark.cli = pytest.mark.cli
pytest.mark.config = pytest.mark.config
