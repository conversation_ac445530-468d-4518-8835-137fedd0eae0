"""
Integration tests for CLI commands.
"""

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import patch, <PERSON><PERSON>
import j<PERSON>
from pathlib import Path

from cli.__main__ import cli
from cli.status import status_cli
from cli.scrape import scrape_cli


@pytest.mark.integration
@pytest.mark.cli
class TestCLICommands:
    """Test cases for CLI commands."""
    
    def setup_method(self):
        """Set up test environment for each test."""
        self.runner = CliRunner()
    
    def test_cli_help(self):
        """Test CLI help command."""
        result = self.runner.invoke(cli, ['--help'])
        
        assert result.exit_code == 0
        assert "News scraping and processing CLI" in result.output
        assert "scrape" in result.output
        assert "status" in result.output
    
    def test_status_help(self):
        """Test status command help."""
        result = self.runner.invoke(status_cli, ['--help'])
        
        assert result.exit_code == 0
        assert "System status and statistics" in result.output
        assert "health" in result.output
        assert "stats" in result.output
        assert "files" in result.output
    
    def test_scrape_help(self):
        """Test scrape command help."""
        result = self.runner.invoke(scrape_cli, ['--help'])
        
        assert result.exit_code == 0
        assert "News scraping commands" in result.output
        assert "macaodaily" in result.output
        assert "today" in result.output
    
    @patch('cli.status.get_settings')
    def test_status_health_command(self, mock_get_settings, mock_settings):
        """Test status health command."""
        mock_get_settings.return_value = mock_settings
        
        result = self.runner.invoke(status_cli, ['health'])
        
        assert result.exit_code == 0
        assert "System Health Check" in result.output
        assert "Python version" in result.output
        assert "Required packages" in result.output
        assert "Data directories" in result.output
    
    @patch('cli.status.get_settings')
    def test_status_config_command(self, mock_get_settings, mock_settings):
        """Test status config command."""
        mock_get_settings.return_value = mock_settings
        
        result = self.runner.invoke(status_cli, ['config'])
        
        assert result.exit_code == 0
        assert "Current Configuration" in result.output
        assert "Data Paths" in result.output
        assert "Scraping Settings" in result.output
    
    @patch('cli.status.get_settings')
    def test_status_files_command_no_files(self, mock_get_settings, mock_settings):
        """Test status files command with no files."""
        mock_get_settings.return_value = mock_settings
        
        result = self.runner.invoke(status_cli, ['files'])
        
        assert result.exit_code == 0
        assert "Recent Data Files" in result.output
        assert "No files found" in result.output
    
    @patch('cli.status.get_settings')
    def test_status_stats_command_no_data(self, mock_get_settings, mock_settings):
        """Test status stats command with no data."""
        mock_get_settings.return_value = mock_settings
        
        result = self.runner.invoke(status_cli, ['stats'])
        
        assert result.exit_code == 0
        assert "Scraping Statistics" in result.output
        assert "No data found" in result.output
    
    @patch('src.scraping.scrapers.macaodaily_scraper.MacaoDailyScraper.scrape_articles')
    @patch('cli.scrape.get_settings')
    def test_scrape_macaodaily_dry_run(self, mock_get_settings, mock_scrape, mock_settings, sample_articles):
        """Test scrape macaodaily command in dry run mode."""
        mock_get_settings.return_value = mock_settings
        mock_scrape.return_value = sample_articles
        
        result = self.runner.invoke(cli, ['--dry-run', 'scrape', 'macaodaily', '--date', '2025-07-01'])
        
        assert result.exit_code == 0
        assert "[DRY RUN]" in result.output
        assert "Would scrape Macao Daily" in result.output
        assert "2025-07-01" in result.output
        # Should not actually call scraper in dry run
        mock_scrape.assert_not_called()
    
    @patch('src.scraping.scrapers.macaodaily_scraper.MacaoDailyScraper.scrape_articles')
    @patch('src.scraping.utils.storage.save_articles')
    @patch('cli.scrape.get_settings')
    def test_scrape_macaodaily_success(self, mock_get_settings, mock_save, mock_scrape, mock_settings, sample_articles, temp_dir):
        """Test successful scrape macaodaily command."""
        mock_get_settings.return_value = mock_settings
        mock_scrape.return_value = sample_articles
        mock_save.return_value = temp_dir / "test.json"
        
        result = self.runner.invoke(cli, ['scrape', 'macaodaily', '--date', '2025-07-01'])
        
        assert result.exit_code == 0
        assert "Scraping Macao Daily" in result.output
        assert "Successfully scraped" in result.output
        assert str(len(sample_articles)) in result.output
        mock_scrape.assert_called_once()
        mock_save.assert_called_once()
    
    @patch('src.scraping.scrapers.macaodaily_scraper.MacaoDailyScraper.scrape_articles')
    @patch('cli.scrape.get_settings')
    def test_scrape_macaodaily_failure(self, mock_get_settings, mock_scrape, mock_settings):
        """Test scrape macaodaily command with scraping failure."""
        mock_get_settings.return_value = mock_settings
        mock_scrape.side_effect = Exception("Scraping failed")
        
        result = self.runner.invoke(cli, ['scrape', 'macaodaily', '--date', '2025-07-01'])
        
        assert result.exit_code == 1
        assert "Error scraping" in result.output
        assert "Scraping failed" in result.output
    
    @patch('src.scraping.scrapers.macaodaily_scraper.MacaoDailyScraper.scrape_articles')
    @patch('src.scraping.utils.storage.save_articles')
    @patch('cli.scrape.get_settings')
    def test_scrape_today_command(self, mock_get_settings, mock_save, mock_scrape, mock_settings, sample_articles, temp_dir):
        """Test scrape today command."""
        mock_get_settings.return_value = mock_settings
        mock_scrape.return_value = sample_articles
        mock_save.return_value = temp_dir / "test.json"
        
        result = self.runner.invoke(cli, ['scrape', 'today'])
        
        assert result.exit_code == 0
        assert "Scraping today's news" in result.output
        mock_scrape.assert_called_once()
    
    def test_invalid_command(self):
        """Test invalid command handling."""
        result = self.runner.invoke(cli, ['invalid-command'])
        
        assert result.exit_code != 0
        assert "No such command" in result.output
    
    def test_invalid_scrape_source(self):
        """Test invalid scrape source handling."""
        result = self.runner.invoke(cli, ['scrape', 'invalid-source'])
        
        assert result.exit_code != 0
        assert "No such command" in result.output
    
    @patch('cli.status.get_settings')
    def test_status_files_with_limit(self, mock_get_settings, mock_settings, temp_dir, sample_json_file):
        """Test status files command with limit parameter."""
        mock_get_settings.return_value = mock_settings
        
        # Create some test files
        for i in range(5):
            test_file = temp_dir / f"test_{i}.json"
            test_file.write_text('{"articles": []}')
        
        result = self.runner.invoke(status_cli, ['files', '--limit', '3'])
        
        assert result.exit_code == 0
        assert "Recent Data Files (limit: 3)" in result.output
    
    @patch('cli.status.get_settings')
    def test_status_stats_with_days(self, mock_get_settings, mock_settings):
        """Test status stats command with days parameter."""
        mock_get_settings.return_value = mock_settings
        
        result = self.runner.invoke(status_cli, ['stats', '--days', '30'])
        
        assert result.exit_code == 0
        assert "Scraping Statistics (Last 30 days)" in result.output


@pytest.mark.integration
@pytest.mark.cli
class TestCLIWithRealFiles:
    """Test CLI commands with real file operations."""
    
    def setup_method(self):
        """Set up test environment for each test."""
        self.runner = CliRunner()
    
    @patch('cli.status.get_settings')
    def test_status_files_with_real_data(self, mock_get_settings, mock_settings, temp_dir, sample_articles):
        """Test status files command with real data files."""
        mock_get_settings.return_value = mock_settings
        
        # Create test data structure
        source_dir = mock_settings.data.raw_data_path / "macaodaily" / "2025"
        source_dir.mkdir(parents=True, exist_ok=True)
        
        # Create test file
        test_file = source_dir / "20250701.json"
        data = {
            "source": "macaodaily",
            "target_date": "2025-07-01",
            "article_count": len(sample_articles),
            "articles": [article.model_dump() for article in sample_articles]
        }
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        
        result = self.runner.invoke(status_cli, ['files', '--limit', '5'])
        
        assert result.exit_code == 0
        assert "20250701.json" in result.output
        assert "macaodaily" in result.output
        assert str(len(sample_articles)) in result.output
    
    @patch('cli.status.get_settings')
    def test_status_stats_with_real_data(self, mock_get_settings, mock_settings, temp_dir, sample_articles):
        """Test status stats command with real data files."""
        mock_get_settings.return_value = mock_settings
        
        # Create test data structure
        source_dir = mock_settings.data.raw_data_path / "macaodaily" / "2025"
        source_dir.mkdir(parents=True, exist_ok=True)
        
        # Create test file
        test_file = source_dir / "20250701.json"
        data = {
            "source": "macaodaily",
            "target_date": "2025-07-01",
            "article_count": len(sample_articles),
            "articles": [article.model_dump() for article in sample_articles]
        }
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        
        result = self.runner.invoke(status_cli, ['stats', '--days', '7'])
        
        assert result.exit_code == 0
        assert "MACAODAILY:" in result.output
        assert "Files: 1" in result.output
        assert f"Articles: {len(sample_articles)}" in result.output
