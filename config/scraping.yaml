scrapers:
  macaodaily:
    name: macaodaily
    class_name: MacaoDailyScraper
    module: src.scraping.scrapers.macaodaily_scraper
    enabled: true
    base_url: "http://www.macaodaily.com/"
    rate_limit: 2.0
    timeout: 30
    max_retries: 3
    selectors:
      article_list: "#all_article_list"
      title: "strong[style*='font-size:14px']"
      content: "tbody#ozoom, founder-content"
    note: "Scrapes Macao Daily news articles with date-based URLs"
  
  shimindaily:
    name: shimindaily
    class_name: ShiminDailyScraper
    module: src.scraping.scrapers.shimindaily_scraper
    enabled: false
    base_url: "https://shimindaily.net"
    rate_limit: 1.5
    timeout: 30
    max_retries: 3
    note: "Currently disabled due to Cloudflare protection"

content_processing:
  min_content_length: 100
  max_content_length: 50000
  clean_html: true
  extract_images: true
  generate_markdown: true

output:
  format: json
  include_metadata: true
  date_format: "%Y%m%d"
