scraping:
  rate_limit_delay: 2.0
  max_retries: 3
  timeout: 30
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
  playwright_headless: true
  playwright_timeout: 30000

rephrasing:
  model_name: "gemini-2.5-flash"
  max_tokens: 4000
  temperature: 0.3
  rate_limit_delay: 2.0
  batch_size: 8
  min_content_length: 100
  max_retries: 3

data:
  raw_data_path: "data/raw"
  processed_data_path: "data/processed"
  export_data_path: "data/exports"
  log_path: "logs"

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_enabled: true
  console_enabled: true

api:
  # Server configuration
  api_url: ""  # Set via environment variable NEWS_API_URL

  # Authentication
  auth_token: ""  # Set via environment variable NEWS_API_AUTH_TOKEN

  # Request configuration
  timeout: 60
  max_retries: 3
  retry_delay: 1.0
  retry_backoff: 2.0
  batch_size: 10

  # Rate limiting
  rate_limit_delay: 1.0
  max_requests_per_minute: 60

  # Upload settings
  default_status: "draft"  # draft, published
  include_images: true
  image_format: "base64"  # external, base64
  max_image_size_mb: 5
