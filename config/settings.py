"""
Centralized configuration management for the scraping and rephrasing system.
"""

import os
import yaml
from pathlib import Path
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()
from functools import lru_cache


@dataclass
class ScrapingSettings:
    """Configuration for scraping operations."""
    rate_limit_delay: float = 2.0
    max_retries: int = 3
    timeout: int = 30
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    playwright_headless: bool = True
    playwright_timeout: int = 30000


@dataclass
class RephrasingSettings:
    """Configuration for AI rephrasing operations."""
    gemini_api_key: str = ""
    model_name: str = "gemini-2.5-flash"
    max_tokens: int = 4000
    temperature: float = 0.3
    rate_limit_delay: float = 6.5  # Minimum seconds between requests (Gemini free tier)
    batch_size: int = 8  # Optimal batch size for reliability
    min_content_length: int = 200
    max_content_length: int = 10000
    max_retries: int = 3

    # Gemini free tier limits
    max_requests_per_day: int = 250
    max_requests_per_minute: int = 10
    max_tokens_per_minute: int = 250000

    # Processing settings
    priority_threshold: float = 0.6  # Minimum priority score to process (0.6 = high priority only)

    @property
    def api_key(self) -> str:
        """Get API key from environment or config."""
        import os
        return os.getenv("GEMINI_API_KEY", self.gemini_api_key)


@dataclass
class DataSettings:
    """Configuration for data storage and management."""
    raw_data_path: Path = field(default_factory=lambda: Path("data/raw"))
    processed_data_path: Path = field(default_factory=lambda: Path("data/processed"))
    export_data_path: Path = field(default_factory=lambda: Path("data/exports"))
    log_path: Path = field(default_factory=lambda: Path("logs"))

    def __post_init__(self):
        """Ensure paths are Path objects."""
        if isinstance(self.raw_data_path, str):
            self.raw_data_path = Path(self.raw_data_path)
        if isinstance(self.processed_data_path, str):
            self.processed_data_path = Path(self.processed_data_path)
        if isinstance(self.export_data_path, str):
            self.export_data_path = Path(self.export_data_path)
        if isinstance(self.log_path, str):
            self.log_path = Path(self.log_path)


@dataclass
class ApiSettings:
    """Configuration for News API integration."""
    # Server configuration
    api_url: str = ""  # Full API URL including endpoint

    # Authentication
    auth_token: str = ""
    api_key: str = ""

    # Request configuration
    timeout: int = 60
    max_retries: int = 3
    retry_delay: float = 1.0
    retry_backoff: float = 2.0
    batch_size: int = 10

    # Rate limiting
    rate_limit_delay: float = 1.0
    max_requests_per_minute: int = 60

    # Upload settings
    default_status: str = "draft"  # draft, published
    include_images: bool = True
    image_format: str = "base64"  # external, base64
    max_image_size_mb: int = 5

    @property
    def full_url(self) -> str:
        """Get the complete API URL."""
        return self.api_url

    @property
    def token(self) -> str:
        """Get authentication token from environment or config."""
        import os
        return os.getenv("NEWS_API_AUTH_TOKEN", self.auth_token)


@dataclass
class LoggingSettings:
    """Configuration for logging."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_enabled: bool = True
    console_enabled: bool = True


@dataclass
class AppSettings:
    """Main application settings container."""
    scraping: ScrapingSettings = field(default_factory=ScrapingSettings)
    rephrasing: RephrasingSettings = field(default_factory=RephrasingSettings)
    data: DataSettings = field(default_factory=DataSettings)
    logging: LoggingSettings = field(default_factory=LoggingSettings)
    api: ApiSettings = field(default_factory=ApiSettings)
    
    @classmethod
    def from_env(cls) -> 'AppSettings':
        """Create settings from environment variables."""
        settings = cls()

        # Override with environment variables
        if api_key := os.getenv('GEMINI_API_KEY'):
            settings.rephrasing.gemini_api_key = api_key

        if log_level := os.getenv('LOG_LEVEL'):
            settings.logging.level = log_level

        if data_root := os.getenv('DATA_ROOT'):
            base_path = Path(data_root)
            settings.data.raw_data_path = base_path / "raw"
            settings.data.processed_data_path = base_path / "processed"
            settings.data.export_data_path = base_path / "exports"

        if log_root := os.getenv('LOG_ROOT'):
            settings.data.log_path = Path(log_root)

        # API configuration from environment
        if news_api_url := os.getenv('NEWS_API_URL'):
            settings.api.api_url = news_api_url

        if news_api_auth_token := os.getenv('NEWS_API_AUTH_TOKEN'):
            settings.api.auth_token = news_api_auth_token

        return settings
    
    @classmethod
    def from_yaml(cls, config_path: Path) -> 'AppSettings':
        """Load settings from YAML file."""
        if not config_path.exists():
            return cls.from_env()
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        settings = cls.from_env()  # Start with env vars
        
        # Override with YAML data
        if scraping_config := config_data.get('scraping'):
            for key, value in scraping_config.items():
                if hasattr(settings.scraping, key):
                    setattr(settings.scraping, key, value)
        
        if rephrasing_config := config_data.get('rephrasing'):
            for key, value in rephrasing_config.items():
                if hasattr(settings.rephrasing, key):
                    setattr(settings.rephrasing, key, value)
        
        if data_config := config_data.get('data'):
            for key, value in data_config.items():
                if hasattr(settings.data, key):
                    setattr(settings.data, key, Path(value) if 'path' in key else value)
        
        if logging_config := config_data.get('logging'):
            for key, value in logging_config.items():
                if hasattr(settings.logging, key):
                    setattr(settings.logging, key, value)

        if api_config := config_data.get('api'):
            for key, value in api_config.items():
                if hasattr(settings.api, key) and value:  # Only override if value is not empty
                    setattr(settings.api, key, value)

        return settings


@lru_cache(maxsize=1)
def get_settings(config_file: Optional[str] = None) -> AppSettings:
    """
    Get application settings with caching.
    
    Args:
        config_file: Optional path to YAML config file
    
    Returns:
        AppSettings instance
    """
    if config_file:
        config_path = Path(config_file)
    else:
        # Look for config files in standard locations
        possible_paths = [
            Path("config/app.yaml"),
            Path("config/settings.yaml"),
            Path("app.yaml"),
            Path("settings.yaml")
        ]
        
        config_path = None
        for path in possible_paths:
            if path.exists():
                config_path = path
                break
    
    if config_path and config_path.exists():
        return AppSettings.from_yaml(config_path)
    else:
        return AppSettings.from_env()


def load_scraper_config(config_path: Optional[Path] = None) -> List[Dict[str, Any]]:
    """
    Load scraper configuration from YAML file.
    
    Args:
        config_path: Path to scraper config file
    
    Returns:
        List of scraper configurations
    """
    if config_path is None:
        config_path = Path("config/scraping.yaml")
    
    if not config_path.exists():
        # Return default configuration
        return [
            {
                'name': 'macaodaily',
                'class_name': 'MacaoDailyScraper',
                'module': 'src.scraping.scrapers.macaodaily_scraper',
                'enabled': True,
                'base_url': 'http://www.macaodaily.com/',
                'rate_limit': 2.0,
                'timeout': 30,
                'max_retries': 3
            }
        ]
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = yaml.safe_load(f)

    # Convert nested dict to list format
    scrapers_dict = config_data.get('scrapers', {})
    scrapers_list = []

    for scraper_name, scraper_config in scrapers_dict.items():
        scraper_config['name'] = scraper_name
        scrapers_list.append(scraper_config)

    return scrapers_list
