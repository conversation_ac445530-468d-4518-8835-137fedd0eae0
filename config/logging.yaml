version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s() - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  simple:
    format: '%(levelname)s - %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
  
  file_info:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: standard
    filename: logs/rephrase_info.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  file_debug:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/rephrase_debug.log
    maxBytes: 10485760  # 10MB
    backupCount: 3
    encoding: utf8
  
  file_error:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/rephrase_error.log
    maxBytes: 10485760  # 10MB
    backupCount: 10
    encoding: utf8

loggers:
  rephrase_processor:
    level: DEBUG
    handlers: [console, file_info, file_debug, file_error]
    propagate: false
  
  src.rephrasing:
    level: DEBUG
    handlers: [file_debug, file_error]
    propagate: false
  
  src.rephrasing.ai_clients:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false
  
  src.rephrasing.utils.rate_limiter:
    level: INFO
    handlers: [console, file_info, file_error]
    propagate: false

root:
  level: WARNING
  handlers: [console, file_error]
