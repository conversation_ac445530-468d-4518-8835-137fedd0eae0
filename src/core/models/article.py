"""
Enhanced data models for news articles.

This module provides the core data structures for representing news articles
throughout the scraping and rephrasing pipeline.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import json
import uuid
import hashlib


def generate_article_id(url: str) -> str:
    """Generate a consistent ID based on the article URL."""
    if not url:
        return str(uuid.uuid4())
    # Create a deterministic ID from the URL
    return hashlib.md5(url.encode('utf-8')).hexdigest()


@dataclass
class ArticleImage:
    """Represents an image associated with an article."""
    src: str
    description: Optional[str] = None
    alt_text: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None


@dataclass
class BaseArticle(ABC):
    """Base class for all article types."""
    id: Optional[str] = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = ""
    content: str = ""
    source_url: str = ""
    publish_date: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def __post_init__(self):
        """Post-initialization processing."""
        # Handle string datetime fields from JSON data
        datetime_fields = ['publish_date', 'created_at', 'updated_at']

        for field_name in datetime_fields:
            if hasattr(self, field_name):
                field_value = getattr(self, field_name)
                if isinstance(field_value, str):
                    try:
                        # Handle various datetime string formats
                        parsed_dt = datetime.fromisoformat(field_value.replace('Z', '+00:00'))
                        setattr(self, field_name, parsed_dt)
                    except (ValueError, AttributeError):
                        if field_name == 'publish_date':
                            setattr(self, field_name, None)
                        else:
                            # For created_at and updated_at, use current time as fallback
                            setattr(self, field_name, datetime.now())

    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        pass

    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2, default=str)


@dataclass
class ScrapedArticle(BaseArticle):
    """Represents a scraped news article with all original content."""
    
    # Source information
    source_site_name: str = ""
    source_site_url: str = ""
    original_title: str = ""
    original_url: str = ""
    
    # Content in multiple formats
    content_html: str = ""
    content_markdown: str = ""
    content_text: str = ""
    
    # Metadata
    author: Optional[str] = None
    scrape_timestamp: datetime = field(default_factory=datetime.now)
    images: List[ArticleImage] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """Post-initialization processing."""
        super().__post_init__()

        # Handle scrape_timestamp string conversion
        if isinstance(self.scrape_timestamp, str):
            try:
                self.scrape_timestamp = datetime.fromisoformat(self.scrape_timestamp.replace('Z', '+00:00'))
            except (ValueError, AttributeError):
                self.scrape_timestamp = datetime.now()

        # Convert legacy image format if needed
        if self.images and isinstance(self.images[0], dict):
            self.images = [
                ArticleImage(
                    src=img.get('src', ''),
                    description=img.get('description', '')
                ) for img in self.images
            ]

        # Generate content formats if missing
        if self.content_html and not self.content_markdown:
            self.content_markdown = self._html_to_markdown(self.content_html)

        if self.content_markdown and not self.content_text:
            self.content_text = self._markdown_to_text(self.content_markdown)

        # Set main content field
        if not self.content:
            self.content = self.content_text or self.content_markdown or self.content_html

    def _html_to_markdown(self, html: str) -> str:
        """Convert HTML content to Markdown format."""
        try:
            from markdownify import markdownify as md
            return md(html, heading_style="ATX").strip()
        except ImportError:
            # Fallback: simple HTML tag removal
            import re
            text = re.sub(r'<[^>]+>', '', html)
            return text.strip()

    def _markdown_to_text(self, markdown: str) -> str:
        """Convert Markdown to plain text."""
        import re
        # Remove markdown formatting
        text = re.sub(r'[#*`_\[\]()]', '', markdown)
        text = re.sub(r'\n+', ' ', text)
        return text.strip()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'source_site_name': self.source_site_name,
            'source_site_url': self.source_site_url,
            'original_url': self.original_url,
            'original_title': self.original_title,
            'title': self.title or self.original_title,
            'author': self.author,
            'publish_date': self.publish_date.isoformat() if self.publish_date else None,
            'scrape_timestamp': self.scrape_timestamp.isoformat(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'content_html': self.content_html,
            'content_markdown': self.content_markdown,
            'content_text': self.content_text,
            'content': self.content,
            'source_url': self.source_url or self.original_url,
            'images': [
                {
                    'src': img.src,
                    'description': img.description,
                    'alt_text': img.alt_text,
                    'width': img.width,
                    'height': img.height
                } for img in self.images
            ],
            'tags': self.tags
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ScrapedArticle':
        """Create ScrapedArticle instance from dictionary."""
        # Handle legacy format
        images_data = data.get('images', [])
        images = []
        for img_data in images_data:
            if isinstance(img_data, dict):
                images.append(ArticleImage(
                    src=img_data.get('src', ''),
                    description=img_data.get('description', ''),
                    alt_text=img_data.get('alt_text'),
                    width=img_data.get('width'),
                    height=img_data.get('height')
                ))
        
        # Generate consistent ID based on URL if no ID provided
        article_id = data.get('id')
        if not article_id:
            original_url = data.get('original_url', '')
            source_url = data.get('source_url', '')
            url_for_id = original_url or source_url
            article_id = generate_article_id(url_for_id)

        return cls(
            id=article_id,
            source_site_name=data.get('source_site_name', ''),
            source_site_url=data.get('source_site_url', ''),
            original_url=data.get('original_url', ''),
            original_title=data.get('original_title', ''),
            title=data.get('title', ''),
            content_html=data.get('content_html', ''),
            content_markdown=data.get('content_markdown', ''),
            content_text=data.get('content_text', ''),
            content=data.get('content', ''),
            source_url=data.get('source_url', '') or data.get('original_url', ''),
            publish_date=data.get('publish_date'),
            author=data.get('author'),
            scrape_timestamp=data.get('scrape_timestamp', datetime.now()),
            images=images,
            tags=data.get('tags', [])
        )


# Legacy compatibility - alias for backward compatibility
Article = ScrapedArticle


@dataclass 
class RephrasedArticle(BaseArticle):
    """Represents an AI-rephrased article ready for publication."""
    
    # Reference to original
    original_article_id: str = ""
    
    # Rephrased content
    rephrased_title: str = ""
    rephrased_content: str = ""
    slug: str = ""
    
    # Categorization and SEO
    categories: List[str] = field(default_factory=list)
    meta_title: Optional[str] = None
    meta_description: Optional[str] = None
    
    # AI processing metadata
    ai_model_used: str = ""
    processing_metadata: Dict[str, Any] = field(default_factory=dict)
    quality_score: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'id': self.id,
            'original_article_id': self.original_article_id,
            'title': self.title,
            'rephrased_title': self.rephrased_title,
            'content': self.content,
            'rephrased_content': self.rephrased_content,
            'slug': self.slug,
            'source_url': self.source_url,
            'publish_date': self.publish_date.isoformat() if self.publish_date else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'categories': self.categories,
            'meta_title': self.meta_title,
            'meta_description': self.meta_description,
            'ai_model_used': self.ai_model_used,
            'processing_metadata': self.processing_metadata,
            'quality_score': self.quality_score
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RephrasedArticle':
        """Create RephrasedArticle instance from dictionary."""
        return cls(
            id=data.get('id'),
            original_article_id=data.get('original_article_id', ''),
            title=data.get('title', ''),
            rephrased_title=data.get('rephrased_title', ''),
            content=data.get('content', ''),
            rephrased_content=data.get('rephrased_content', ''),
            slug=data.get('slug', ''),
            source_url=data.get('source_url', ''),
            publish_date=data.get('publish_date'),
            categories=data.get('categories', []),
            meta_title=data.get('meta_title'),
            meta_description=data.get('meta_description'),
            ai_model_used=data.get('ai_model_used', ''),
            processing_metadata=data.get('processing_metadata', {}),
            quality_score=data.get('quality_score')
        )
