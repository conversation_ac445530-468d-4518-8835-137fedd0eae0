"""
Data models for rephrased articles.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Dict, Any, Optional
import uuid


@dataclass
class RephrasedArticle:
    """Model for AI-rephrased articles aligned with PostData API."""

    # Core content (required for API)
    title: str = ""
    slug: str = ""  # SEO-friendly URL slug (English/numbers only)
    content: str = ""  # Markdown format
    summary: str = ""  # Fast reading summary

    # Metadata
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    original_article_id: str = ""
    source_url: str = ""
    source_site_name: str = ""

    # AI processing info
    ai_model_used: str = ""
    processing_timestamp: datetime = field(default_factory=datetime.now)
    processing_metadata: Dict[str, Any] = field(default_factory=dict)

    # Content organization (aligned with API categories)
    categories: List[str] = field(default_factory=list)  # Category slugs: tech, economy, politics, etc.
    tags: List[str] = field(default_factory=list)  # Original tags from scraping
    keywords: List[str] = field(default_factory=list)  # AI-extracted keywords
    priority_score: float = 0.0
    quality_score: float = 0.0

    # SEO metadata (required for API)
    meta_title: str = ""  # SEO title (50-60 characters)
    meta_description: str = ""  # SEO description (100-150 characters)

    # Publishing status
    is_published: bool = False
    publish_date: Optional[datetime] = None
    status: str = "draft"  # 'draft' or 'published'

    # Images (preserved from original article)
    images: List[Dict[str, Any]] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "title": self.title,
            "slug": self.slug,
            "content": self.content,
            "summary": self.summary,
            "original_article_id": self.original_article_id,
            "source_url": self.source_url,
            "source_site_name": self.source_site_name,
            "ai_model_used": self.ai_model_used,
            "processing_timestamp": self.processing_timestamp.isoformat(),
            "processing_metadata": self.processing_metadata,
            "categories": self.categories,
            "tags": self.tags,
            "keywords": self.keywords,
            "priority_score": self.priority_score,
            "quality_score": self.quality_score,
            "meta_title": self.meta_title,
            "meta_description": self.meta_description,
            "is_published": self.is_published,
            "publish_date": self.publish_date.isoformat() if self.publish_date else None,
            "status": self.status
        }

    def to_post_data(self) -> Dict[str, Any]:
        """Convert to PostData format for API submission."""
        return {
            "title": self.title,
            "slug": self.slug,
            "content": self.content,
            "categories": self.categories,  # Category slugs
            "meta": {
                "title": self.meta_title,
                "description": self.meta_description
            },
            "publishedAt": self.publish_date.isoformat() if self.publish_date else None,
            "_status": self.status
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RephrasedArticle':
        """Create instance from dictionary."""
        # Handle datetime fields
        processing_timestamp = data.get('processing_timestamp')
        if isinstance(processing_timestamp, str):
            processing_timestamp = datetime.fromisoformat(processing_timestamp)
        elif processing_timestamp is None:
            processing_timestamp = datetime.now()

        publish_date = data.get('publish_date')
        if isinstance(publish_date, str):
            publish_date = datetime.fromisoformat(publish_date)

        return cls(
            id=data.get('id', str(uuid.uuid4())),
            title=data.get('title', ''),
            slug=data.get('slug', ''),
            content=data.get('content', ''),
            summary=data.get('summary', ''),
            original_article_id=data.get('original_article_id', ''),
            source_url=data.get('source_url', ''),
            source_site_name=data.get('source_site_name', ''),
            ai_model_used=data.get('ai_model_used', ''),
            processing_timestamp=processing_timestamp,
            processing_metadata=data.get('processing_metadata', {}),
            categories=data.get('categories', []),
            tags=data.get('tags', []),
            keywords=data.get('keywords', []),
            priority_score=data.get('priority_score', 0.0),
            quality_score=data.get('quality_score', 0.0),
            meta_title=cls._extract_meta_title(data),
            meta_description=cls._extract_meta_description(data),
            is_published=data.get('is_published', False),
            publish_date=publish_date,
            status=data.get('status', 'draft'),
            images=data.get('images', [])
        )

    @classmethod
    def _extract_meta_title(cls, data: Dict[str, Any]) -> str:
        """Extract meta title from both old and new formats."""
        # Check for new format first (meta object)
        if 'meta' in data and data['meta']:
            meta_obj = data['meta']
            if isinstance(meta_obj, dict) and 'title' in meta_obj:
                return meta_obj['title'] or ''

        # Fallback to old format
        return data.get('meta_title', '')

    @classmethod
    def _extract_meta_description(cls, data: Dict[str, Any]) -> str:
        """Extract meta description from both old and new formats."""
        # Check for new format first (meta object)
        if 'meta' in data and data['meta']:
            meta_obj = data['meta']
            if isinstance(meta_obj, dict) and 'description' in meta_obj:
                return meta_obj['description'] or ''

        # Fallback to old format
        return data.get('meta_description', '')


@dataclass
class RephrasingJob:
    """Model for tracking rephrasing job progress."""
    
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    source_file: str = ""
    target_date: str = ""
    total_articles: int = 0
    processed_articles: int = 0
    successful_articles: int = 0
    failed_articles: int = 0
    skipped_articles: int = 0
    
    # Rate limiting tracking
    daily_quota_used: int = 0
    quota_reset_time: Optional[datetime] = None
    last_request_time: Optional[datetime] = None
    
    # Status
    status: str = "pending"  # pending, running, paused, completed, failed, quota_exhausted
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    quota_exhausted_time: Optional[datetime] = None
    error_message: Optional[str] = None
    
    # Progress tracking
    processed_article_ids: List[str] = field(default_factory=list)
    failed_article_ids: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "source_file": self.source_file,
            "target_date": self.target_date,
            "total_articles": self.total_articles,
            "processed_articles": self.processed_articles,
            "successful_articles": self.successful_articles,
            "failed_articles": self.failed_articles,
            "skipped_articles": self.skipped_articles,
            "daily_quota_used": self.daily_quota_used,
            "quota_reset_time": self.quota_reset_time.isoformat() if self.quota_reset_time else None,
            "last_request_time": self.last_request_time.isoformat() if self.last_request_time else None,
            "status": self.status,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "quota_exhausted_time": self.quota_exhausted_time.isoformat() if self.quota_exhausted_time else None,
            "error_message": self.error_message,
            "processed_article_ids": self.processed_article_ids,
            "failed_article_ids": self.failed_article_ids
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RephrasingJob':
        """Create instance from dictionary."""
        # Handle datetime fields
        def parse_datetime(dt_str):
            return datetime.fromisoformat(dt_str) if dt_str else None
            
        return cls(
            id=data.get('id', str(uuid.uuid4())),
            source_file=data.get('source_file', ''),
            target_date=data.get('target_date', ''),
            total_articles=data.get('total_articles', 0),
            processed_articles=data.get('processed_articles', 0),
            successful_articles=data.get('successful_articles', 0),
            failed_articles=data.get('failed_articles', 0),
            skipped_articles=data.get('skipped_articles', 0),
            daily_quota_used=data.get('daily_quota_used', 0),
            quota_reset_time=parse_datetime(data.get('quota_reset_time')),
            last_request_time=parse_datetime(data.get('last_request_time')),
            status=data.get('status', 'pending'),
            started_at=parse_datetime(data.get('started_at')),
            completed_at=parse_datetime(data.get('completed_at')),
            quota_exhausted_time=parse_datetime(data.get('quota_exhausted_time')),
            error_message=data.get('error_message'),
            processed_article_ids=data.get('processed_article_ids', []),
            failed_article_ids=data.get('failed_article_ids', [])
        )
