"""
Custom exceptions for the scraping and rephrasing system.
"""


class ScrapeRephraseException(Exception):
    """Base exception for all scraping and rephrasing errors."""
    pass


class ScrapingException(ScrapeRephraseException):
    """Base exception for scraping-related errors."""
    pass


class ScraperNotFoundError(ScrapingException):
    """Raised when a requested scraper is not found."""
    pass


class ScrapingTimeoutError(ScrapingException):
    """Raised when scraping operations timeout."""
    pass


class ContentExtractionError(ScrapingException):
    """Raised when content cannot be extracted from a page."""
    pass


class RephrasingException(ScrapeRephraseException):
    """Base exception for rephrasing-related errors."""
    pass


class AIServiceError(RephrasingException):
    """Raised when AI service calls fail."""
    pass


class ContentValidationError(RephrasingException):
    """Raised when rephrased content fails validation."""
    pass


class ConfigurationError(ScrapeRephraseException):
    """Raised when configuration is invalid or missing."""
    pass


class DataStorageError(ScrapeRephraseException):
    """Raised when data storage operations fail."""
    pass
