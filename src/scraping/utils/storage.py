"""
Data storage utilities for the news scraping project.
Currently supports JSON file storage with future database expansion in mind.
"""

import json
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

from src.core.models.article import ScrapedArticle
from config.settings import get_settings


class DataStorage:
    """
    Handles data storage operations.
    Currently implements JSON file storage.
    """
    
    def __init__(self, output_dir: str = None):
        settings = get_settings()
        self.output_dir = Path(output_dir or settings.data.raw_data_path)
        self.output_dir.mkdir(exist_ok=True, parents=True)

    def save_articles(self, articles: List[ScrapedArticle], scraper_name: str, target_date: str = None) -> str:
        """
        Save articles to JSON file in organized structure.
        Checks existing file article count and only saves if count differs.
        Returns the path of the saved file, or None if skipped.
        """
        if not articles:
            return None

        # Use provided date or current date
        if target_date:
            date_obj = datetime.strptime(target_date, "%Y-%m-%d")
        else:
            date_obj = datetime.now()

        # Create organized directory structure: data/raw/source/year/
        year = date_obj.strftime("%Y")
        date_str = date_obj.strftime("%Y%m%d")

        # Create directory structure
        source_dir = self.output_dir / scraper_name / year
        source_dir.mkdir(parents=True, exist_ok=True)

        # Create filename without source prefix: 20250210.json
        filename = f"{date_str}.json"
        filepath = source_dir / filename

        # Check if file exists and compare article count
        file_action = "created"
        if filepath.exists():
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                existing_count = existing_data.get('article_count', 0)
                new_count = len(articles)

                if existing_count == new_count:
                    # Same article count, skip saving and return special indicator
                    return f"SKIPPED:{filepath}"
                else:
                    # Different article count, proceed with saving
                    file_action = "updated"
            except (json.JSONDecodeError, KeyError, FileNotFoundError):
                # If we can't read the existing file properly, proceed with saving
                file_action = "overwritten"

        # Convert articles to dict format
        articles_data = {
            'scraper_name': scraper_name,
            'target_date': target_date or date_obj.strftime("%Y-%m-%d"),
            'scrape_timestamp': datetime.now().isoformat(),
            'article_count': len(articles),
            'articles': [article.to_dict() for article in articles]
        }

        # Save to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(articles_data, f, ensure_ascii=False, indent=2)

        return str(filepath)

    def should_skip_full_scraping(self, scraper, target_date: str) -> tuple[bool, str, int]:
        """
        Check if full article scraping should be skipped by comparing article counts.
        Only scrapes the index page to get current article count.
        Returns (should_skip, reason, current_count) tuple.
        """
        existing_info = self.get_existing_file_info('macaodaily', target_date)

        # Get current article count from index page only (fast)
        current_count = scraper.get_article_count_only()

        if existing_info:
            existing_count = existing_info['article_count']
            if existing_count == current_count:
                return True, f"Same article count ({current_count}), skipping full scraping", current_count
            else:
                return False, f"Article count changed ({existing_count} → {current_count}), proceeding with full scraping", current_count
        else:
            return False, f"No existing file, proceeding with full scraping ({current_count} articles)", current_count

    def get_existing_file_info(self, scraper_name: str, target_date: str) -> Dict[str, Any]:
        """
        Get information about an existing file for the given scraper and date.
        Returns dict with file info or None if file doesn't exist.
        """
        date_obj = datetime.strptime(target_date, "%Y-%m-%d")
        year = date_obj.strftime("%Y")
        date_str = date_obj.strftime("%Y%m%d")

        source_dir = self.output_dir / scraper_name / year
        filename = f"{date_str}.json"
        filepath = source_dir / filename

        if not filepath.exists():
            return None

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return {
                'filepath': str(filepath),
                'article_count': data.get('article_count', 0),
                'scrape_timestamp': data.get('scrape_timestamp'),
                'target_date': data.get('target_date')
            }
        except (json.JSONDecodeError, KeyError, FileNotFoundError):
            return None

    def load_articles(self, filepath: str) -> List[ScrapedArticle]:
        """
        Load articles from JSON file.
        Returns list of ScrapedArticle objects.
        """
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)

        articles = []
        for article_data in data.get('articles', []):
            article = ScrapedArticle.from_dict(article_data)
            articles.append(article)

        return articles
    
    def get_existing_urls(self, scraper_name: str = None) -> set:
        """
        Get set of URLs that have already been scraped.
        Used to avoid duplicate scraping.
        """
        existing_urls = set()
        
        # Search through existing JSON files
        pattern = f"{scraper_name}_*.json" if scraper_name else "*.json"
        for filepath in self.output_dir.glob(pattern):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for article_data in data.get('articles', []):
                    url = article_data.get('original_url')
                    if url:
                        existing_urls.add(url)
                        
            except Exception as e:
                print(f"Error reading {filepath}: {e}")
                continue
        
        return existing_urls
    
    def get_latest_file(self, scraper_name: str) -> str:
        """
        Get the path of the most recent file for a scraper.
        """
        pattern = f"{scraper_name}_*.json"
        files = list(self.output_dir.glob(pattern))
        
        if not files:
            return None
        
        # Sort by modification time, return most recent
        latest_file = max(files, key=lambda f: f.stat().st_mtime)
        return str(latest_file)
    
    def list_all_files(self) -> List[Dict[str, Any]]:
        """
        List all scraped data files with metadata.
        """
        files_info = []
        
        for filepath in self.output_dir.glob("*.json"):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                files_info.append({
                    'filename': filepath.name,
                    'filepath': str(filepath),
                    'scraper_name': data.get('scraper_name', 'unknown'),
                    'scrape_timestamp': data.get('scrape_timestamp'),
                    'article_count': data.get('article_count', 0),
                    'file_size': filepath.stat().st_size
                })
                
            except Exception as e:
                print(f"Error reading {filepath}: {e}")
                continue
        
        # Sort by timestamp, newest first
        files_info.sort(key=lambda x: x['scrape_timestamp'] or '', reverse=True)
        return files_info
    
    def cleanup_old_files(self, keep_count: int = 10):
        """
        Remove old files, keeping only the most recent ones for each scraper.
        """
        scrapers = {}
        
        # Group files by scraper
        for filepath in self.output_dir.glob("*.json"):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                scraper_name = data.get('scraper_name', 'unknown')
                if scraper_name not in scrapers:
                    scrapers[scraper_name] = []
                
                scrapers[scraper_name].append({
                    'filepath': filepath,
                    'timestamp': data.get('scrape_timestamp', '')
                })
                
            except Exception:
                continue
        
        # Remove old files for each scraper
        for scraper_name, files in scrapers.items():
            files.sort(key=lambda x: x['timestamp'], reverse=True)
            
            # Remove files beyond keep_count
            for file_info in files[keep_count:]:
                try:
                    file_info['filepath'].unlink()
                    print(f"Removed old file: {file_info['filepath'].name}")
                except Exception as e:
                    print(f"Error removing {file_info['filepath']}: {e}")


# Convenience functions
def save_articles(articles: List[ScrapedArticle], scraper_name: str, target_date: str = None) -> str:
    """Convenience function to save articles."""
    storage = DataStorage()
    return storage.save_articles(articles, scraper_name, target_date)


def load_articles(filepath: str) -> List[ScrapedArticle]:
    """Convenience function to load articles."""
    storage = DataStorage()
    return storage.load_articles(filepath)


def get_existing_urls(scraper_name: str = None) -> set:
    """Convenience function to get existing URLs."""
    storage = DataStorage()
    return storage.get_existing_urls(scraper_name)
