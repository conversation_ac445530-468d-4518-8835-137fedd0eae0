"""
Base scraper classes for static and dynamic websites.
All specific scrapers should inherit from these base classes.
"""

import requests
from abc import ABC, abstractmethod
from typing import List, Optional
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright, <PERSON>, Browser
import logging
from datetime import datetime

from src.core.models.article import ScrapedArticle
from src.core.exceptions import ScrapingException, ScrapingTimeoutError, ContentExtractionError


class BaseScraper(ABC):
    """Abstract base class for all scrapers."""
    
    def __init__(self, source_site_name: str, source_site_url: str):
        self.source_site_name = source_site_name
        self.source_site_url = source_site_url
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def scrape(self, url: str) -> List[ScrapedArticle]:
        """
        Main scraping method that must be implemented by subclasses.
        Returns a list of ScrapedArticle objects.
        """
        pass
    
    @abstractmethod
    def parse(self, content) -> List[ScrapedArticle]:
        """
        Parse the content and extract articles.
        Must be implemented by subclasses.
        """
        pass


class StaticScraper(BaseScraper):
    """
    Base class for scraping static websites using requests + BeautifulSoup.
    """
    
    def __init__(self, source_site_name: str, source_site_url: str):
        super().__init__(source_site_name, source_site_url)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def scrape(self, url: str) -> List[ScrapedArticle]:
        """
        Scrape a static website using requests.
        """
        try:
            self.logger.info(f"Scraping static site: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = response.apparent_encoding  # Handle Chinese encoding
            
            soup = BeautifulSoup(response.text, 'html.parser')
            return self.parse(soup)
            
        except requests.RequestException as e:
            self.logger.error(f"Request failed for {url}: {e}")
            raise ScrapingException(f"Failed to scrape {url}: {e}")
        except Exception as e:
            self.logger.error(f"Unexpected error scraping {url}: {e}")
            raise ScrapingException(f"Unexpected error scraping {url}: {e}")


class DynamicScraper(BaseScraper):
    """
    Base class for scraping dynamic websites using Playwright.
    Handles JavaScript-rendered content and Cloudflare protection.
    """
    
    def __init__(self, source_site_name: str, source_site_url: str):
        super().__init__(source_site_name, source_site_url)
    
    def scrape(self, url: str) -> List[ScrapedArticle]:
        """
        Scrape a dynamic website using Playwright.
        """
        try:
            self.logger.info(f"Scraping dynamic site: {url}")
            
            with sync_playwright() as p:
                # Launch browser with stealth settings
                browser = p.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-blink-features=AutomationControlled',
                        '--disable-dev-shm-usage',
                        '--disable-extensions',
                        '--disable-gpu',
                        '--disable-web-security',
                        '--allow-running-insecure-content'
                    ]
                )

                # Create context with realistic settings
                context = browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    locale='zh-CN',
                    timezone_id='Asia/Macau'
                )

                page = context.new_page()

                # Navigate to page with extended timeout
                try:
                    page.goto(url, timeout=60000, wait_until='domcontentloaded')

                    # Wait for initial content
                    page.wait_for_timeout(5000)

                    # Handle Cloudflare challenge if present
                    self._handle_cloudflare_challenge(page)

                    # Get page content
                    content = page.content()
                    soup = BeautifulSoup(content, 'html.parser')

                    articles = self.parse(soup)

                except Exception as nav_error:
                    self.logger.warning(f"Navigation failed for {url}: {nav_error}")
                    # Try to get whatever content we can
                    try:
                        content = page.content()
                        soup = BeautifulSoup(content, 'html.parser')
                        articles = self.parse(soup)
                    except:
                        articles = []

                browser.close()
                return articles

        except Exception as e:
            self.logger.error(f"Playwright scraping failed for {url}: {e}")
            raise ScrapingException(f"Failed to scrape dynamic site {url}: {e}")

    def _handle_cloudflare_challenge(self, page: Page):
        """
        Handle Cloudflare challenge page.
        """
        try:
            # Check for Cloudflare challenge indicators
            cf_indicators = [
                'Checking your browser before accessing',
                'DDoS protection by Cloudflare',
                'cf-browser-verification',
                'cf-challenge-running'
            ]
            
            page_content = page.content().lower()
            
            if any(indicator.lower() in page_content for indicator in cf_indicators):
                self.logger.info("Cloudflare challenge detected, waiting...")
                
                # Wait for challenge to complete (up to 30 seconds)
                for i in range(30):
                    page.wait_for_timeout(1000)
                    current_content = page.content().lower()
                    
                    # Check if challenge is completed
                    if not any(indicator.lower() in current_content for indicator in cf_indicators):
                        self.logger.info("Cloudflare challenge completed")
                        break
                        
                    if i == 29:
                        self.logger.warning("Cloudflare challenge timeout")
                        
        except Exception as e:
            self.logger.warning(f"Error handling Cloudflare challenge: {e}")

    def _wait_for_content(self, page: Page, selector: str, timeout: int = 10000):
        """
        Wait for specific content to load.
        """
        try:
            page.wait_for_selector(selector, timeout=timeout)
        except Exception as e:
            self.logger.warning(f"Content selector '{selector}' not found within {timeout}ms: {e}")


# Legacy compatibility aliases
Article = ScrapedArticle
