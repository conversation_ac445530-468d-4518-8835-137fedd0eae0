"""
Scraper for Macao Daily (澳門日報) - Static website
http://www.macaodaily.com/html/2025-05/28/node_1.htm
"""

import re
from typing import List, Optional, Dict
from bs4 import BeautifulSoup, Tag
from datetime import datetime, date
from urllib.parse import urljoin

from src.scraping.scrapers.base import StaticScraper
from src.core.models.article import ScrapedArticle


class MacaoDailyScraper(StaticScraper):
    """
    Scraper for Macao Daily newspaper website.
    Inherits from StaticScraper for simple HTTP requests.
    """

    def __init__(self, target_date: Optional[str] = None):
        super().__init__(
            source_site_name="澳門日報",
            source_site_url="http://www.macaodaily.com/"
        )
        # Set target date (format: YYYY-MM-DD) or use today
        if target_date:
            try:
                self.target_date = datetime.strptime(target_date, "%Y-%m-%d").date()
            except ValueError:
                self.logger.warning(f"Invalid date format: {target_date}, using today")
                self.target_date = date.today()
        else:
            self.target_date = date.today()

        self.logger.info(f"Target scraping date: {self.target_date}")

    def get_index_url(self) -> str:
        """Generate the index URL for the target date."""
        year = self.target_date.year
        month = self.target_date.month
        day = self.target_date.day
        return f"http://www.macaodaily.com/html/{year}-{month:02d}/{day:02d}/node_1.htm"
    
    def scrape(self, url: str = None) -> List[ScrapedArticle]:
        """
        Scrape Macao Daily using date-based URL.
        If url is provided, use it; otherwise use the target date.
        """
        if url is None:
            url = self.get_index_url()

        self.logger.info(f"Scraping Macao Daily for date {self.target_date}: {url}")
        return super().scrape(url)

    def get_article_count_only(self, url: str = None) -> int:
        """
        Get only the article count from the index page without scraping individual articles.
        This is much faster than full scraping.
        """
        if url is None:
            url = self.get_index_url()

        try:
            self.logger.info(f"Getting article count for {self.target_date}: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = response.apparent_encoding

            soup = BeautifulSoup(response.text, 'html.parser')
            article_links = self._find_article_links(soup)
            count = len(article_links)

            self.logger.info(f"Found {count} articles on index page for {self.target_date}")
            return count

        except Exception as e:
            self.logger.error(f"Error getting article count from {url}: {e}")
            return 0

    def parse(self, soup: BeautifulSoup) -> List[ScrapedArticle]:
        """
        Parse the Macao Daily page and extract article information.
        """
        articles = []

        try:
            # Find all article links on the page
            # Macao Daily typically has article links in specific patterns
            article_links = self._find_article_links(soup)

            for link_info in article_links:
                try:
                    article = self._scrape_individual_article(link_info)
                    if article:
                        articles.append(article)
                except Exception as e:
                    self.logger.error(f"Error scraping individual article {link_info.get('url', 'unknown')}: {e}")
                    continue

            self.logger.info(f"Successfully scraped {len(articles)} articles from Macao Daily for {self.target_date}")

        except Exception as e:
            self.logger.error(f"Error parsing Macao Daily page: {e}")

        return articles
    
    def _find_article_links(self, soup: BeautifulSoup) -> List[dict]:
        """
        Find all article links from the index page using the proper structure.
        Returns list of dicts with url, title, and tags.
        """
        links = []

        # Find the main article list div with id="all_article_list"
        article_list_div = soup.find('div', id='all_article_list')
        if not article_list_div:
            self.logger.warning("Could not find div with id='all_article_list'")
            return []

        self.logger.info("Found article list div, extracting articles by category")

        # Find all h4 elements that contain category information
        for h4 in article_list_div.find_all('h4'):
            # Extract category tags from h4 element
            category_tags = self._extract_category_tags(h4)

            if not category_tags:
                continue

            self.logger.debug(f"Processing category: {category_tags}")

            # Find the corresponding listblock div that follows this h4
            listblock_div = h4.find_next_sibling('div', class_='listblock')
            if not listblock_div:
                continue

            # Find all article links in this category
            for link in listblock_div.find_all('a', href=True):
                href = link.get('href', '')
                title = link.get_text(strip=True)

                # Filter for actual article links (content_*.htm pattern)
                if (href and title and
                    href.startswith('content_') and href.endswith('.htm') and
                    len(title) > 3 and  # Reasonable title length
                    not any(skip in title.lower() for skip in ['廣告', 'advertisement', '節目表'])):

                    # Convert relative URLs to absolute
                    year = self.target_date.year
                    month = self.target_date.month
                    day = self.target_date.day
                    base_url = f"http://www.macaodaily.com/html/{year}-{month:02d}/{day:02d}/"
                    full_url = urljoin(base_url, href)

                    links.append({
                        'url': full_url,
                        'title': title,
                        'tags': category_tags.copy()  # Copy tags for this article
                    })

        # Remove duplicates (same URL might appear in multiple categories)
        seen_urls = set()
        unique_links = []
        for link in links:
            if link['url'] not in seen_urls:
                seen_urls.add(link['url'])
                unique_links.append(link)
            else:
                # If URL already exists, merge tags
                for existing_link in unique_links:
                    if existing_link['url'] == link['url']:
                        # Merge tags without duplicates
                        existing_tags = set(existing_link['tags'])
                        new_tags = set(link['tags'])
                        existing_link['tags'] = list(existing_tags.union(new_tags))
                        break

        self.logger.info(f"Found {len(unique_links)} unique article links across all categories")
        return unique_links

    def _extract_category_tags(self, h4_element: Tag) -> List[str]:
        """
        Extract category tags from h4 element.
        Example: "A01：澳聞" -> ["A01", "澳聞"]
        """
        tags = []

        # Find the link inside the h4 that contains the category text
        # Pattern: <a href="node_2.htm">A01：澳聞</a>
        category_link = h4_element.find('a', href=lambda x: x and x.startswith('node_'))

        if category_link:
            category_text = category_link.get_text(strip=True)

            # Parse category text like "A01：澳聞" or "A07：澳聞／特刋"
            if '：' in category_text:
                # Split by colon
                parts = category_text.split('：', 1)
                page_number = parts[0].strip()  # e.g., "A01"
                category_name = parts[1].strip()  # e.g., "澳聞" or "澳聞／特刋"

                # Add page number tag
                if page_number:
                    tags.append(page_number)

                # Handle category name with potential subcategories
                if '／' in category_name:
                    # Split subcategories like "澳聞／特刋"
                    subcategories = category_name.split('／')
                    for subcat in subcategories:
                        subcat = subcat.strip()
                        if subcat:
                            tags.append(subcat)
                else:
                    # Single category
                    if category_name:
                        tags.append(category_name)
            else:
                # Fallback: use the whole text as a single tag
                if category_text:
                    tags.append(category_text)

        self.logger.debug(f"Extracted tags from h4: {tags}")
        return tags
    
    def _scrape_individual_article(self, link_info: dict) -> ScrapedArticle:
        """
        Scrape an individual article page.
        """
        url = link_info['url']
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = response.apparent_encoding
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract article data
            title = self._extract_title(soup, link_info['title'])
            content_html = self._extract_content(soup)
            publish_date = self._extract_publish_date(soup, url)
            author = self._extract_author(soup)
            images = self._extract_images(soup, url)
            tags = link_info.get('tags', [])  # Get tags from link_info

            if not content_html:
                self.logger.warning(f"No content found for article: {url}")
                return None

            return ScrapedArticle(
                source_site_name=self.source_site_name,
                source_site_url=self.source_site_url,
                original_url=url,
                original_title=title,
                content_html=content_html,
                publish_date=publish_date,
                author=author,
                images=images,
                tags=tags
            )
            
        except Exception as e:
            self.logger.error(f"Error scraping article {url}: {e}")
            return None
    
    def _extract_title(self, soup: BeautifulSoup, fallback_title: str) -> str:
        """Extract article title from the specific strong element with styling."""
        # Look for the title in the specific strong element with font styling
        # <strong style="font-size:23px;font-family:黑体;line-height:30px;"> 巴士撞六旬途人疑無讓先  </strong>

        for strong in soup.find_all('strong'):
            style = strong.get('style', '')
            text = strong.get_text(strip=True)

            # Check if this strong element has the title styling
            if ('font-size:23px' in style or 'font-family:黑体' in style or 'line-height:30px' in style):
                if (len(text) > 5 and len(text) < 100 and
                    not any(skip in text for skip in ['版面導航', '上一篇', '下一篇', '放大', '縮小', '設為首頁', '返回主頁']) and
                    not text.startswith('第')):
                    return text

        # Fallback: look for any strong element with reasonable title text
        for strong in soup.find_all('strong'):
            text = strong.get_text(strip=True)
            if (len(text) > 5 and len(text) < 100 and
                '。' not in text and '！' not in text and '？' not in text and
                not any(skip in text for skip in ['版面導航', '上一篇', '下一篇', '放大', '縮小', '設為首頁', '返回主頁']) and
                not text.startswith('第') and
                not text.isdigit()):
                return text

        # Last resort: clean up fallback title
        if fallback_title and len(fallback_title) > 5:
            clean_title = fallback_title
            for remove in ['content_', '.htm', 'node_', '上一版', '下一版', '本版標題導航']:
                clean_title = clean_title.replace(remove, '').strip()
            if len(clean_title) > 5:
                return clean_title

        return "澳門日報新聞"
    
    def _extract_content(self, soup: BeautifulSoup) -> str:
        """Extract main article content using the specific HTML structure."""

        # Method 1: Look for <founder-content> tag (most reliable)
        founder_content = soup.find('founder-content')
        if founder_content:
            self.logger.debug("Found content in <founder-content> tag")
            return self._clean_founder_content(founder_content)

        # Method 2: Look for <div id="ozoom"> (backup)
        ozoom_div = soup.find('div', id='ozoom')
        if ozoom_div:
            self.logger.debug("Found content in <div id='ozoom'>")
            # Look for founder-content inside ozoom
            founder_content = ozoom_div.find('founder-content')
            if founder_content:
                return self._clean_founder_content(founder_content)
            # If no founder-content, use the ozoom div itself
            return self._clean_founder_content(ozoom_div)

        # Method 3: Look for content in tbody with specific styling (fallback)
        for tbody in soup.find_all('tbody'):
            td = tbody.find('td', style=lambda x: x and 'PADDING:0px 30px 0px 10px' in x)
            if td:
                self.logger.debug("Found content in styled tbody/td")
                # Look for founder-content or ozoom inside
                founder_content = td.find('founder-content')
                if founder_content:
                    return self._clean_founder_content(founder_content)
                ozoom_div = td.find('div', id='ozoom')
                if ozoom_div:
                    return self._clean_founder_content(ozoom_div)

        self.logger.warning("Could not find content using standard selectors")
        return ""

    def _clean_founder_content(self, element: Tag) -> str:
        """Clean and format content from founder-content or ozoom elements."""
        import copy

        # Create a copy to avoid modifying the original
        clean_element = copy.deepcopy(element)

        # Remove unwanted child elements
        for unwanted in clean_element.find_all(['script', 'style', 'nav', 'header', 'footer']):
            unwanted.decompose()

        # Get all paragraph elements
        paragraphs = clean_element.find_all('p')

        if paragraphs:
            # Process existing paragraphs
            cleaned_paragraphs = []

            for p in paragraphs:
                # Get text and clean up
                text = p.get_text()

                # Remove excessive whitespace and non-breaking spaces
                text = re.sub(r'\s+', ' ', text)  # Replace multiple spaces with single space
                text = text.replace('\u00a0', ' ')  # Replace &nbsp; with regular space
                text = text.strip()

                # Skip empty paragraphs or paragraphs with only whitespace
                if not text or len(text) < 5:
                    continue

                # Skip paragraphs that are just spacing or formatting
                if text in ['', ' ', '　', '&nbsp;']:
                    continue

                cleaned_paragraphs.append(text)

            # Create clean HTML structure
            if cleaned_paragraphs:
                html_content = '<div class="article-content">\n'
                for para in cleaned_paragraphs:
                    html_content += f'<p>{para}</p>\n'
                html_content += '</div>'
                return html_content

        else:
            # If no paragraphs found, try to extract text and create paragraphs
            text = clean_element.get_text()
            text = re.sub(r'\s+', ' ', text)
            text = text.replace('\u00a0', ' ')
            text = text.strip()

            if text and len(text) > 20:
                # Split into paragraphs based on content structure
                paragraphs = self._create_paragraphs_from_text(text)
                if paragraphs:
                    html_content = '<div class="article-content">\n'
                    for para in paragraphs:
                        html_content += f'<p>{para}</p>\n'
                    html_content += '</div>'
                    return html_content

        return ""

    def _create_paragraphs_from_text(self, text: str) -> List[str]:
        """Create paragraphs from plain text."""
        # Split by common paragraph indicators
        lines = text.split('\n')
        paragraphs = []
        current_paragraph = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check if this should start a new paragraph
            is_new_paragraph = (
                line.startswith(('【', '　　')) or  # Special markers
                (current_paragraph and
                 current_paragraph[-1].endswith(('。', '！', '？')) and
                 len(' '.join(current_paragraph)) > 50)
            )

            if is_new_paragraph and current_paragraph:
                paragraph_text = ' '.join(current_paragraph).strip()
                if len(paragraph_text) > 10:
                    paragraphs.append(paragraph_text)
                current_paragraph = [line]
            else:
                current_paragraph.append(line)

        # Add the last paragraph
        if current_paragraph:
            paragraph_text = ' '.join(current_paragraph).strip()
            if len(paragraph_text) > 10:
                paragraphs.append(paragraph_text)

        return paragraphs

    def _extract_images(self, soup: BeautifulSoup, article_url: str) -> List[Dict[str, str]]:
        """Extract images from the article with their descriptions."""
        images = []

        # Look for the image table structure as described in the HTML pattern
        # Images are in tables with specific structure containing <img> and description

        # Find all tables that might contain images
        for table in soup.find_all('table'):
            # Look for tables with image content
            img_elements = table.find_all('img')

            for img in img_elements:
                src = img.get('src', '')
                if not src:
                    continue

                # Skip template images and layout images
                if any(skip in src for skip in ['tplimg/', 'logo/', 'icon/', 'button/', 'bg_']):
                    continue

                # Focus on actual news images (usually in res/ directory)
                if not ('res/' in src or 'upload/' in src or 'photo/' in src):
                    continue

                # Convert relative URLs to absolute URLs
                if src.startswith('../../../'):
                    # Handle relative paths like "../../../res/1/20250528/95901748368328798.jpg"
                    # Extract the base URL from article_url
                    base_url = self._get_base_url_for_images(article_url)
                    absolute_src = src.replace('../../../', base_url)
                elif src.startswith('/'):
                    # Handle absolute paths from root
                    absolute_src = f"http://www.macaodaily.com{src}"
                elif src.startswith('http'):
                    # Already absolute URL
                    absolute_src = src
                else:
                    # Relative path, construct from base
                    base_url = self._get_base_url_for_images(article_url)
                    absolute_src = f"{base_url}{src}"

                # Look for image description in the same table
                description = self._extract_image_description(table, img)

                # Create image entry
                image_entry = {
                    'src': absolute_src,
                    'description': description or ''
                }

                # Avoid duplicates
                if not any(existing['src'] == absolute_src for existing in images):
                    images.append(image_entry)
                    self.logger.debug(f"Found image: {absolute_src} - {description}")

        # self.logger.info(f"Extracted {len(images)} images from article")
        return images

    def _get_base_url_for_images(self, article_url: str) -> str:
        """Get the base URL for resolving relative image paths."""
        # Article URL format: http://www.macaodaily.com/html/2025-05/28/content_1835104.htm
        # Image paths are relative to: http://www.macaodaily.com/
        # Note: article_url parameter kept for potential future use with different URL patterns
        return "http://www.macaodaily.com/"

    def _extract_image_description(self, table: Tag, img: Tag) -> str:
        """Extract description for an image from the table structure."""
        # Look for description in the same table
        # Based on the HTML pattern, descriptions are in <font size="2"> elements

        # First, try to find description in the same row or nearby
        img_row = img.find_parent('tr')
        if img_row:
            # Look for the next row that might contain description
            next_row = img_row.find_next_sibling('tr')
            if next_row:
                font_element = next_row.find('font', size='2')
                if font_element:
                    description = font_element.get_text(strip=True)
                    if description and len(description) > 3:  # Reasonable description length
                        return description

        # Fallback: look for any font size="2" in the same table
        for font in table.find_all('font', size='2'):
            text = font.get_text(strip=True)
            if text and len(text) > 3 and len(text) < 200:  # Reasonable description
                return text

        # Last resort: look for any text near the image
        img_cell = img.find_parent('td')
        if img_cell:
            # Get all text from the cell, excluding the image alt text
            texts = []
            for text_node in img_cell.stripped_strings:
                if text_node and len(text_node) > 3 and text_node != img.get('alt', ''):
                    texts.append(text_node)

            if texts:
                return ' '.join(texts)

        return ""




    
    def _clean_content_element(self, element: Tag):
        """Clean unwanted elements from content."""
        # Remove unwanted elements
        for unwanted in element.find_all(['script', 'style', 'nav', 'header', 'footer', 'aside']):
            unwanted.decompose()
        
        # Remove elements with unwanted classes/ids
        unwanted_patterns = ['ad', 'advertisement', 'share', 'comment', 'related', '廣告', '分享', '評論']
        for pattern in unwanted_patterns:
            for elem in element.find_all(attrs={'class': re.compile(pattern, re.I)}):
                elem.decompose()
            for elem in element.find_all(attrs={'id': re.compile(pattern, re.I)}):
                elem.decompose()
    
    def _extract_publish_date(self, soup: BeautifulSoup, url: str) -> str:
        """Extract publish date."""
        # Try to extract from URL pattern (common in Macao Daily)
        url_date_match = re.search(r'(\d{4})-(\d{1,2})/(\d{1,2})', url)
        if url_date_match:
            year, month, day = url_date_match.groups()
            return f"{year}-{month.zfill(2)}-{day.zfill(2)}T00:00:00+08:00"
        
        # Try to find date in page content
        date_selectors = [
            '.publish-date',
            '.date',
            '.article-date',
            'time'
        ]
        
        for selector in date_selectors:
            element = soup.select_one(selector)
            if element:
                date_text = element.get_text(strip=True)
                # Try to parse common Chinese date formats
                parsed_date = self._parse_chinese_date(date_text)
                if parsed_date:
                    return parsed_date
        
        # Fallback to current date
        return datetime.now().strftime("%Y-%m-%dT%H:%M:%S+08:00")
    
    def _parse_chinese_date(self, date_text: str) -> str:
        """Parse Chinese date formats."""
        # Common patterns in Chinese news sites
        patterns = [
            r'(\d{4})年(\d{1,2})月(\d{1,2})日',
            r'(\d{4})-(\d{1,2})-(\d{1,2})',
            r'(\d{4})/(\d{1,2})/(\d{1,2})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, date_text)
            if match:
                year, month, day = match.groups()
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}T00:00:00+08:00"
        
        return None
    
    def _extract_author(self, soup: BeautifulSoup) -> str:
        """Extract author information."""
        author_selectors = [
            '.author',
            '.byline',
            '.article-author',
            '.writer'
        ]
        
        for selector in author_selectors:
            element = soup.select_one(selector)
            if element:
                author = element.get_text(strip=True)
                if author and len(author) < 50:  # Reasonable author name length
                    return author
        
        return None
