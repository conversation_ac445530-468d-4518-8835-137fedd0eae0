"""
Pydantic models for API integration that match the server's expected schema.
"""

from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime


class FeaturedImageSchema(BaseModel):
    """Schema for featured image in API requests."""
    type: Literal["base64", "external"]
    data: str  # Base64 string or external URL
    filename: Optional[str] = None  # Required for base64, optional for external
    alt: Optional[str] = None


class MetaSchema(BaseModel):
    """Schema for meta information."""
    title: Optional[str] = None
    description: Optional[str] = None


class PostSchema(BaseModel):
    """Schema for individual post in API requests."""
    title: str
    slug: str
    content: str  # Markdown content
    summary: Optional[str] = None
    source_url: Optional[HttpUrl] = None
    source_site_name: Optional[str] = None
    extra: Optional[Dict[str, Any]] = None  # For arbitrary JSON metadata
    categories: Optional[List[str]] = None
    meta: Optional[MetaSchema] = None
    publishedAt: Optional[str] = None
    status: Literal["draft", "published"] = Field(default="draft", alias="_status")
    featuredImage: Optional[FeaturedImageSchema] = None

    class Config:
        populate_by_name = True  # Allow both field name and alias


class RequestSchema(BaseModel):
    """Schema for the complete API request."""
    posts: List[PostSchema]


class ApiResponseSchema(BaseModel):
    """Schema for API response."""
    success: bool
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    errors: Optional[List[str]] = None


class UploadStatusSchema(BaseModel):
    """Schema for tracking upload status."""
    article_id: str
    slug: str
    title: str
    status: Literal["pending", "uploaded", "failed", "skipped"]
    uploaded_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0


class BatchUploadResultSchema(BaseModel):
    """Schema for batch upload results."""
    batch_id: str
    total_articles: int
    successful_uploads: int
    failed_uploads: int
    skipped_uploads: int
    upload_statuses: List[UploadStatusSchema]
    started_at: datetime
    completed_at: Optional[datetime] = None
    errors: Optional[List[str]] = None
