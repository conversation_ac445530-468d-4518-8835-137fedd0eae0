"""
Image processing utilities for API uploads.
"""

import logging
import base64
import requests
from typing import Optional, Dict, <PERSON>, Tuple
from urllib.parse import urlparse, urljoin
from pathlib import Path
import mimetypes
from PIL import Image
import io

from src.api.models.api_schemas import FeaturedImageSchema
from config.settings import get_settings

logger = logging.getLogger(__name__)


class ImageProcessor:
    """Handles image processing for API uploads."""
    
    def __init__(self):
        self.settings = get_settings()
        self.supported_formats = {'jpg', 'jpeg', 'png', 'gif', 'webp'}
        self.max_size_bytes = self.settings.api.max_image_size_mb * 1024 * 1024
    
    def process_image(self, image_data: Dict[str, Any], slug: str = None) -> Optional[FeaturedImageSchema]:
        """
        Process image data and return FeaturedImageSchema.

        Args:
            image_data: Dictionary containing image information
            slug: Article slug to use as base filename (optional)

        Returns:
            FeaturedImageSchema or None if processing fails
        """
        try:
            # Extract image URL and metadata
            image_url = self._extract_image_url(image_data)
            if not image_url:
                return None
            
            alt_text = self._extract_alt_text(image_data)
            
            # Validate URL
            if not self._is_valid_image_url(image_url):
                logger.warning(f"Invalid image URL: {image_url}")
                return None
            
            # Process based on format preference
            if self.settings.api.image_format == "external":
                return self._create_external_image(image_url, alt_text)
            else:
                return self._create_base64_image(image_url, alt_text, slug)
                
        except Exception as e:
            logger.warning(f"Failed to process image: {e}")
            return None
    
    def _extract_image_url(self, image_data: Dict[str, Any]) -> Optional[str]:
        """Extract image URL from various data formats."""
        if isinstance(image_data, str):
            return image_data
        
        if isinstance(image_data, dict):
            # Try common field names
            for field in ['src', 'url', 'href', 'link']:
                if field in image_data and image_data[field]:
                    return str(image_data[field])
        
        return None
    
    def _extract_alt_text(self, image_data: Dict[str, Any]) -> str:
        """Extract alt text from image data."""
        if isinstance(image_data, dict):
            for field in ['alt', 'description', 'title', 'caption']:
                if field in image_data and image_data[field]:
                    return str(image_data[field])
        
        return ""
    
    def _is_valid_image_url(self, url: str) -> bool:
        """Check if URL is a valid image URL."""
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Check file extension
            path = Path(parsed.path.lower())
            if path.suffix.lstrip('.') in self.supported_formats:
                return True
            
            # If no extension, we'll try to download and check
            return True
            
        except Exception:
            return False
    
    def _create_external_image(self, url: str, alt_text: str) -> FeaturedImageSchema:
        """Create external image schema."""
        return FeaturedImageSchema(
            type="external",
            data=url,
            alt=alt_text
        )
    
    def _create_base64_image(self, url: str, alt_text: str, slug: str = None) -> Optional[FeaturedImageSchema]:
        """Create base64 image schema by downloading and encoding."""
        try:
            # Download image
            response = requests.get(url, timeout=30, stream=True)
            response.raise_for_status()
            
            # Check content type
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                logger.warning(f"URL does not return image content: {url}")
                return None
            
            # Read content with size limit
            content = b''
            for chunk in response.iter_content(chunk_size=8192):
                content += chunk
                if len(content) > self.max_size_bytes:
                    logger.warning(f"Image too large (>{self.max_size_bytes} bytes): {url}")
                    return None
            
            # Validate and potentially optimize image
            processed_content, filename = self._process_image_content(content, url, slug)
            if not processed_content:
                return None
            
            # Encode to base64 with proper data URI format
            base64_string = base64.b64encode(processed_content).decode('utf-8')

            # Determine MIME type from content type or filename
            mime_type = self._get_mime_type(content_type, filename)
            data_uri = f"data:{mime_type};base64,{base64_string}"

            return FeaturedImageSchema(
                type="base64",
                data=data_uri,
                filename=filename,
                alt=alt_text
            )
            
        except requests.RequestException as e:
            logger.warning(f"Failed to download image {url}: {e}")
            return None
        except Exception as e:
            logger.warning(f"Failed to process image {url}: {e}")
            return None
    
    def _process_image_content(self, content: bytes, url: str, slug: str = None) -> Tuple[Optional[bytes], str]:
        """Process and potentially optimize image content."""
        try:
            # Use slug as base filename if provided, otherwise extract from URL
            if slug:
                filename = f"{slug}.jpg"  # Default to .jpg, will be updated based on actual format
            else:
                parsed_url = urlparse(url)
                filename = Path(parsed_url.path).name or "image.jpg"
            
            # Try to open with PIL for validation and optimization
            try:
                with Image.open(io.BytesIO(content)) as img:
                    # Validate format
                    if img.format.lower() not in ['jpeg', 'jpg', 'png', 'gif', 'webp']:
                        logger.warning(f"Unsupported image format: {img.format}")
                        return None, filename
                    
                    # Check if optimization is needed
                    if len(content) > self.max_size_bytes * 0.8:  # 80% of max size
                        return self._optimize_image(img, filename, slug)
                    
                    # Update filename with correct extension, preserving slug if used
                    if slug:
                        if img.format.lower() == 'jpeg':
                            filename = f"{slug}.jpg"
                        else:
                            filename = f"{slug}.{img.format.lower()}"
                    else:
                        if img.format.lower() == 'jpeg':
                            filename = Path(filename).with_suffix('.jpg').name
                        else:
                            filename = Path(filename).with_suffix(f'.{img.format.lower()}').name
                    
                    return content, filename
                    
            except Exception as e:
                logger.warning(f"PIL processing failed, using original content: {e}")
                return content, filename
                
        except Exception as e:
            logger.warning(f"Failed to process image content: {e}")
            return None, "image.jpg"
    
    def _optimize_image(self, img: Image.Image, filename: str, slug: str = None) -> Tuple[Optional[bytes], str]:
        """Optimize image to reduce file size."""
        try:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create white background for transparency
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                img = background
            
            # Calculate new size to fit within limit
            quality = 85
            max_dimension = 1920  # Max width or height
            
            # Resize if too large
            if max(img.size) > max_dimension:
                ratio = max_dimension / max(img.size)
                new_size = tuple(int(dim * ratio) for dim in img.size)
                img = img.resize(new_size, Image.Resampling.LANCZOS)
            
            # Save with optimization
            output = io.BytesIO()
            img.save(output, format='JPEG', quality=quality, optimize=True)
            optimized_content = output.getvalue()
            
            # Update filename, preserving slug if used
            if slug:
                filename = f"{slug}.jpg"
            else:
                filename = Path(filename).with_suffix('.jpg').name
            
            logger.info(f"Optimized image: {len(optimized_content)} bytes")
            return optimized_content, filename
            
        except Exception as e:
            logger.warning(f"Failed to optimize image: {e}")
            return None, filename

    def _get_mime_type(self, content_type: str, filename: str) -> str:
        """Determine MIME type from content type or filename."""
        # First try to get from HTTP content-type header
        if content_type and content_type.startswith('image/'):
            return content_type.split(';')[0]  # Remove any additional parameters

        # Fall back to filename extension
        extension = Path(filename).suffix.lower().lstrip('.')
        mime_mapping = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'webp': 'image/webp',
            'bmp': 'image/bmp',
            'svg': 'image/svg+xml'
        }

        return mime_mapping.get(extension, 'image/jpeg')  # Default to JPEG
