"""
Data transformation service for converting rephrased articles to API format.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

from src.api.models.api_schemas import (
    PostSchema, MetaSchema, FeaturedImageSchema, RequestSchema
)
from src.api.utils.image_processor import ImageProcessor
from src.core.models.rephrased_article import RephrasedArticle
from config.settings import get_settings

logger = logging.getLogger(__name__)


class DataTransformer:
    """Transforms rephrased articles to API-compatible format."""
    
    def __init__(self):
        self.settings = get_settings()
        self.image_processor = ImageProcessor()
    
    def transform_article(self, article: RephrasedArticle, publication_date: Optional[str] = None) -> PostSchema:
        """
        Transform a single rephrased article to API format.
        
        Args:
            article: RephrasedArticle instance
            
        Returns:
            PostSchema instance ready for API submission
        """
        try:
            # Basic fields mapping
            post_data = {
                "title": article.title,
                "slug": article.slug,
                "content": article.content,
                "summary": article.summary,
                "source_url": article.source_url if article.source_url else None,
                "source_site_name": article.source_site_name,
                "status": self._map_status(getattr(article, 'status', 'draft')),
                "publishedAt": self._parse_datetime(article.publish_date or publication_date),
            }
            
            # Categories mapping
            if hasattr(article, 'categories') and article.categories:
                post_data["categories"] = article.categories
            
            # Meta information
            meta_data = {}
            if hasattr(article, 'meta_title') and article.meta_title:
                meta_data["title"] = article.meta_title
            if hasattr(article, 'meta_description') and article.meta_description:
                meta_data["description"] = article.meta_description
            
            if meta_data:
                post_data["meta"] = MetaSchema(**meta_data)
            
            # Extra metadata
            extra_data = self._build_extra_metadata(article)
            if extra_data:
                post_data["extra"] = extra_data
            
            # Featured image
            featured_image = self._extract_featured_image(article)
            if featured_image:
                post_data["featuredImage"] = featured_image
            
            return PostSchema(**post_data)
            
        except Exception as e:
            logger.error(f"Failed to transform article {getattr(article, 'id', 'unknown')}: {e}")
            raise
    
    def transform_articles_batch(self, articles: List[RephrasedArticle], publication_date: Optional[str] = None) -> RequestSchema:
        """
        Transform a batch of articles to API request format.
        
        Args:
            articles: List of RephrasedArticle instances
            
        Returns:
            RequestSchema instance ready for API submission
        """
        transformed_posts = []
        
        for article in articles:
            try:
                post = self.transform_article(article, publication_date)
                transformed_posts.append(post)
            except Exception as e:
                logger.warning(f"Skipping article {getattr(article, 'id', 'unknown')} due to transformation error: {e}")
                continue
        
        return RequestSchema(posts=transformed_posts)
    
    def _map_status(self, status: str) -> str:
        """Map article status to API format."""
        status_mapping = {
            "draft": "draft",
            "published": "published",
            "pending": "draft",
            "ready": "draft"
        }
        return status_mapping.get(status.lower(), self.settings.api.default_status)
    
    def _parse_datetime(self, timestamp) -> Optional[str]:
        """Parse various datetime formats to ISO string format."""
        if not timestamp:
            return None

        dt_obj = None

        if isinstance(timestamp, datetime):
            dt_obj = timestamp
        elif isinstance(timestamp, str):
            try:
                # Try ISO format first
                dt_obj = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            except ValueError:
                try:
                    # Try common formats
                    for fmt in [
                        "%Y-%m-%d %H:%M:%S.%f",
                        "%Y-%m-%d %H:%M:%S",
                        "%Y-%m-%dT%H:%M:%S.%f",
                        "%Y-%m-%dT%H:%M:%S"
                    ]:
                        dt_obj = datetime.strptime(timestamp, fmt)
                        break
                except ValueError:
                    logger.warning(f"Could not parse datetime: {timestamp}")
                    return None

        if dt_obj:
            # Return ISO format string with timezone that Zod expects
            # If no timezone info, assume UTC
            if dt_obj.tzinfo is None:
                dt_obj = dt_obj.replace(tzinfo=timezone.utc)

            # Format without microseconds and use 'Z' for UTC
            dt_obj = dt_obj.replace(microsecond=0)
            iso_string = dt_obj.isoformat()

            # Replace '+00:00' with 'Z' for UTC (common Zod expectation)
            if iso_string.endswith('+00:00'):
                iso_string = iso_string[:-6] + 'Z'

            return iso_string

        return None
    
    def _build_extra_metadata(self, article: RephrasedArticle) -> Dict[str, Any]:
        """Build extra metadata from article attributes."""
        extra = {}
        
        # Add processing metadata
        if hasattr(article, 'processing_metadata') and article.processing_metadata:
            extra["processing_metadata"] = article.processing_metadata
        
        # Add original article information
        if hasattr(article, 'original_article_id') and article.original_article_id:
            extra["original_article_id"] = article.original_article_id
        
        # Add AI model information
        if hasattr(article, 'ai_model_used') and article.ai_model_used:
            extra["ai_model_used"] = article.ai_model_used
        
        # Add tags
        if hasattr(article, 'tags') and article.tags:
            extra["tags"] = article.tags
        
        # Add keywords
        if hasattr(article, 'keywords') and article.keywords:
            extra["keywords"] = article.keywords
        
        # Add priority and quality scores
        if hasattr(article, 'priority_score'):
            extra["priority_score"] = article.priority_score
        
        if hasattr(article, 'quality_score'):
            extra["quality_score"] = article.quality_score
        
        return extra
    
    def _extract_featured_image(self, article: RephrasedArticle) -> Optional[FeaturedImageSchema]:
        """Extract and process featured image from article."""
        if not self.settings.api.include_images:
            return None

        # Check if article has images
        images = getattr(article, 'images', None)
        if not images or not isinstance(images, list) or len(images) == 0:
            return None

        # Use the first image as featured image
        first_image = images[0]

        try:
            # Use article slug as the base filename
            return self.image_processor.process_image(first_image, article.slug)
        except Exception as e:
            logger.warning(f"Failed to process featured image: {e}")
            return None

