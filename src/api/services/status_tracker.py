"""
Upload status tracking service for managing article upload states.
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, date
from pathlib import Path
import sqlite3
from contextlib import contextmanager

from src.api.models.api_schemas import UploadStatusSchema, BatchUploadResultSchema
from config.settings import get_settings

logger = logging.getLogger(__name__)


class UploadStatusTracker:
    """Tracks upload status of articles with persistent storage."""
    
    def __init__(self, use_database: bool = False):
        self.settings = get_settings()
        self.use_database = use_database
        
        if use_database:
            self.db_path = self.settings.data.processed_data_path / "upload_status.db"
            self._init_database()
        else:
            self.json_path = self.settings.data.processed_data_path / "upload_status.json"
            self.status_cache: Dict[str, UploadStatusSchema] = {}
            self._load_json_status()
    
    def _init_database(self):
        """Initialize SQLite database for status tracking."""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        with self._get_db_connection() as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS upload_status (
                    article_id TEXT PRIMARY KEY,
                    slug TEXT NOT NULL,
                    title TEXT NOT NULL,
                    status TEXT NOT NULL,
                    uploaded_at TIMESTAMP,
                    error_message TEXT,
                    retry_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS batch_results (
                    batch_id TEXT PRIMARY KEY,
                    source TEXT NOT NULL,
                    target_date TEXT NOT NULL,
                    total_articles INTEGER NOT NULL,
                    successful_uploads INTEGER NOT NULL,
                    failed_uploads INTEGER NOT NULL,
                    skipped_uploads INTEGER NOT NULL,
                    started_at TIMESTAMP NOT NULL,
                    completed_at TIMESTAMP,
                    errors TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_upload_status_status ON upload_status(status)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_batch_results_date ON batch_results(target_date)
            """)
    
    @contextmanager
    def _get_db_connection(self):
        """Get database connection with proper error handling."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def _load_json_status(self):
        """Load status from JSON file."""
        try:
            if self.json_path.exists():
                with open(self.json_path, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)
                    self.status_cache = {
                        k: UploadStatusSchema(**v) for k, v in status_data.items()
                    }
                logger.info(f"Loaded upload status for {len(self.status_cache)} articles")
        except Exception as e:
            logger.warning(f"Failed to load upload status: {e}")
            self.status_cache = {}
    
    def _save_json_status(self):
        """Save status to JSON file."""
        try:
            self.json_path.parent.mkdir(parents=True, exist_ok=True)
            status_data = {
                k: v.model_dump() for k, v in self.status_cache.items()
            }
            with open(self.json_path, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, indent=2, default=str, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save upload status: {e}")
    
    def get_status(self, article_id: str) -> Optional[UploadStatusSchema]:
        """Get upload status for an article."""
        if self.use_database:
            return self._get_status_db(article_id)
        else:
            return self.status_cache.get(article_id)
    
    def _get_status_db(self, article_id: str) -> Optional[UploadStatusSchema]:
        """Get status from database."""
        try:
            with self._get_db_connection() as conn:
                row = conn.execute(
                    "SELECT * FROM upload_status WHERE article_id = ?",
                    (article_id,)
                ).fetchone()
                
                if row:
                    return UploadStatusSchema(
                        article_id=row['article_id'],
                        slug=row['slug'],
                        title=row['title'],
                        status=row['status'],
                        uploaded_at=datetime.fromisoformat(row['uploaded_at']) if row['uploaded_at'] else None,
                        error_message=row['error_message'],
                        retry_count=row['retry_count']
                    )
        except Exception as e:
            logger.error(f"Failed to get status from database: {e}")
        
        return None
    
    def update_status(self, status: UploadStatusSchema):
        """Update upload status for an article."""
        if self.use_database:
            self._update_status_db(status)
        else:
            self.status_cache[status.article_id] = status
            self._save_json_status()
    
    def _update_status_db(self, status: UploadStatusSchema):
        """Update status in database."""
        try:
            with self._get_db_connection() as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO upload_status 
                    (article_id, slug, title, status, uploaded_at, error_message, retry_count, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (
                    status.article_id,
                    status.slug,
                    status.title,
                    status.status,
                    status.uploaded_at.isoformat() if status.uploaded_at else None,
                    status.error_message,
                    status.retry_count
                ))
        except Exception as e:
            logger.error(f"Failed to update status in database: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get upload statistics."""
        if self.use_database:
            return self._get_statistics_db()
        else:
            total = len(self.status_cache)
            uploaded = sum(1 for s in self.status_cache.values() if s.status == "uploaded")
            failed = sum(1 for s in self.status_cache.values() if s.status == "failed")
            pending = sum(1 for s in self.status_cache.values() if s.status == "pending")
            
            return {
                "total_articles": total,
                "uploaded": uploaded,
                "failed": failed,
                "pending": pending,
                "success_rate": (uploaded / total * 100) if total > 0 else 0
            }
    
    def _get_statistics_db(self) -> Dict[str, Any]:
        """Get statistics from database."""
        try:
            with self._get_db_connection() as conn:
                stats = conn.execute("""
                    SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'uploaded' THEN 1 ELSE 0 END) as uploaded,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending
                    FROM upload_status
                """).fetchone()
                
                total = stats['total']
                uploaded = stats['uploaded']
                failed = stats['failed']
                pending = stats['pending']
                
                return {
                    "total_articles": total,
                    "uploaded": uploaded,
                    "failed": failed,
                    "pending": pending,
                    "success_rate": (uploaded / total * 100) if total > 0 else 0
                }
        except Exception as e:
            logger.error(f"Failed to get statistics from database: {e}")
            return {"total_articles": 0, "uploaded": 0, "failed": 0, "pending": 0, "success_rate": 0}
    
    def reset_failed_uploads(self) -> int:
        """Reset failed uploads to pending status."""
        if self.use_database:
            return self._reset_failed_db()
        else:
            reset_count = 0
            for status in self.status_cache.values():
                if status.status == "failed":
                    status.status = "pending"
                    status.error_message = None
                    status.retry_count += 1
                    reset_count += 1
            
            if reset_count > 0:
                self._save_json_status()
            
            return reset_count
    
    def _reset_failed_db(self) -> int:
        """Reset failed uploads in database."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.execute("""
                    UPDATE upload_status 
                    SET status = 'pending', error_message = NULL, retry_count = retry_count + 1, updated_at = CURRENT_TIMESTAMP
                    WHERE status = 'failed'
                """)
                return cursor.rowcount
        except Exception as e:
            logger.error(f"Failed to reset failed uploads in database: {e}")
            return 0
    
    def save_batch_result(self, result: BatchUploadResultSchema, source: str, target_date: str):
        """Save batch upload result."""
        if self.use_database:
            self._save_batch_result_db(result, source, target_date)
        # For JSON mode, we don't save batch results separately
    
    def _save_batch_result_db(self, result: BatchUploadResultSchema, source: str, target_date: str):
        """Save batch result to database."""
        try:
            with self._get_db_connection() as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO batch_results 
                    (batch_id, source, target_date, total_articles, successful_uploads, failed_uploads, 
                     skipped_uploads, started_at, completed_at, errors)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    result.batch_id,
                    source,
                    target_date,
                    result.total_articles,
                    result.successful_uploads,
                    result.failed_uploads,
                    result.skipped_uploads,
                    result.started_at.isoformat(),
                    result.completed_at.isoformat() if result.completed_at else None,
                    json.dumps(result.errors) if result.errors else None
                ))
        except Exception as e:
            logger.error(f"Failed to save batch result to database: {e}")
    
    def get_pending_articles(self) -> List[UploadStatusSchema]:
        """Get all articles with pending status."""
        if self.use_database:
            return self._get_pending_articles_db()
        else:
            return [s for s in self.status_cache.values() if s.status == "pending"]
    
    def _get_pending_articles_db(self) -> List[UploadStatusSchema]:
        """Get pending articles from database."""
        try:
            with self._get_db_connection() as conn:
                rows = conn.execute(
                    "SELECT * FROM upload_status WHERE status = 'pending' ORDER BY created_at"
                ).fetchall()
                
                return [
                    UploadStatusSchema(
                        article_id=row['article_id'],
                        slug=row['slug'],
                        title=row['title'],
                        status=row['status'],
                        uploaded_at=datetime.fromisoformat(row['uploaded_at']) if row['uploaded_at'] else None,
                        error_message=row['error_message'],
                        retry_count=row['retry_count']
                    )
                    for row in rows
                ]
        except Exception as e:
            logger.error(f"Failed to get pending articles from database: {e}")
            return []
