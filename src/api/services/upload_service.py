"""
Main API upload service that orchestrates the upload process.
"""

import asyncio
import json
import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date, timedelta
from pathlib import Path
import uuid

from src.api.clients.http_client import ApiHttpClient
from src.api.services.data_transformer import DataTransformer
from src.api.models.api_schemas import (
    RequestSchema, BatchUploadResultSchema, UploadStatusSchema
)
from src.core.models.rephrased_article import RephrasedArticle
from config.settings import get_settings

logger = logging.getLogger(__name__)


class ApiUploadService:
    """Service for uploading rephrased articles to API server."""
    
    def __init__(self):
        self.settings = get_settings()
        self.transformer = DataTransformer()
        self.upload_status_file = self.settings.data.processed_data_path / "upload_status.json"
        self.upload_status: Dict[str, UploadStatusSchema] = {}
        self._load_upload_status()
    
    def _load_upload_status(self):
        """Load upload status from file."""
        try:
            if self.upload_status_file.exists():
                with open(self.upload_status_file, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)
                    self.upload_status = {
                        k: UploadStatusSchema(**v) for k, v in status_data.items()
                    }
                logger.info(f"Loaded upload status for {len(self.upload_status)} articles")
        except Exception as e:
            logger.warning(f"Failed to load upload status: {e}")
            self.upload_status = {}

    def _extract_publication_date_from_filename(self, file_path: Path) -> Optional[str]:
        """
        Extract publication date from filename (e.g., 20250321.json -> 2025-03-21T00:00:00+08:00).

        Args:
            file_path: Path to the rephrased JSON file

        Returns:
            ISO format date string with timezone, or None if extraction fails
        """
        try:
            # Extract date from filename (e.g., 20250321.json)
            filename = file_path.stem  # Remove .json extension

            # Match YYYYMMDD pattern
            date_match = re.match(r'^(\d{4})(\d{2})(\d{2})$', filename)
            if not date_match:
                logger.warning(f"Could not extract date from filename: {filename}")
                return None

            year, month, day = date_match.groups()

            # Create date string in ISO format with UTC timezone (Z format)
            # Use 00:00:00 as the time since we only have the date
            publication_date = f"{year}-{month}-{day}T00:00:00Z"

            # Validate the date
            datetime.fromisoformat(publication_date.replace('Z', '+00:00'))

            return publication_date

        except Exception as e:
            logger.warning(f"Failed to extract publication date from {file_path}: {e}")
            return None
    
    def _save_upload_status(self):
        """Save upload status to file."""
        try:
            self.upload_status_file.parent.mkdir(parents=True, exist_ok=True)
            status_data = {
                k: v.model_dump() for k, v in self.upload_status.items()
            }
            with open(self.upload_status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, indent=2, default=str, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save upload status: {e}")
    
    async def upload_rephrased_file(self, file_path: Path, limit: Optional[int] = None) -> BatchUploadResultSchema:
        """
        Upload articles from a rephrased file.

        Args:
            file_path: Path to the rephrased JSON file
            limit: Maximum number of articles to upload (None for all)

        Returns:
            BatchUploadResultSchema with upload results
        """
        logger.info(f"Starting upload for file: {file_path}")
        
        batch_id = str(uuid.uuid4())
        started_at = datetime.now()
        
        try:
            # Load rephrased articles
            articles = self._load_rephrased_articles(file_path)
            if not articles:
                logger.warning(f"No articles found in {file_path}")
                return BatchUploadResultSchema(
                    batch_id=batch_id,
                    total_articles=0,
                    successful_uploads=0,
                    failed_uploads=0,
                    skipped_uploads=0,
                    upload_statuses=[],
                    started_at=started_at,
                    completed_at=datetime.now()
                )
            
            # Filter articles that haven't been uploaded yet
            pending_articles = self._filter_pending_articles(articles)
            logger.info(f"Found {len(pending_articles)} articles to upload (out of {len(articles)} total)")

            # Apply limit if specified
            if limit and len(pending_articles) > limit:
                pending_articles = pending_articles[:limit]
                logger.info(f"Limited to {limit} articles for upload")

            if not pending_articles:
                logger.info("No articles to upload")
                return BatchUploadResultSchema(
                    batch_id=batch_id,
                    total_articles=len(articles),
                    successful_uploads=0,
                    failed_uploads=0,
                    skipped_uploads=len(articles),
                    upload_statuses=[],
                    started_at=started_at,
                    completed_at=datetime.now()
                )
            
            # Extract publication date from filename
            publication_date = self._extract_publication_date_from_filename(file_path)

            # Upload articles in batches
            upload_results = await self._upload_articles_in_batches(pending_articles, batch_id, publication_date)
            
            # Update upload status
            self._save_upload_status()
            
            completed_at = datetime.now()
            logger.info(f"Upload completed for {file_path}: {upload_results['successful']} successful, {upload_results['failed']} failed")
            
            # Calculate correct totals when limit is applied
            total_processed = len(pending_articles)
            total_skipped = len(articles) - total_processed

            return BatchUploadResultSchema(
                batch_id=batch_id,
                total_articles=total_processed,
                successful_uploads=upload_results['successful'],
                failed_uploads=upload_results['failed'],
                skipped_uploads=total_skipped,
                upload_statuses=upload_results['statuses'],
                started_at=started_at,
                completed_at=completed_at,
                errors=upload_results.get('errors', [])
            )
            
        except Exception as e:
            logger.error(f"Failed to upload file {file_path}: {e}")
            return BatchUploadResultSchema(
                batch_id=batch_id,
                total_articles=0,
                successful_uploads=0,
                failed_uploads=1,
                skipped_uploads=0,
                upload_statuses=[],
                started_at=started_at,
                completed_at=datetime.now(),
                errors=[str(e)]
            )
    
    def _load_rephrased_articles(self, file_path: Path) -> List[RephrasedArticle]:
        """Load rephrased articles from JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            articles = []
            for article_data in data.get('articles', []):
                try:
                    article = RephrasedArticle.from_dict(article_data)
                    articles.append(article)
                except Exception as e:
                    logger.warning(f"Failed to parse article {article_data.get('id', 'unknown')}: {e}")
                    continue
            
            return articles
            
        except Exception as e:
            logger.error(f"Failed to load rephrased file {file_path}: {e}")
            return []
    
    def _filter_pending_articles(self, articles: List[RephrasedArticle]) -> List[RephrasedArticle]:
        """Filter articles that haven't been uploaded yet."""
        pending = []
        
        for article in articles:
            article_id = getattr(article, 'id', None)
            if not article_id:
                continue
            
            status = self.upload_status.get(article_id)
            if not status or status.status in ['pending', 'failed']:
                pending.append(article)
            else:
                logger.debug(f"Skipping already uploaded article: {article.title}")
        
        return pending
    
    async def _upload_articles_in_batches(self, articles: List[RephrasedArticle], batch_id: str, publication_date: Optional[str] = None) -> Dict[str, Any]:
        """Upload articles in batches."""
        successful = 0
        failed = 0
        statuses = []
        errors = []
        
        batch_size = self.settings.api.batch_size
        total_batches = (len(articles) + batch_size - 1) // batch_size
        
        async with ApiHttpClient() as client:
            for i in range(0, len(articles), batch_size):
                batch_articles = articles[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                
                logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch_articles)} articles)")
                
                try:
                    # Transform articles to API format
                    request_data = self.transformer.transform_articles_batch(batch_articles, publication_date)
                    
                    if not request_data.posts:
                        logger.warning(f"No valid posts in batch {batch_num}, skipping")
                        continue
                    
                    # Upload batch
                    response = await client.upload_posts(request_data)
                    
                    if response.success:
                        # Mark articles as uploaded
                        for article in batch_articles:
                            article_id = getattr(article, 'id', None)
                            if article_id:
                                status = UploadStatusSchema(
                                    article_id=article_id,
                                    slug=getattr(article, 'slug', ''),
                                    title=getattr(article, 'title', ''),
                                    status="uploaded",
                                    uploaded_at=datetime.now()
                                )
                                self.upload_status[article_id] = status
                                statuses.append(status)
                                successful += 1
                        
                        logger.info(f"Batch {batch_num} uploaded successfully")
                    else:
                        # Mark articles as failed
                        error_msg = response.message or "Upload failed"
                        errors.append(f"Batch {batch_num}: {error_msg}")
                        
                        for article in batch_articles:
                            article_id = getattr(article, 'id', None)
                            if article_id:
                                status = UploadStatusSchema(
                                    article_id=article_id,
                                    slug=getattr(article, 'slug', ''),
                                    title=getattr(article, 'title', ''),
                                    status="failed",
                                    error_message=error_msg
                                )
                                self.upload_status[article_id] = status
                                statuses.append(status)
                                failed += 1
                        
                        logger.error(f"Batch {batch_num} failed: {error_msg}")
                
                except Exception as e:
                    error_msg = f"Batch {batch_num} processing error: {e}"
                    errors.append(error_msg)
                    logger.error(error_msg)
                    
                    # Mark articles as failed
                    for article in batch_articles:
                        article_id = getattr(article, 'id', None)
                        if article_id:
                            status = UploadStatusSchema(
                                article_id=article_id,
                                slug=getattr(article, 'slug', ''),
                                title=getattr(article, 'title', ''),
                                status="failed",
                                error_message=str(e)
                            )
                            self.upload_status[article_id] = status
                            statuses.append(status)
                            failed += 1
                
                # Rate limiting delay between batches
                if i + batch_size < len(articles):
                    await asyncio.sleep(self.settings.api.rate_limit_delay)
        
        return {
            'successful': successful,
            'failed': failed,
            'statuses': statuses,
            'errors': errors
        }

    async def upload_date_range(self, source: str, start_date: date, end_date: date) -> List[BatchUploadResultSchema]:
        """
        Upload rephrased articles for a date range.

        Args:
            source: Source name (e.g., 'macaodaily')
            start_date: Start date
            end_date: End date

        Returns:
            List of BatchUploadResultSchema for each date
        """
        results = []
        current_date = start_date

        while current_date <= end_date:
            date_str = current_date.strftime('%Y%m%d')
            year = current_date.strftime('%Y')

            file_path = (
                self.settings.data.processed_data_path /
                "rephrased" / source / year / f"{date_str}.json"
            )

            if file_path.exists():
                logger.info(f"Uploading {source} articles for {current_date}")
                result = await self.upload_rephrased_file(file_path)
                results.append(result)
            else:
                logger.info(f"No rephrased file found for {current_date}: {file_path}")

            current_date = date(current_date.year, current_date.month, current_date.day) + timedelta(days=1)

        return results

    def get_upload_statistics(self) -> Dict[str, Any]:
        """Get upload statistics."""
        total = len(self.upload_status)
        uploaded = sum(1 for s in self.upload_status.values() if s.status == "uploaded")
        failed = sum(1 for s in self.upload_status.values() if s.status == "failed")
        pending = sum(1 for s in self.upload_status.values() if s.status == "pending")

        return {
            "total_articles": total,
            "uploaded": uploaded,
            "failed": failed,
            "pending": pending,
            "success_rate": (uploaded / total * 100) if total > 0 else 0
        }

    def reset_failed_uploads(self):
        """Reset failed uploads to pending status."""
        reset_count = 0
        for status in self.upload_status.values():
            if status.status == "failed":
                status.status = "pending"
                status.error_message = None
                status.retry_count += 1
                reset_count += 1

        if reset_count > 0:
            self._save_upload_status()
            logger.info(f"Reset {reset_count} failed uploads to pending")

        return reset_count
