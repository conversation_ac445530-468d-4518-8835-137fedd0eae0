"""
HTTP client for API communication with authentication, retry logic, and rate limiting.
"""

import asyncio
import logging
from typing import Optional
import json
import time

import httpx
from httpx import AsyncClient, Response

from src.api.models.api_schemas import RequestSchema, ApiResponseSchema
from config.settings import get_settings

logger = logging.getLogger(__name__)


class ApiRateLimiter:
    """Rate limiter for API requests."""
    
    def __init__(self, max_requests_per_minute: int = 60):
        self.max_requests_per_minute = max_requests_per_minute
        self.requests = []
        self.lock = asyncio.Lock()
    
    async def acquire_permit(self) -> bool:
        """Acquire a permit to make a request."""
        async with self.lock:
            now = time.time()
            # Remove requests older than 1 minute
            self.requests = [req_time for req_time in self.requests if now - req_time < 60]
            
            if len(self.requests) >= self.max_requests_per_minute:
                # Calculate wait time
                oldest_request = min(self.requests)
                wait_time = 60 - (now - oldest_request)
                if wait_time > 0:
                    logger.info(f"Rate limit reached, waiting {wait_time:.1f} seconds")
                    await asyncio.sleep(wait_time)
                    return await self.acquire_permit()
            
            self.requests.append(now)
            return True


class ApiHttpClient:
    """HTTP client for API communication."""
    
    def __init__(self):
        self.settings = get_settings()
        self.rate_limiter = ApiRateLimiter(self.settings.api.max_requests_per_minute)
        self.client: Optional[AsyncClient] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._initialize_client()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.client:
            await self.client.aclose()
    
    async def _initialize_client(self):
        """Initialize the HTTP client with proper configuration."""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "NewsUploader/1.0"
        }
        
        # Add authentication headers
        print(f"Token: {self.settings.api.token}")
        if self.settings.api.token:
            headers["Authorization"] = f"Bearer {self.settings.api.token}"
        
        self.client = AsyncClient(
            timeout=httpx.Timeout(self.settings.api.timeout),
            headers=headers,
            follow_redirects=True
        )
    
    async def upload_posts(self, request_data: RequestSchema) -> ApiResponseSchema:
        """
        Upload posts to the API server.
        
        Args:
            request_data: RequestSchema containing posts to upload
            
        Returns:
            ApiResponseSchema with the response
        """
        if not self.client:
            await self._initialize_client()
        
        # Acquire rate limit permit
        await self.rate_limiter.acquire_permit()
        
        # Prepare request
        url = self.settings.api.full_url
        payload = request_data.model_dump(exclude_none=True, mode='json')
        
        logger.info(f"Uploading {len(request_data.posts)} posts to {url}")
        logger.debug(f"Request payload: {json.dumps(payload, indent=2, default=str)}")
        
        # Make request with retry logic
        return await self._make_request_with_retry("POST", url, json=payload)
    
    async def _make_request_with_retry(
        self, 
        method: str, 
        url: str, 
        **kwargs
    ) -> ApiResponseSchema:
        """Make HTTP request with retry logic."""
        last_exception = None
        
        for attempt in range(self.settings.api.max_retries + 1):
            try:
                response = await self.client.request(method, url, **kwargs)
                return await self._handle_response(response)
                
            except httpx.TimeoutException as e:
                last_exception = e
                logger.warning(f"Request timeout (attempt {attempt + 1}/{self.settings.api.max_retries + 1}): {e}")
                
            except httpx.ConnectError as e:
                last_exception = e
                logger.warning(f"Connection error (attempt {attempt + 1}/{self.settings.api.max_retries + 1}): {e}")
                
            except httpx.HTTPStatusError as e:
                # Don't retry on client errors (4xx)
                if 400 <= e.response.status_code < 500:
                    return await self._handle_response(e.response)
                
                last_exception = e
                logger.warning(f"HTTP error {e.response.status_code} (attempt {attempt + 1}/{self.settings.api.max_retries + 1}): {e}")
                
            except Exception as e:
                last_exception = e
                logger.warning(f"Unexpected error (attempt {attempt + 1}/{self.settings.api.max_retries + 1}): {e}")
            
            # Wait before retry (exponential backoff)
            if attempt < self.settings.api.max_retries:
                wait_time = self.settings.api.retry_delay * (self.settings.api.retry_backoff ** attempt)
                logger.info(f"Waiting {wait_time:.1f} seconds before retry...")
                await asyncio.sleep(wait_time)
        
        # All retries failed
        error_msg = f"Request failed after {self.settings.api.max_retries + 1} attempts: {last_exception}"
        logger.error(error_msg)
        return ApiResponseSchema(
            success=False,
            message=error_msg,
            errors=[str(last_exception)]
        )
    
    async def _handle_response(self, response: Response) -> ApiResponseSchema:
        """Handle HTTP response and convert to ApiResponseSchema."""
        try:
            # Log response details
            logger.debug(f"Response status: {response.status_code}")
            logger.debug(f"Response headers: {dict(response.headers)}")
            
            # Try to parse JSON response
            try:
                response_data = response.json()
                logger.debug(f"Response data: {json.dumps(response_data, indent=2, default=str)}")
            except json.JSONDecodeError:
                response_data = {"raw_response": response.text}
                logger.debug(f"Non-JSON response: {response.text}")
            
            # Check if request was successful
            if response.is_success:
                return ApiResponseSchema(
                    success=True,
                    message=response_data.get("message", "Upload successful"),
                    data=response_data
                )
            else:
                # Handle error responses
                error_message = response_data.get("message", f"HTTP {response.status_code}")

                # Ensure errors is a list of strings
                errors = response_data.get("errors", [])
                if not isinstance(errors, list):
                    errors = [str(errors)]

                # Convert any non-string errors to strings
                errors = [str(error) for error in errors]

                # If no errors in response, use the error message
                if not errors:
                    errors = [error_message]

                return ApiResponseSchema(
                    success=False,
                    message=error_message,
                    data=response_data,
                    errors=errors
                )
                
        except Exception as e:
            logger.error(f"Failed to handle response: {e}")
            return ApiResponseSchema(
                success=False,
                message=f"Failed to process response: {e}",
                errors=[str(e)]
            )
    
    async def test_connection(self) -> bool:
        """Test connection to the API server."""
        try:
            if not self.client:
                await self._initialize_client()
            
            # Try a simple GET request to the API URL (remove /posts and add /health)
            api_url = self.settings.api.api_url.rstrip('/')
            if api_url.endswith('/posts'):
                base_url = api_url[:-6]  # Remove '/posts'
            else:
                base_url = api_url
            response = await self.client.get(f"{base_url}/health", timeout=10)
            
            logger.info(f"Connection test: {response.status_code}")
            return response.is_success
            
        except Exception as e:
            logger.warning(f"Connection test failed: {e}")
            return False
