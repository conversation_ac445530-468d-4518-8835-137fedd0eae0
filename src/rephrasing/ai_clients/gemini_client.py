"""
Gemini API client for article rephrasing using OpenAI library.

This module provides a client for Google's Gemini 2.5 Flash model using
the OpenAI-compatible endpoint with structured outputs and comprehensive
rate limiting and error handling.
"""

from typing import Dict, Any, List
import logging
from datetime import datetime

from openai import AsyncOpenAI
from openai._types import NOT_GIVEN

from src.core.models.article import ScrapedArticle
from src.core.models.rephrased_article import RephrasedArticle
from src.rephrasing.utils.rate_limiter import Gemini<PERSON><PERSON><PERSON>imiter
from src.rephrasing.templates.prompts import PromptBuilder, estimate_tokens
from src.rephrasing.models.response_models import (
    RephrasedContent, ArticleSummary, ArticleClassification,
    QualityAssessment, TitleOptimization, BatchRephrasedContent
)
from config.settings import get_settings

logger = logging.getLogger(__name__)


class GeminiAPIError(Exception):
    """Base exception for Gemini API errors."""
    pass


class GeminiRateLimitError(GeminiAPIError):
    """Exception for rate limit errors."""
    pass


class GeminiQuotaExceededError(GeminiAPIError):
    """Exception for quota exceeded errors."""
    pass


class GeminiClient:
    """Client for Google Gemini 2.5 Flash API using OpenAI library."""

    def __init__(self, api_key: str = None, rate_limiter: GeminiRateLimiter = None):
        """Initialize Gemini client."""
        self.settings = get_settings()
        self.api_key = api_key or self.settings.rephrasing.api_key
        self.rate_limiter = rate_limiter or GeminiRateLimiter()
        self.prompt_builder = PromptBuilder()

        if not self.api_key:
            raise ValueError("Gemini API key is required")

        # Initialize OpenAI client with Gemini endpoint
        # Disable retries to prevent wasting quota on exhausted limits
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
            timeout=300,
            max_retries=0  # Disable automatic retries to prevent quota waste
        )
        self.model = self.settings.rephrasing.model_name

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.close()
    
    async def _make_structured_request(self, messages: list, response_format, max_tokens: int = None):
        """Make a structured request to Gemini API using OpenAI library."""

        # Estimate tokens for rate limiting
        prompt_text = " ".join([msg["content"] for msg in messages])
        estimated_tokens = estimate_tokens(prompt_text) + (max_tokens or 4000)

        # Acquire rate limit permit
        if not await self.rate_limiter.acquire_permit(estimated_tokens):
            raise GeminiRateLimitError("Rate limit exceeded, cannot make request")

        try:
            # Make structured request using OpenAI library
            request_params = {
                "model": self.model,
                "messages": messages,
                "response_format": response_format,
                "temperature": self.settings.rephrasing.temperature,
                "top_p": 0.95,
                "timeout": 300
            }

            # Only add max_tokens if specified
            if max_tokens:
                request_params["max_tokens"] = max_tokens

            # Log request details for debugging
            logger.debug(f"Making API request with model: {self.model}")
            logger.debug(f"Request params: {list(request_params.keys())}")
            logger.debug(f"Message count: {len(messages)}")
            logger.debug(f"Estimated input tokens: {estimated_tokens}")

            completion = await self.client.chat.completions.parse(**request_params)

            # Record successful request
            self.rate_limiter.record_request(estimated_tokens)

            return completion.choices[0].message.parsed

        except Exception as e:
            # Concise error handling - detailed logging is done at batch level
            error_str = str(e).lower()

            if "rate limit" in error_str or "429" in error_str:
                self.rate_limiter.record_error("rate_limit")
                raise GeminiRateLimitError(f"Rate limit exceeded: {e}")
            elif "quota" in error_str or "403" in error_str:
                self.rate_limiter.record_error("quota_exceeded")
                raise GeminiQuotaExceededError(f"Quota exceeded: {e}")
            elif "context_length" in error_str or "token" in error_str:
                self.rate_limiter.record_error("api_error")
                raise GeminiAPIError(f"Token limit exceeded: {e}")
            elif "timeout" in error_str:
                self.rate_limiter.record_error("api_error")
                raise GeminiAPIError(f"Request timeout: {e}")
            else:
                self.rate_limiter.record_error("api_error")
                raise GeminiAPIError(f"API error: {e}")

    async def _make_simple_request(self, messages: list, max_tokens: int = None) -> str:
        """Make a simple text request to Gemini API."""

        # Estimate tokens for rate limiting
        prompt_text = " ".join([msg["content"] for msg in messages])
        estimated_tokens = estimate_tokens(prompt_text) + (max_tokens or 4000)

        # Acquire rate limit permit
        if not await self.rate_limiter.acquire_permit(estimated_tokens):
            raise GeminiRateLimitError("Rate limit exceeded, cannot make request")

        try:
            # Make simple chat completion request
            request_params = {
                "model": self.model,
                "messages": messages,
                "temperature": self.settings.rephrasing.temperature,
                "top_p": 0.95
            }

            # Only add max_tokens if specified
            if max_tokens:
                request_params["max_tokens"] = max_tokens

            completion = await self.client.chat.completions.create(**request_params)

            # Record successful request
            self.rate_limiter.record_request(estimated_tokens)

            return completion.choices[0].message.content

        except Exception as e:
            # Handle different types of errors
            error_str = str(e).lower()

            if "rate limit" in error_str or "429" in error_str:
                self.rate_limiter.record_error("rate_limit")
                raise GeminiRateLimitError(f"Rate limit exceeded: {e}")
            elif "quota" in error_str or "403" in error_str:
                self.rate_limiter.record_error("quota_exceeded")
                raise GeminiQuotaExceededError(f"Quota exceeded: {e}")
            else:
                self.rate_limiter.record_error("api_error")
                raise GeminiAPIError(f"API error: {e}")
    

    
    async def rephrase_article(self, article: ScrapedArticle) -> RephrasedArticle:
        """Rephrase a single article using Gemini API with structured output."""

        logger.info(f"Rephrasing article: {article.title[:50]}...")

        try:
            # Build messages for structured request
            system_prompt = """你是一位專業的新聞編輯和內容創作者。請將新聞文章重新改寫，使其更適合現代讀者閱讀，同時保持新聞的準確性和客觀性。

改寫要求：
1. 保持事實準確性，不添加或修改任何事實信息
2. 使用更現代、流暢的中文表達
3. 改善文章結構，使邏輯更清晰，使用Markdown格式
4. 保持新聞的客觀性和專業性
5. 適當精簡冗餘內容，提高可讀性
6. 保留重要的數據、日期、人名等關鍵信息
7. 生成SEO友好的英文slug（只能包含英文字母、數字和連字符）
8. 創建吸引人的SEO標題和描述
9. 選擇1-3個最相關的分類：
   - 澳門新聞：澳門本地新聞、市民生活、本地事件
   - 政府政策：澳門政府公告、政策法規、施政報告
   - 經濟財經：經濟、博彩、旅遊、地產、金融
   - 社會民生：民生問題、社會議題、社區發展
   - 國際新聞：國際要聞、全球動態
   - 體育新聞：體育賽事、運動相關
   - 文化生活：文化、娛樂、美食、生活方式
   - 科技新聞：科技創新、數位發展
   - 大灣區新聞：粵港澳大灣區發展、跨境合作
   - 健康醫療：公共衛生、醫療科技、健康保健
   - 教育學習：校園新聞、教育政策、學習資源
10. 提供簡潔的摘要供快速閱讀"""

            # Use markdown content if available (better structure)
            content = getattr(article, 'content_markdown', None) or article.content
            # No trimming - preserve full content

            tags_str = ", ".join(article.tags) if article.tags else "未分類"

            user_prompt = f"""請改寫以下新聞文章：

**原始文章信息：**
標題：{article.title}
來源：{article.source_site_name}
分類：{tags_str}

**原始內容：**
{content}"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            # Make structured API request
            rephrased_content = await self._make_structured_request(
                messages=messages,
                response_format=RephrasedContent
            )

            # Create rephrased article from structured response
            rephrased_article = RephrasedArticle(
                title=rephrased_content.title or article.title,
                slug=rephrased_content.slug,
                content=rephrased_content.content,
                summary=rephrased_content.summary,
                original_article_id=article.id,
                source_url=article.source_url,
                source_site_name=article.source_site_name,
                ai_model_used=self.model,
                processing_timestamp=datetime.now(),
                processing_metadata={
                    "original_title": article.title,
                    "original_content_length": len(article.content),
                    "rephrased_content_length": len(rephrased_content.content),
                    "prompt_tokens": estimate_tokens(user_prompt),
                    "response_tokens": estimate_tokens(rephrased_content.content)
                },
                categories=rephrased_content.categories,
                tags=article.tags,  # Keep original tags for reference
                keywords=rephrased_content.keywords,
                priority_score=0.8,  # Default high priority for rephrased content
                quality_score=0.0,   # Will be assessed separately
                meta_title=rephrased_content.meta_title,
                meta_description=rephrased_content.meta_description,
                status="draft",  # Default to draft status
                images=[getattr(article, 'images', [])[0]] if getattr(article, 'images', []) else []  # Include only first image
            )

            logger.info(f"Successfully rephrased article: {rephrased_article.title[:50]}...")
            return rephrased_article

        except Exception as e:
            logger.error(f"Failed to rephrase article {article.title[:50]}: {e}")
            raise

    async def rephrase_articles_batch(self, articles: List[ScrapedArticle]) -> List[RephrasedArticle]:
        """Rephrase multiple articles in a single API request to save quota."""

        if not articles:
            return []

        # Allow larger batch sizes but with warnings for very large batches
        if len(articles) > 15:
            logger.warning(f"Batch size {len(articles)} exceeds maximum of 15. Processing first 15 articles.")
            articles = articles[:15]
        elif len(articles) > 10:
            logger.info(f"Large batch size {len(articles)} - this may use more tokens.")

        logger.info(f"Batch rephrasing {len(articles)} articles...")

        # Log detailed batch information for debugging
        total_content_length = sum(len(getattr(a, 'content_markdown', '') or a.content) for a in articles)
        logger.debug(f"Batch details: {len(articles)} articles, ~{total_content_length} characters total")
        logger.debug(f"Average content length per article: ~{total_content_length // len(articles)} characters")

        # Retry logic for network/timeout issues (not quota issues)
        max_retries = 2
        retry_count = 0

        while retry_count <= max_retries:
            try:
                # Build batch prompt
                system_prompt = """你是一位專業的新聞編輯和內容創作者。請將以下多篇新聞文章重新改寫，使其更適合現代讀者閱讀，同時保持新聞的準確性和客觀性。

改寫要求：
1. 保持事實準確性，不添加或修改任何事實信息
2. 使用更現代、流暢的中文表達
3. 改善文章結構，使邏輯更清晰，使用Markdown格式
4. 保持新聞的客觀性和專業性
5. 適當精簡冗餘內容，提高可讀性
6. 保留重要的數據、日期、人名等關鍵信息
7. 生成SEO友好的英文slug（只能包含英文字母、數字和連字符）
8. 創建吸引人的SEO標題和描述
9. 選擇1-3個最相關的分類：
   - 澳門新聞：澳門本地新聞、市民生活、本地事件
   - 政府政策：澳門政府公告、政策法規、施政報告
   - 經濟財經：經濟、博彩、旅遊、地產、金融
   - 社會民生：民生問題、社會議題、社區發展
   - 國際新聞：國際要聞、全球動態
   - 體育新聞：體育賽事、運動相關
   - 文化生活：文化、娛樂、美食、生活方式
   - 科技新聞：科技創新、數位發展
   - 大灣區新聞：粵港澳大灣區發展、跨境合作
   - 健康醫療：公共衛生、醫療科技、健康保健
   - 教育學習：校園新聞、教育政策、學習資源
10. 提供簡潔的摘要供快速閱讀

請為每篇文章返回完整的改寫結果。"""

                # Prepare batch content
                user_prompt = "請改寫以下新聞文章：\n\n"

                for i, article in enumerate(articles, 1):
                    # Use markdown content if available (better structure)
                    content = getattr(article, 'content_markdown', None) or article.content
                    # No trimming - preserve full content for better quality

                    tags_str = ", ".join(article.tags) if article.tags else "未分類"

                    user_prompt += f"""**文章 {i}：**
標題：{article.title}
分類：{tags_str}
內容：{content}

---

"""

                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]

                # Make structured API request for batch
                # No max_tokens limit - let API handle token management naturally
                logger.debug(f"🚀 Making API request for batch of {len(articles)} articles")
                logger.debug(f"📊 Request details: {len(messages)} messages, ~{len(user_prompt)} characters in prompt")

                batch_response = await self._make_structured_request(
                    messages=messages,
                    response_format=BatchRephrasedContent
                )

                logger.debug(f"✅ API request completed successfully")

                # Create rephrased articles from batch response
                rephrased_articles = []

                for i, (original_article, rephrased_content) in enumerate(zip(articles, batch_response.articles)):
                    rephrased_article = RephrasedArticle(
                        title=rephrased_content.title or original_article.title,
                        slug=rephrased_content.slug,
                        content=rephrased_content.content,
                        summary=rephrased_content.summary,
                        original_article_id=original_article.id,
                        source_url=original_article.source_url,
                        source_site_name=original_article.source_site_name,
                        ai_model_used=self.model,
                        processing_timestamp=datetime.now(),
                        processing_metadata={
                            "original_title": original_article.title,
                            "original_content_length": len(original_article.content),
                            "rephrased_content_length": len(rephrased_content.content),
                            "batch_size": len(articles),
                            "batch_index": i,
                            "prompt_tokens": estimate_tokens(user_prompt) // len(articles),  # Approximate per article
                            "response_tokens": estimate_tokens(rephrased_content.content)
                        },
                        categories=rephrased_content.categories,
                        tags=original_article.tags,
                        keywords=rephrased_content.keywords,
                        priority_score=0.8,
                        quality_score=0.0,
                        meta_title=rephrased_content.meta_title,
                        meta_description=rephrased_content.meta_description,
                        status="draft",
                        images=[getattr(original_article, 'images', [])[0]] if getattr(original_article, 'images', []) else []  # Include only first image
                    )
                    rephrased_articles.append(rephrased_article)

                logger.info(f"Successfully batch rephrased {len(rephrased_articles)} articles")
                return rephrased_articles

            except Exception as e:
                # Concise error logging for batch processing failures
                error_str = str(e).lower()

                # Extract retry delay if available
                import re
                retry_match = re.search(r"retrydelay.*?(\d+)s", error_str)
                retry_delay = int(retry_match.group(1)) if retry_match else None

                # Check error type and log with full details
                if "rate" in error_str or "quota" in error_str or "limit" in error_str:
                    if "quota" in error_str or "resource_exhausted" in error_str:
                        logger.error(f"🚫 Daily quota exhausted (batch size: {len(articles)})")
                        if retry_delay:
                            logger.error(f"⏰ API suggests waiting {retry_delay}s until quota reset")
                        logger.error(f"📋 Full error details: {e}")
                        raise GeminiQuotaExceededError(f"Quota exceeded: {e}")
                    else:
                        logger.error(f"⏳ Rate limit hit (batch size: {len(articles)})")
                        if retry_delay:
                            logger.error(f"⏰ API suggests waiting {retry_delay}s before retry")
                        logger.error(f"📋 Full error details: {e}")
                        raise GeminiRateLimitError(f"Rate limit exceeded: {e}")
                elif "length limit" in error_str or "token" in error_str or "context_length" in error_str:
                    total_content_length = sum(len(getattr(a, 'content_markdown', '') or a.content) for a in articles)
                    logger.error(f"📏 Content too large: {total_content_length} chars for {len(articles)} articles")
                    logger.error(f"📋 Full error details: {e}")
                    return []
                elif "timeout" in error_str or "connection" in error_str:
                    retry_count += 1
                    if retry_count <= max_retries:
                        wait_time = 2 ** retry_count  # Exponential backoff: 2, 4, 8 seconds
                        logger.warning(f"🌐 Network/timeout issue (batch size: {len(articles)}) - Retry {retry_count}/{max_retries}")
                        logger.warning(f"⏳ Waiting {wait_time}s before retry...")
                        logger.error(f"📋 Full error details: {e}")
                        import asyncio
                        await asyncio.sleep(wait_time)
                        continue  # Continue the while loop to retry
                    else:
                        logger.error(f"🌐 Network/timeout issue (batch size: {len(articles)}) - Max retries exceeded")
                        logger.error(f"📋 Full error details: {e}")
                        return []
                elif "parse" in error_str or "json" in error_str:
                    logger.error(f"� Response parsing error (batch size: {len(articles)})")
                    logger.error(f"📋 Full error details: {e}")
                    return []
                else:
                    logger.error(f"❌ Unknown error: {type(e).__name__} (batch size: {len(articles)})")
                    logger.error(f"📋 Full error details: {e}")
                    return []

        # If we reach here, all retries were exhausted
        logger.error(f"❌ All retries exhausted for batch of {len(articles)} articles")
        return []
    
    async def test_connection(self) -> bool:
        """Test the API connection with a simple request."""
        try:
            messages = [
                {"role": "user", "content": "請說'連接成功'"}
            ]
            response_text = await self._make_simple_request(messages, max_tokens=10)
            return "成功" in response_text

        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False

    async def summarize_article(self, article: ScrapedArticle) -> ArticleSummary:
        """Generate a summary for an article using structured output."""
        try:
            content = getattr(article, 'content_markdown', None) or article.content

            messages = [
                {"role": "system", "content": "你是一位專業的新聞編輯。請為新聞文章生成簡潔的摘要和要點。"},
                {"role": "user", "content": f"請為以下文章生成摘要：\n\n標題：{article.title}\n內容：{content}"}
            ]

            return await self._make_structured_request(
                messages=messages,
                response_format=ArticleSummary,
                max_tokens=500
            )
        except Exception as e:
            logger.error(f"Failed to summarize article: {e}")
            raise

    async def classify_article(self, article: ScrapedArticle) -> ArticleClassification:
        """Classify an article using structured output."""
        try:
            content = getattr(article, 'content_markdown', None) or article.content
            tags_str = ", ".join(article.tags) if article.tags else "無"

            messages = [
                {"role": "system", "content": "你是一位專業的新聞分類專家。請為新聞文章進行準確的分類。"},
                {"role": "user", "content": f"請分類以下文章：\n\n標題：{article.title}\n內容：{content}\n原始標籤：{tags_str}"}
            ]

            return await self._make_structured_request(
                messages=messages,
                response_format=ArticleClassification,
                max_tokens=300
            )
        except Exception as e:
            logger.error(f"Failed to classify article: {e}")
            raise

    async def optimize_title(self, title: str, content: str) -> TitleOptimization:
        """Optimize an article title using structured output."""
        try:
            content_preview = content  # No trimming

            messages = [
                {"role": "system", "content": "你是一位專業的新聞標題編輯。請優化標題使其更吸引人但保持準確性。"},
                {"role": "user", "content": f"請優化以下標題：\n\n原標題：{title}\n文章內容：{content_preview}"}
            ]

            return await self._make_structured_request(
                messages=messages,
                response_format=TitleOptimization,
                max_tokens=300
            )
        except Exception as e:
            logger.error(f"Failed to optimize title: {e}")
            raise

    def get_quota_status(self) -> Dict[str, Any]:
        """Get current quota status."""
        return self.rate_limiter.get_quota_status()

    def get_wait_time(self, estimated_tokens: int = 1000) -> float:
        """Get the time to wait before making a request."""
        return self.rate_limiter.get_wait_time(estimated_tokens)
