"""
Rate limiter for Gemini API with quota management.

Handles the strict rate limits of Gemini 2.5 Flash free tier:
- 10 requests per minute (RPM)
- 250,000 tokens per minute (TPM)  
- 250 requests per day (RPD)
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)


@dataclass
class RateLimitState:
    """State tracking for rate limiting."""
    
    # Daily limits
    daily_requests: int = 0
    daily_reset_time: Optional[datetime] = None
    
    # Minute limits  
    minute_requests: int = 0
    minute_reset_time: Optional[datetime] = None
    
    # Token tracking
    tokens_used_today: int = 0
    tokens_used_this_minute: int = 0
    
    # Last request tracking
    last_request_time: Optional[datetime] = None
    
    # Error tracking
    consecutive_errors: int = 0
    last_error_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            "daily_requests": self.daily_requests,
            "daily_reset_time": self.daily_reset_time.isoformat() if self.daily_reset_time else None,
            "minute_requests": self.minute_requests,
            "minute_reset_time": self.minute_reset_time.isoformat() if self.minute_reset_time else None,
            "tokens_used_today": self.tokens_used_today,
            "tokens_used_this_minute": self.tokens_used_this_minute,
            "last_request_time": self.last_request_time.isoformat() if self.last_request_time else None,
            "consecutive_errors": self.consecutive_errors,
            "last_error_time": self.last_error_time.isoformat() if self.last_error_time else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RateLimitState':
        """Create from dictionary."""
        def parse_datetime(dt_str):
            return datetime.fromisoformat(dt_str) if dt_str else None
            
        return cls(
            daily_requests=data.get('daily_requests', 0),
            daily_reset_time=parse_datetime(data.get('daily_reset_time')),
            minute_requests=data.get('minute_requests', 0),
            minute_reset_time=parse_datetime(data.get('minute_reset_time')),
            tokens_used_today=data.get('tokens_used_today', 0),
            tokens_used_this_minute=data.get('tokens_used_this_minute', 0),
            last_request_time=parse_datetime(data.get('last_request_time')),
            consecutive_errors=data.get('consecutive_errors', 0),
            last_error_time=parse_datetime(data.get('last_error_time'))
        )


class GeminiRateLimiter:
    """Rate limiter for Gemini API with persistent state."""
    
    # Gemini 2.5 Flash free tier limits
    MAX_REQUESTS_PER_MINUTE = 10
    MAX_TOKENS_PER_MINUTE = 250000
    MAX_REQUESTS_PER_DAY = 250
    
    # Safety margins
    MIN_REQUEST_INTERVAL = 6.5  # Slightly more than 60/10 = 6 seconds
    
    def __init__(self, state_file: Optional[Path] = None):
        """Initialize rate limiter with optional persistent state."""
        self.state_file = state_file or Path("data/rephrasing_state.json")
        self.state = self._load_state()
        
    def _load_state(self) -> RateLimitState:
        """Load rate limiting state from disk."""
        if self.state_file.exists():
            try:
                with open(self.state_file, 'r') as f:
                    data = json.load(f)
                return RateLimitState.from_dict(data)
            except Exception as e:
                logger.warning(f"Failed to load rate limit state: {e}")
        
        return RateLimitState()
    
    def _save_state(self):
        """Save rate limiting state to disk."""
        try:
            self.state_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.state_file, 'w') as f:
                json.dump(self.state.to_dict(), f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save rate limit state: {e}")
    
    def _reset_counters_if_needed(self):
        """Reset counters if time windows have passed."""
        now = datetime.now()
        
        # Reset daily counter if it's a new day
        if (self.state.daily_reset_time is None or 
            now >= self.state.daily_reset_time):
            self.state.daily_requests = 0
            self.state.tokens_used_today = 0
            # Set next reset to tomorrow at midnight
            tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            self.state.daily_reset_time = tomorrow
            logger.info(f"Daily quota reset. Next reset: {tomorrow}")
        
        # Reset minute counter if it's a new minute
        if (self.state.minute_reset_time is None or 
            now >= self.state.minute_reset_time):
            self.state.minute_requests = 0
            self.state.tokens_used_this_minute = 0
            # Set next reset to next minute
            next_minute = now.replace(second=0, microsecond=0) + timedelta(minutes=1)
            self.state.minute_reset_time = next_minute
    
    def can_make_request(self, estimated_tokens: int = 1000) -> bool:
        """Check if a request can be made without exceeding limits."""
        self._reset_counters_if_needed()
        
        # Check daily limits
        if self.state.daily_requests >= self.MAX_REQUESTS_PER_DAY:
            return False

        # Note: Gemini 2.5 Flash has no daily token limit, only daily request limit
        
        # Check minute limits
        if self.state.minute_requests >= self.MAX_REQUESTS_PER_MINUTE:
            return False
        
        if self.state.tokens_used_this_minute + estimated_tokens > self.MAX_TOKENS_PER_MINUTE:
            return False
        
        # Check minimum interval since last request
        if self.state.last_request_time:
            time_since_last = datetime.now() - self.state.last_request_time
            if time_since_last.total_seconds() < self.MIN_REQUEST_INTERVAL:
                logger.debug(f"Rate limit: Need to wait {self.MIN_REQUEST_INTERVAL - time_since_last.total_seconds():.1f}s more")
                return False
        
        return True
    
    def get_wait_time(self, estimated_tokens: int = 1000) -> float:
        """Get the time to wait before making a request."""
        self._reset_counters_if_needed()
        
        wait_times = []
        
        # Check if we need to wait for daily reset
        if self.state.daily_requests >= self.MAX_REQUESTS_PER_DAY:
            if self.state.daily_reset_time:
                daily_wait = (self.state.daily_reset_time - datetime.now()).total_seconds()
                wait_times.append(max(0, daily_wait))
        
        # Check if we need to wait for minute reset
        if self.state.minute_requests >= self.MAX_REQUESTS_PER_MINUTE:
            if self.state.minute_reset_time:
                minute_wait = (self.state.minute_reset_time - datetime.now()).total_seconds()
                wait_times.append(max(0, minute_wait))
        
        # Check minimum interval since last request
        if self.state.last_request_time:
            time_since_last = datetime.now() - self.state.last_request_time
            interval_wait = self.MIN_REQUEST_INTERVAL - time_since_last.total_seconds()
            if interval_wait > 0:
                wait_times.append(interval_wait)
        
        return max(wait_times) if wait_times else 0
    
    async def acquire_permit(self, estimated_tokens: int = 1000) -> bool:
        """Acquire permission to make a request, waiting if necessary."""
        wait_time = self.get_wait_time(estimated_tokens)
        
        if wait_time > 0:
            logger.info(f"Rate limiting: waiting {wait_time:.1f} seconds")
            await asyncio.sleep(wait_time)
        
        # Double-check after waiting
        if not self.can_make_request(estimated_tokens):
            logger.warning("Still cannot make request after waiting")
            return False
        
        return True
    
    def record_request(self, tokens_used: int = 1000):
        """Record a successful request."""
        now = datetime.now()
        
        self._reset_counters_if_needed()
        
        # Update counters
        self.state.daily_requests += 1
        self.state.minute_requests += 1
        self.state.tokens_used_today += tokens_used
        self.state.tokens_used_this_minute += tokens_used
        self.state.last_request_time = now
        
        # Reset error tracking on successful request
        self.state.consecutive_errors = 0
        
        self._save_state()
        
        logger.debug(f"Request recorded. Daily: {self.state.daily_requests}/{self.MAX_REQUESTS_PER_DAY}, "
                    f"Minute: {self.state.minute_requests}/{self.MAX_REQUESTS_PER_MINUTE}")
    
    def record_error(self, error_type: str = "unknown"):
        """Record a failed request."""
        now = datetime.now()
        
        self.state.consecutive_errors += 1
        self.state.last_error_time = now
        
        self._save_state()
        
        logger.warning(f"Error recorded: {error_type}. Consecutive errors: {self.state.consecutive_errors}")
    
    def get_backoff_delay(self) -> float:
        """Get exponential backoff delay based on consecutive errors."""
        if self.state.consecutive_errors == 0:
            return 0
        
        # Exponential backoff: 2^errors seconds, capped at 300 seconds (5 minutes)
        delay = min(2 ** self.state.consecutive_errors, 300)
        return delay
    
    def get_quota_status(self) -> Dict[str, Any]:
        """Get current quota usage status."""
        self._reset_counters_if_needed()
        
        return {
            "daily_requests": self.state.daily_requests,
            "daily_limit": self.MAX_REQUESTS_PER_DAY,
            "daily_remaining": self.MAX_REQUESTS_PER_DAY - self.state.daily_requests,
            "minute_requests": self.state.minute_requests,
            "minute_limit": self.MAX_REQUESTS_PER_MINUTE,
            "tokens_used_today": self.state.tokens_used_today,
            "tokens_used_this_minute": self.state.tokens_used_this_minute,
            "daily_reset_time": self.state.daily_reset_time.isoformat() if self.state.daily_reset_time else None,
            "minute_reset_time": self.state.minute_reset_time.isoformat() if self.state.minute_reset_time else None,
            "consecutive_errors": self.state.consecutive_errors,
            "can_make_request": self.can_make_request()
        }
