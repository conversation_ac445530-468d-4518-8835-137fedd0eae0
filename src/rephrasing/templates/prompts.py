"""
Prompt templates for AI article rephrasing.

This module contains carefully crafted prompts for different types of content
and rephrasing objectives.
"""

from typing import Dict, Any
from src.core.models.article import ScrapedArticle


class PromptTemplate:
    """Base class for prompt templates."""
    
    def __init__(self, template: str, variables: Dict[str, str] = None):
        """Initialize with template string and variable descriptions."""
        self.template = template
        self.variables = variables or {}
    
    def format(self, **kwargs) -> str:
        """Format the template with provided variables."""
        return self.template.format(**kwargs)


class NewsRephrasingPrompts:
    """Collection of prompts for news article rephrasing."""
    
    # Main rephrasing prompt (now handled by structured output)
    REPHRASE_ARTICLE = PromptTemplate("""
你是一位專業的新聞編輯和內容創作者。請將以下新聞文章重新改寫，使其更適合現代讀者閱讀，同時保持新聞的準確性和客觀性。

**原始文章信息：**
標題：{original_title}
來源：{source_site_name}
分類：{tags}

**原始內容：**
{original_content}

**改寫要求：**
1. 保持事實準確性，不添加或修改任何事實信息
2. 使用更現代、流暢的中文表達
3. 改善文章結構，使邏輯更清晰，使用Markdown格式
4. 保持新聞的客觀性和專業性
5. 適當精簡冗餘內容，提高可讀性
6. 保留重要的數據、日期、人名等關鍵信息

**特殊要求：**
- slug: 必須是SEO友好的英文URL（只能包含英文字母、數字和連字符，不能有中文）
- summary: 提供50-100字的簡潔摘要供快速閱讀
- content: 使用Markdown格式，包含適當的標題和段落結構
- categories: 從以下預設分類中選擇：tech（澳門科技）, international-tech-innovation（科技與創新）, economy（經濟與產業）, politics（政府與政策）, society（社會民生）, health（健康醫療）, education（教育學習）
- meta_title: SEO標題（50-60字元，包含關鍵詞）
- meta_description: SEO描述（100-150字元，準確描述內容）
- keywords: 提取3-5個最相關的關鍵詞
""")

    # Summary generation prompt
    GENERATE_SUMMARY = PromptTemplate("""
請為以下新聞文章生成一個簡潔的摘要：

**文章標題：** {title}
**文章內容：** {content}

**要求：**
- 摘要長度控制在50-100字
- 突出文章的核心信息
- 使用客觀、中性的語調
- 包含關鍵的時間、地點、人物信息（如有）

**摘要：**
""")

    # Title optimization prompt
    OPTIMIZE_TITLE = PromptTemplate("""
請為以下新聞文章優化標題，使其更吸引讀者但保持準確性：

**原標題：** {original_title}
**文章內容：** {content_preview}

**要求：**
- 標題長度控制在15-25字
- 突出新聞價值和關鍵信息
- 使用吸引人但不誇張的表達
- 保持新聞的客觀性

**優化後標題：**
""")

    # Category classification prompt
    CLASSIFY_ARTICLE = PromptTemplate("""
請為以下新聞文章進行分類：

**標題：** {title}
**內容：** {content}
**原始標籤：** {original_tags}

**可選分類：**
- 政治 (Politics)
- 經濟 (Economy)
- 社會 (Society)
- 科技 (Technology)
- 健康 (Health)
- 教育 (Education)
- 文化 (Culture)
- 體育 (Sports)
- 娛樂 (Entertainment)
- 澳聞 (Macao News)

**請選擇最適合的主分類和次分類（如適用）：**

**主分類：**
**次分類：**
**信心度：** [1-10分]
""")

    # Quality assessment prompt
    ASSESS_QUALITY = PromptTemplate("""
請評估以下改寫文章的質量：

**原始文章：**
{original_content}

**改寫文章：**
{rephrased_content}

**評估標準：**
1. 事實準確性 (1-10分)
2. 語言流暢性 (1-10分)
3. 結構清晰度 (1-10分)
4. 可讀性改善 (1-10分)
5. 整體質量 (1-10分)

**請按以下格式評分：**

**事實準確性：** [分數] - [簡短說明]
**語言流暢性：** [分數] - [簡短說明]
**結構清晰度：** [分數] - [簡短說明]
**可讀性改善：** [分數] - [簡短說明]
**整體質量：** [分數] - [簡短說明]

**總體評價：**
[簡短的總體評價和改進建議]
""")


class PromptBuilder:
    """Builder for creating customized prompts."""
    
    def __init__(self):
        """Initialize prompt builder."""
        self.prompts = NewsRephrasingPrompts()
    
    def build_rephrase_prompt(self, article: ScrapedArticle, 
                            style: str = "standard") -> str:
        """Build a rephrasing prompt for an article."""
        
        # Prepare article data
        tags_str = ", ".join(article.tags) if article.tags else "未分類"
        
        # Truncate content if too long (to stay within token limits)
        content = article.content
        if len(content) > 8000:  # Rough character limit
            content = content[:8000] + "..."
        
        return self.prompts.REPHRASE_ARTICLE.format(
            original_title=article.title,
            source_site_name=article.source_site_name,
            tags=tags_str,
            original_content=content
        )
    
    def build_summary_prompt(self, title: str, content: str) -> str:
        """Build a summary generation prompt."""
        # Truncate content for summary
        if len(content) > 2000:
            content = content[:2000] + "..."
        
        return self.prompts.GENERATE_SUMMARY.format(
            title=title,
            content=content
        )
    
    def build_title_prompt(self, original_title: str, content: str) -> str:
        """Build a title optimization prompt."""
        # Use first 500 characters as preview
        content_preview = content[:500] + "..." if len(content) > 500 else content
        
        return self.prompts.OPTIMIZE_TITLE.format(
            original_title=original_title,
            content_preview=content_preview
        )
    
    def build_classification_prompt(self, title: str, content: str, 
                                  original_tags: list = None) -> str:
        """Build a classification prompt."""
        # Truncate content for classification
        if len(content) > 1500:
            content = content[:1500] + "..."
        
        tags_str = ", ".join(original_tags) if original_tags else "無"
        
        return self.prompts.CLASSIFY_ARTICLE.format(
            title=title,
            content=content,
            original_tags=tags_str
        )
    
    def build_quality_assessment_prompt(self, original_content: str, 
                                      rephrased_content: str) -> str:
        """Build a quality assessment prompt."""
        # Truncate both contents if needed
        if len(original_content) > 2000:
            original_content = original_content[:2000] + "..."
        
        if len(rephrased_content) > 2000:
            rephrased_content = rephrased_content[:2000] + "..."
        
        return self.prompts.ASSESS_QUALITY.format(
            original_content=original_content,
            rephrased_content=rephrased_content
        )


def estimate_tokens(text: str) -> int:
    """Estimate token count for a text string."""
    # Rough estimation: 1 token ≈ 0.75 words for English, 1 token ≈ 1 character for Chinese
    # This is a conservative estimate
    chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
    other_chars = len(text) - chinese_chars
    
    # Conservative token estimation
    estimated_tokens = chinese_chars + (other_chars // 3)
    
    return max(estimated_tokens, len(text) // 4)  # Minimum estimate
