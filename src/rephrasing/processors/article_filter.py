"""
Intelligent article filtering for AI rephrasing.

This module implements smart filtering to select the most valuable articles
for rephrasing, considering the limited daily quota of Gemini API.
"""

import re
from typing import List, Dict, Set
from dataclasses import dataclass

from src.core.models.article import ScrapedArticle


@dataclass
class FilterCriteria:
    """Configuration for article filtering."""
    
    # Content length limits
    min_content_length: int = 200
    max_content_length: int = 10000
    
    # Priority tags (high value for rephrasing)
    high_priority_tags: Set[str] = None
    medium_priority_tags: Set[str] = None
    low_priority_tags: Set[str] = None
    
    # Exclusion patterns
    exclude_patterns: List[str] = None
    
    # Quality indicators
    require_images: bool = False
    require_author: bool = False
    
    def __post_init__(self):
        """Initialize default values."""
        if self.high_priority_tags is None:
            self.high_priority_tags = {
                "A01", "澳聞",      # Macao News
                "A02", "要聞",      # Important News  
                "A03", "政治",      # Politics
                "A04", "經濟",      # Economy
                "A05", "科技",      # Technology
                "A06", "社會",      # Society
                "A07", "健康",      # Health
                "A08", "教育"       # Education
            }
        
        if self.medium_priority_tags is None:
            self.medium_priority_tags = {
                "A09", "文化",      # Culture
                "A10", "體育",      # Sports
                "A11", "娛樂",      # Entertainment
                "A12", "旅遊"       # Tourism
            }
        
        if self.low_priority_tags is None:
            self.low_priority_tags = {
                "A13", "廣告",      # Advertisements
                "A14", "分類",      # Classified
                "A15", "公告"       # Announcements
            }
        
        if self.exclude_patterns is None:
            self.exclude_patterns = [
                r"廣告",           # Advertisement
                r"招聘",           # Job posting
                r"拍賣",           # Auction
                r"尋人",           # Missing person
                r"聲明",           # Statement/Notice
                r"公告",           # Announcement
                r"啟事",           # Notice
                r"徵求",           # Seeking
                r"招標"            # Tender
            ]


class ArticleFilter:
    """Intelligent article filter for rephrasing selection."""
    
    def __init__(self, criteria: FilterCriteria = None):
        """Initialize with filtering criteria."""
        self.criteria = criteria or FilterCriteria()
    
    def calculate_priority_score(self, article: ScrapedArticle) -> float:
        """Calculate priority score for an article (0.0 to 1.0)."""
        score = 0.0
        
        # Tag-based scoring
        article_tags = set(article.tags)
        
        if article_tags & self.criteria.high_priority_tags:
            score += 0.6
        elif article_tags & self.criteria.medium_priority_tags:
            score += 0.4
        elif article_tags & self.criteria.low_priority_tags:
            score += 0.1
        else:
            score += 0.3  # Default for untagged articles
        
        # Content quality indicators
        if len(article.content) > 1000:
            score += 0.1
        
        if article.images:
            score += 0.1
        
        if article.author:
            score += 0.1
        
        # Title quality (longer titles often indicate more substantial content)
        if len(article.title) > 20:
            score += 0.1
        
        return min(score, 1.0)
    
    def should_exclude(self, article: ScrapedArticle) -> bool:
        """Check if article should be excluded from rephrasing."""
        
        # Content length check
        content_length = len(article.content)
        if (content_length < self.criteria.min_content_length or 
            content_length > self.criteria.max_content_length):
            return True
        
        # Check exclusion patterns
        combined_text = f"{article.title} {article.content}"
        for pattern in self.criteria.exclude_patterns:
            if re.search(pattern, combined_text, re.IGNORECASE):
                return True
        
        # Check for low-priority tags if strict filtering
        article_tags = set(article.tags)
        if article_tags & self.criteria.low_priority_tags and not (
            article_tags & (self.criteria.high_priority_tags | self.criteria.medium_priority_tags)
        ):
            return True
        
        # Quality requirements
        if self.criteria.require_images and not article.images:
            return True
        
        if self.criteria.require_author and not article.author:
            return True
        
        return False
    
    def filter_articles(self, articles: List[ScrapedArticle], max_articles: int = None) -> List[ScrapedArticle]:
        """Filter and prioritize articles for rephrasing.

        Note: max_articles parameter is deprecated and ignored. All articles that meet
        the filtering criteria will be processed to avoid missing important news.
        """

        # First pass: exclude unwanted articles
        filtered_articles = []
        for article in articles:
            if not self.should_exclude(article):
                filtered_articles.append(article)

        # Calculate priority scores
        scored_articles = []
        for article in filtered_articles:
            priority_score = self.calculate_priority_score(article)
            scored_articles.append((article, priority_score))

        # Sort by priority score (highest first)
        scored_articles.sort(key=lambda x: x[1], reverse=True)

        # Return ALL filtered articles (no artificial limit)
        # Priority threshold filtering happens at a higher level
        return [article for article, score in scored_articles]
    
    def get_filtering_stats(self, articles: List[ScrapedArticle]) -> Dict[str, int]:
        """Get statistics about filtering results."""
        stats = {
            "total_articles": len(articles),
            "excluded_by_length": 0,
            "excluded_by_pattern": 0,
            "excluded_by_tags": 0,
            "excluded_by_quality": 0,
            "high_priority": 0,
            "medium_priority": 0,
            "low_priority": 0,
            "untagged": 0
        }
        
        for article in articles:
            # Length exclusions
            content_length = len(article.content)
            if (content_length < self.criteria.min_content_length or 
                content_length > self.criteria.max_content_length):
                stats["excluded_by_length"] += 1
                continue
            
            # Pattern exclusions
            combined_text = f"{article.title} {article.content}"
            excluded_by_pattern = False
            for pattern in self.criteria.exclude_patterns:
                if re.search(pattern, combined_text, re.IGNORECASE):
                    stats["excluded_by_pattern"] += 1
                    excluded_by_pattern = True
                    break
            
            if excluded_by_pattern:
                continue
            
            # Tag-based categorization
            article_tags = set(article.tags)
            if article_tags & self.criteria.high_priority_tags:
                stats["high_priority"] += 1
            elif article_tags & self.criteria.medium_priority_tags:
                stats["medium_priority"] += 1
            elif article_tags & self.criteria.low_priority_tags:
                stats["low_priority"] += 1
            else:
                stats["untagged"] += 1
        
        return stats


def create_daily_filter(daily_quota: int = 250) -> ArticleFilter:
    """Create a filter optimized for daily quota management."""
    
    # Adjust criteria based on available quota
    if daily_quota <= 50:
        # Very strict filtering for low quota
        criteria = FilterCriteria(
            min_content_length=500,
            max_content_length=8000,
            require_images=True
        )
        # Only keep highest priority tags
        criteria.high_priority_tags = {"A01", "澳聞", "A02", "要聞", "A03", "政治"}
        criteria.medium_priority_tags = set()
        
    elif daily_quota <= 150:
        # Moderate filtering
        criteria = FilterCriteria(
            min_content_length=300,
            max_content_length=10000
        )
        
    else:
        # Standard filtering for higher quota
        criteria = FilterCriteria()
    
    return ArticleFilter(criteria)
