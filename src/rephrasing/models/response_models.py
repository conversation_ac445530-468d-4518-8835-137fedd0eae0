"""
Pydantic models for structured AI responses.
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class RephrasedContent(BaseModel):
    """Structured response for rephrased article content aligned with PostData API."""

    title: str = Field(description="Rephrased article title, more engaging but accurate")
    slug: str = Field(description="SEO-friendly URL slug (English/numbers only, no Chinese)")
    summary: str = Field(description="2-3 sentence summary for fast reading (50-100 characters)")
    content: str = Field(description="Full rephrased article content in Markdown format with improved readability")
    keywords: List[str] = Field(description="3-5 relevant keywords extracted from the content")
    categories: List[str] = Field(description="1-3 category titles from predefined list: 澳門新聞, 政府政策, 經濟財經, 社會民生, 國際新聞, 體育新聞, 文化生活, 科技新聞, 大灣區新聞, 健康醫療, 教育學習")
    meta_title: str = Field(description="SEO meta title (50-60 characters, includes relevant keywords)")
    meta_description: str = Field(description="SEO meta description (100-150 characters, accurately describes content)")

    class Config:
        json_schema_extra = {
            "example": {
                "title": "澳門經濟發展迎來新機遇",
                "slug": "macau-economic-development-opportunities-2025",
                "summary": "澳門特別行政區政府宣布新的經濟發展措施，促進中小企業發展並推動科技創新。",
                "content": "# 澳門經濟發展迎來新機遇\n\n澳門特別行政區政府今日正式宣布一系列全新的經濟發展措施...",
                "keywords": ["澳門", "經濟發展", "政府政策", "中小企業", "科技創新"],
                "categories": ["經濟財經", "政府政策"],
                "meta_title": "澳門經濟發展新政策 | 中小企業創新機遇 2025",
                "meta_description": "澳門政府推出全新經濟發展措施，重點支持中小企業發展和科技創新，為澳門經濟注入新活力，促進多元化發展。"
            }
        }


class ArticleSummary(BaseModel):
    """Structured response for article summarization."""
    
    summary: str = Field(description="Concise summary of the article in 50-100 characters")
    key_points: List[str] = Field(description="3-5 key points from the article")
    
    class Config:
        json_schema_extra = {
            "example": {
                "summary": "澳門政府推出新經濟政策，支持中小企業發展和科技創新。",
                "key_points": [
                    "政府宣布新經濟發展措施",
                    "重點支持中小企業發展", 
                    "推動科技創新項目",
                    "預計為經濟注入新活力"
                ]
            }
        }


class ArticleClassification(BaseModel):
    """Structured response for article classification."""
    
    primary_category: str = Field(description="Main category of the article")
    secondary_categories: List[str] = Field(description="Additional relevant categories")
    confidence_score: float = Field(description="Confidence score from 0.0 to 1.0", ge=0.0, le=1.0)
    reasoning: str = Field(description="Brief explanation for the classification")
    
    class Config:
        json_schema_extra = {
            "example": {
                "primary_category": "經濟財經",
                "secondary_categories": ["政府政策", "澳門新聞"],
                "confidence_score": 0.9,
                "reasoning": "文章主要討論政府經濟政策，涉及澳門本地新聞"
            }
        }


class QualityAssessment(BaseModel):
    """Structured response for content quality assessment."""
    
    factual_accuracy: int = Field(description="Factual accuracy score (1-10)", ge=1, le=10)
    language_fluency: int = Field(description="Language fluency score (1-10)", ge=1, le=10)
    structural_clarity: int = Field(description="Structural clarity score (1-10)", ge=1, le=10)
    readability_improvement: int = Field(description="Readability improvement score (1-10)", ge=1, le=10)
    overall_quality: int = Field(description="Overall quality score (1-10)", ge=1, le=10)
    
    feedback: str = Field(description="Brief feedback and suggestions for improvement")
    
    class Config:
        json_schema_extra = {
            "example": {
                "factual_accuracy": 9,
                "language_fluency": 8,
                "structural_clarity": 8,
                "readability_improvement": 7,
                "overall_quality": 8,
                "feedback": "改寫質量良好，保持了事實準確性，語言更加流暢。建議進一步優化段落結構。"
            }
        }


class TitleOptimization(BaseModel):
    """Structured response for title optimization."""
    
    optimized_title: str = Field(description="Optimized title that is more engaging but accurate")
    alternative_titles: List[str] = Field(description="2-3 alternative title options")
    improvement_notes: str = Field(description="Brief notes on what was improved")
    
    class Config:
        json_schema_extra = {
            "example": {
                "optimized_title": "澳門推出重磅經濟新政，中小企業迎發展良機",
                "alternative_titles": [
                    "澳門經濟新政策正式啟動，助力企業創新發展",
                    "政府力推經濟改革，澳門中小企業受惠"
                ],
                "improvement_notes": "增加了吸引力詞彙，突出了政策影響和受益群體"
            }
        }


class BatchRephrasedContent(BaseModel):
    """Structured response for batch article rephrasing."""

    articles: List[RephrasedContent] = Field(description="List of rephrased articles")

    class Config:
        json_schema_extra = {
            "example": {
                "articles": [
                    {
                        "title": "澳門經濟發展迎來新機遇",
                        "slug": "macau-economic-development-opportunities-2025",
                        "summary": "澳門特別行政區政府宣布新的經濟發展措施，促進中小企業發展並推動科技創新。",
                        "content": "# 澳門經濟發展迎來新機遇\n\n澳門特別行政區政府今日正式宣布一系列全新的經濟發展措施...",
                        "keywords": ["澳門", "經濟發展", "政府政策", "中小企業", "科技創新"],
                        "categories": ["經濟財經", "政府政策"],
                        "meta_title": "澳門經濟發展新政策 | 中小企業創新機遇 2025",
                        "meta_description": "澳門政府推出全新經濟發展措施，重點支持中小企業發展和科技創新，為澳門經濟注入新活力，促進多元化發展。"
                    }
                ]
            }
        }
