# 📰 Macao Daily Bulk Scraping Guide

This guide covers the comprehensive bulk scraping tools for collecting Macao Daily news data across date ranges with intelligent features like random delays, duplicate detection, and organized file storage.

## 🚀 Quick Start

### Single Date Scraping
```bash
# Scrape today's news
python scrape.py today

# Scrape yesterday's news
python scrape.py yesterday

# Scrape a specific date
python scrape.py date 2025-05-28
```

### Date Range Scraping
```bash
# Scrape a month
python scrape.py range 2025-05-01 2025-05-31

# Scrape entire year 2024
python scrape.py year 2024

# Scrape last week
python scrape.py week
```

## 📁 File Organization

### Filename Format
Files are now saved with clean date-based naming:
- **Format**: `macaodaily_YYYYMMDD.json`
- **Examples**: 
  - `macaodaily_20250528.json` (May 28, 2025)
  - `macaodaily_20241225.json` (December 25, 2024)

### File Structure
Each JSON file contains:
```json
{
  "scraper_name": "macaodaily",
  "target_date": "2025-05-28",
  "scrape_timestamp": "2025-06-29T02:05:44.724298",
  "article_count": 205,
  "articles": [
    {
      "source_site_name": "澳門日報",
      "original_url": "http://www.macaodaily.com/html/2025-05/28/content_1835130.htm",
      "original_title": "首季電騙立案損失雙降",
      "tags": ["A01", "澳聞"],
      "images": [
        {
          "src": "http://www.macaodaily.com/res/1/20250528/95901748368328798.jpg",
          "description": "亞馬喇前地發生巴士撞途人交通事故"
        }
      ],
      "content_html": "<div class=\"article-content\">...</div>",
      "content_markdown": "...",
      "content_text": "..."
    }
  ]
}
```

## 🛠️ Advanced Usage

### Convenience Script (`scrape.py`) - Recommended

#### Basic Commands
```bash
# Single date
python scrape.py date 2025-05-28

# Date range
python scrape.py range 2025-01-01 2025-12-31

# Entire year
python scrape.py year 2024

# Quick utilities
python scrape.py today
python scrape.py yesterday
python scrape.py week
python scrape.py stats
```

#### Advanced Options
```bash
# Custom delays (2-10 seconds between requests)
python scrape.py year 2025 --min-delay 2 --max-delay 10

# Dry run to preview what would be scraped
python scrape.py year 2025 --dry-run

# Verbose logging
python scrape.py range 2025-05-01 2025-05-31 --verbose
```

### Direct Script Access (Advanced Users)

#### Bulk Scraping Script (`scripts/bulk_scrape.py`)
```bash
# Direct access to full options
python scripts/bulk_scrape.py --date 2025-05-28
python scripts/bulk_scrape.py --year 2024 --min-delay 3 --max-delay 8
```

#### Utility Script (`scripts/scrape_utils.py`)
```bash
# Direct utility access
python scripts/scrape_utils.py today
python scripts/scrape_utils.py stats
```

## 🎯 Smart Features

### 1. **Random Delays**
- Prevents overwhelming the server
- Configurable delay ranges (default: 1-5 seconds)
- Mimics human browsing patterns

### 2. **Duplicate Detection**
- Automatically skips dates that already have data files
- Prevents unnecessary re-scraping
- Can be overridden with `--no-skip-existing`

### 3. **Comprehensive Logging**
- Progress tracking with percentages
- Detailed error reporting
- Separate log file (`bulk_scrape.log`)

### 4. **Robust Error Handling**
- Continues scraping even if individual dates fail
- Detailed failure reporting
- Graceful handling of network issues

## 📊 Statistics and Monitoring

### View Scraping Statistics
```bash
python scrape_utils.py stats
```

**Sample Output:**
```
📊 Macao Daily Scraping Statistics
==================================================
📁 Total files: 5
📰 Total articles: 1,005
📅 Date range: 2025-05-26 to 2025-05-29
📈 Average articles per file: 201.0

📅 Files by month:
  2025-05: 4 files

🏷️  Top article tags:
  澳聞: 370 articles
  要聞: 179 articles
  藝海: 128 articles
  經濟: 74 articles
  體育: 58 articles

📋 Recent files:
  macaodaily_20250526.json: 180 articles (1,080,380 bytes)
  macaodaily_20250527.json: 224 articles (1,233,960 bytes)
```

## 🏷️ Article Tags System

Articles are automatically tagged based on newspaper sections:

### Primary Categories
- **澳聞** (Local News): A01-A13 sections
- **要聞** (Important News): B06 section
- **經濟** (Economics): A14 section
- **體育** (Sports): A11-A12 sections
- **藝海** (Arts & Entertainment): C01-C04 sections

### Tag Format
- **Page Numbers**: A01, A02, B06, C01, etc.
- **Categories**: 澳聞, 要聞, 體育, 藝海, etc.
- **Subcategories**: 特刋, 新園地, etc.

**Example**: "A01：澳聞" → `["A01", "澳聞"]`

## 🖼️ Image Support

Articles include comprehensive image data:
- **Absolute URLs**: Full paths to image resources
- **Descriptions**: Chinese captions for each image
- **Smart Filtering**: Excludes template/layout images

## ⚡ Performance Tips

### For Large Date Ranges
```bash
# Use longer delays for stability
python bulk_scrape.py --year 2024 --min-delay 3 --max-delay 8

# Run in background with logging
nohup python bulk_scrape.py --year 2024 > scrape.log 2>&1 &
```

### For Daily Automation
```bash
# Add to crontab for daily scraping
0 9 * * * cd /path/to/project && python scrape_utils.py today
```

## 🔧 Configuration

### Date Range Limits
- **Earliest**: Limited by Macao Daily's archive availability
- **Latest**: Current date
- **Recommended**: Start with recent months, then expand

### Delay Recommendations
- **Conservative**: 3-8 seconds (for large ranges)
- **Normal**: 1-5 seconds (default)
- **Fast**: 0.5-2 seconds (for small ranges)

## 📈 Example Workflows

### Scrape Entire 2024
```bash
# Preview first
python bulk_scrape.py --year 2024 --dry-run

# Execute with conservative delays
python bulk_scrape.py --year 2024 --min-delay 3 --max-delay 8
```

### Daily News Collection
```bash
# Morning routine
python scrape_utils.py today
python scrape_utils.py stats
```

### Catch Up on Missing Days
```bash
# Check what's missing, then fill gaps
python scrape_utils.py stats
python bulk_scrape.py --start-date 2025-05-01 --end-date 2025-05-20
```

## 🎉 Success Metrics

With this system, you can expect:
- **~200 articles per day** on average
- **Complete metadata** including tags and images
- **Clean, structured content** ready for analysis
- **Reliable, resumable** bulk operations
- **Comprehensive statistics** for monitoring

The bulk scraping system provides a professional-grade solution for collecting comprehensive Macao Daily news data with all the features needed for serious news analysis and archival projects! 🚀
