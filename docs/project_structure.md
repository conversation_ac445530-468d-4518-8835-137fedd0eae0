# 📁 Project Structure

This document outlines the organized structure of the Macao Daily news scraping project.

## 🏗️ Directory Layout

```
scrape-rephrase-news/
├── 📄 scrape.py                    # 🎯 Main convenience script (START HERE)
├── 📄 main.py                      # Core scraping engine
├── 📄 config.py                    # Configuration settings
├── 📄 requirements.txt             # Python dependencies
├── 📄 PROJECT_STRUCTURE.md         # This file
├── 📄 README.md                    # Project overview
│
├── 📁 scripts/                     # 🛠️ User-facing tools and utilities
│   ├── 📄 __init__.py             # Package initialization
│   ├── 📄 bulk_scrape.py          # Advanced bulk scraping script
│   └── 📄 scrape_utils.py         # Utility functions and quick commands
│
├── 📁 scrapers/                    # 🕷️ Core scraper implementations
│   ├── 📄 __init__.py             # Package initialization
│   ├── 📄 base.py                 # Base scraper classes
│   ├── 📄 macaodaily_scraper.py   # Macao Daily scraper
│   └── 📄 shimindaily_scraper.py  # Shimin Daily scraper (disabled)
│
├── 📁 models/                      # 📊 Data models and structures
│   ├── 📄 __init__.py             # Package initialization
│   └── 📄 article.py              # Article data model
│
├── 📁 utils/                       # 🔧 Internal utilities
│   ├── 📄 __init__.py             # Package initialization
│   └── 📄 storage.py              # Data storage utilities
│
├── 📁 docs/                        # 📚 Documentation
│   ├── 📄 BULK_SCRAPING_GUIDE.md  # Comprehensive bulk scraping guide
│   ├── 📄 requirements.md         # Requirements documentation
│   └── 📄 todo.md                 # Project todo list
│
├── 📁 data/                        # 💾 Scraped data files
│   ├── 📄 macaodaily_20250526.json # Date-specific data files
│   ├── 📄 macaodaily_20250527.json
│   └── 📄 ...                     # More data files
│
└── 📁 venv/                        # 🐍 Python virtual environment
    └── ...                        # Virtual environment files
```

## 🎯 Entry Points

### 1. **Main Convenience Script** (Recommended)
```bash
python scrape.py [command] [options]
```
- **Purpose**: Easy access to all functionality
- **Location**: Root directory
- **Usage**: `python scrape.py help` for full options

### 2. **Core Scraping Engine**
```bash
python main.py
```
- **Purpose**: Run configured scrapers
- **Location**: Root directory
- **Usage**: Basic scraping with current config

### 3. **Direct Script Access** (Advanced)
```bash
python scripts/bulk_scrape.py [options]
python scripts/scrape_utils.py [command]
```
- **Purpose**: Direct access to advanced features
- **Location**: `scripts/` directory
- **Usage**: Full control over scraping parameters

## 📦 Package Organization

### **scripts/** - User Tools
- **Purpose**: User-facing scripts and utilities
- **Target Users**: End users, automation scripts
- **Key Features**: 
  - Simple command-line interfaces
  - Bulk operations
  - Statistics and monitoring

### **scrapers/** - Core Logic
- **Purpose**: Scraper implementations
- **Target Users**: Developers, maintainers
- **Key Features**:
  - Base classes and interfaces
  - Site-specific scraping logic
  - Content extraction algorithms

### **models/** - Data Structures
- **Purpose**: Data models and schemas
- **Target Users**: Developers, data analysts
- **Key Features**:
  - Article data structure
  - JSON serialization
  - Content format conversion

### **utils/** - Internal Tools
- **Purpose**: Internal utilities and helpers
- **Target Users**: Internal code, scrapers
- **Key Features**:
  - Storage management
  - File operations
  - Configuration handling

## 🚀 Usage Patterns

### **Quick Daily Use**
```bash
# Check today's news
python scrape.py today

# Check statistics
python scrape.py stats
```

### **Bulk Historical Data**
```bash
# Scrape entire year
python scrape.py year 2024

# Scrape specific range
python scrape.py range 2025-01-01 2025-12-31
```

### **Development and Testing**
```bash
# Dry run to preview
python scrape.py date 2025-05-28 --dry-run

# Direct script access for debugging
python scripts/bulk_scrape.py --date 2025-05-28 --verbose
```

## 🔄 Import Structure

### **From Root Directory**
```python
# Main modules
from scrapers.macaodaily_scraper import MacaoDailyScraper
from models.article import Article
from utils.storage import DataStorage

# Convenience functions
from scripts import scrape_today, scrape_yesterday, get_scraping_stats
```

### **From Scripts Directory**
```python
# Scripts add parent to path automatically
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

# Then import normally
from scrapers.macaodaily_scraper import MacaoDailyScraper
```

## 📊 Data Flow

```
1. User Command
   ↓
2. scrape.py (convenience layer)
   ↓
3. scripts/bulk_scrape.py or scripts/scrape_utils.py
   ↓
4. scrapers/macaodaily_scraper.py
   ↓
5. models/article.py (data structure)
   ↓
6. utils/storage.py (file management)
   ↓
7. data/raw/macaodaily/YYYY/MM/macaodaily_YYYYMMDD.json (output)
```

## 🎨 Design Principles

### **1. Separation of Concerns**
- **Scripts**: User interface and workflow
- **Scrapers**: Core scraping logic
- **Models**: Data representation
- **Utils**: Supporting functionality

### **2. Progressive Disclosure**
- **Simple**: `python scrape.py today`
- **Intermediate**: `python scrape.py year 2024 --min-delay 3`
- **Advanced**: `python scripts/bulk_scrape.py --help`

### **3. Maintainability**
- Clear module boundaries
- Consistent naming conventions
- Comprehensive documentation
- Modular, testable code

## 🔧 Configuration

### **Main Config** (`config.py`)
- Scraper settings
- Site configurations
- Output directories

### **Script-specific Options**
- Command-line arguments
- Environment variables
- Runtime parameters

This structure provides a clean, scalable foundation for the news scraping project while maintaining ease of use for both casual users and developers! 🎉
