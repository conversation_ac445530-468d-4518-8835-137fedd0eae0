# TODO List

## Future Improvements (After MVP)

### Code Quality & Development Tools
- [ ] **Integrate Ruff**: Add ruff for fast Python linting and code formatting
  - Replace flake8, black, isort with single ruff tool
  - Configure ruff.toml for project-specific rules
  - Add pre-commit hooks for automatic formatting
  
- [ ] **Migrate to UV**: Replace pip with uv for faster dependency management
  - UV is 10-100x faster than pip for package installation
  - Better dependency resolution and lock file management
  - Simpler virtual environment management
  - Replace requirements.txt with pyproject.toml

### Architecture Improvements
- [ ] Add async/await support for concurrent scraping
- [ ] Implement proper logging with different levels
- [ ] Add configuration validation with Pydantic
- [ ] Create proper exception hierarchy
- [ ] Add retry mechanisms with exponential backoff
- [ ] Implement rate limiting per website

### Advanced Features
- [ ] Database integration (SQLite -> PostgreSQL)
- [ ] Web dashboard for monitoring
- [ ] AI content rephrasing integration
- [ ] Advanced anti-bot detection handling
- [ ] IP proxy pool support
- [ ] Scheduled execution with cron-like functionality

### Testing & CI/CD
- [ ] Unit tests for all scrapers
- [ ] Integration tests
- [ ] GitHub Actions for CI/CD
- [ ] Code coverage reporting

## Current MVP Focus
- [x] Simple, maintainable Python project structure
- [x] Basic static website scraping (Macao Daily) - ✅ **WORKING**
- [ ] Basic dynamic website scraping (市民日報) - ⚠️ **BLOCKED BY CLOUDFLARE**
- [x] Unified data schema implementation
- [x] JSON file storage
- [x] Simple configuration system

## Immediate Next Steps
- [ ] **Advanced Cloudflare Bypass**: Implement sophisticated bypass for 市民日報
  - Research latest Cloudflare bypass techniques
  - Consider using undetected-chromedriver or stealth plugins
  - Implement rotating user agents and browser fingerprinting
  - Add CAPTCHA solving capabilities if needed
