# Continuous Rephrase Processing Guide

This guide covers the enhanced rephrase system designed for 24/7 operation to process all your news data efficiently while respecting Gemini API rate limits.

## 🚀 New Features

### 1. **Continuous Processing Command**
- Process entire date ranges automatically
- 24/7 operation with automatic rate limit handling
- Enhanced logging for monitoring
- Pre-rephrase filtering stage for better visibility

### 2. **Enhanced Batch Processing**
- Increased default batch size to 15 articles per request
- Intelligent rate limiting with quota management
- Automatic retry and backoff mechanisms

### 3. **Pre-Rephrase Filtering**
- Creates filtered article files before AI processing
- Stored in `data/processed/pre-rephrase/` for better visibility
- Includes priority scores and filtering criteria

### 4. **Comprehensive Logging**
- Separate log files for different levels (info, debug, error)
- Daily log rotation
- Real-time progress monitoring

## 📁 New Directory Structure

```
data/
├── raw/                    # Original scraped data
│   └── macaodaily/
│       └── 2024/
│           └── 20240701.json
├── processed/
│   ├── pre-rephrase/      # NEW: Filtered articles before AI processing
│   │   └── macaodaily/
│   │       └── 2024/
│   │           └── 20240701.json
│   └── rephrased/         # Final AI-rephrased articles
│       └── macaodaily/
│           └── 2024/
│               └── 20240701.json
└── exports/               # Export-ready data

logs/                      # NEW: Enhanced logging
├── rephrase_20250705.log
├── rephrase_info.log
├── rephrase_debug.log
├── rephrase_error.log
└── continuous_rephrase_20250705.log
```

## 🎯 Usage Examples

### Basic Continuous Processing

```bash
# Process entire year 2024
python -m cli rephrase continuous --year 2024

# Process specific date range
python -m cli rephrase continuous --start-date 2024-01-01 --end-date 2024-12-31

# Process from specific date to today
python -m cli rephrase continuous --start-date 2024-06-01
```

### Advanced Options

```bash
# Custom batch size and priority threshold
python -m cli rephrase continuous --year 2024 \
  --batch-size 10 \
  --priority-threshold 0.7 \
  --max-articles 30

# Enable debug logging
python -m cli rephrase continuous --year 2024 \
  --log-level DEBUG

# Dry run to see what would be processed
python -m cli rephrase continuous --year 2024 --dry-run
```

### 24/7 Processing Script

For long-running operations, use the monitoring script:

```bash
# Process entire year with monitoring and auto-restart
python scripts/continuous_rephrase.py --year 2024

# Process date range with custom settings
python scripts/continuous_rephrase.py \
  --start-date 2024-01-01 \
  --end-date 2024-12-31 \
  --batch-size 15 \
  --priority-threshold 0.6
```

## 📊 Rate Limiting & Quota Management

### Gemini Free Tier Limits
- **Daily**: 250 requests per day
- **Per Minute**: 10 requests per minute
- **Tokens**: 250,000 tokens per minute

### Automatic Handling
- **Smart Batching**: 15 articles per request (configurable)
- **Rate Limiting**: Automatic delays between requests
- **Quota Monitoring**: Real-time quota status checking
- **Graceful Degradation**: Pauses when quota exceeded

### Estimated Processing Time

With 15 articles per batch and rate limits:
- **Per Day**: ~250 requests = ~3,750 articles
- **Per Month**: ~112,500 articles
- **Full Year (365 days)**: ~1.37M articles

For your dataset (~188 articles/day × 365 days = ~68,620 articles/year):
- **Estimated Time**: ~18-20 days for full year processing

## 🔍 Pre-Rephrase Filtering

### What Gets Filtered

**High Priority Tags (Score: 0.6)**
- A01-A08: 澳聞, 要聞, 政治, 經濟, 科技, 社會, 健康, 教育

**Medium Priority Tags (Score: 0.4)**
- A09-A12: 文化, 體育, 娛樂, 旅遊

**Additional Scoring**
- Content length > 1000 chars: +0.1
- Has images: +0.1
- Has author: +0.1
- Title length > 20 chars: +0.1

### Pre-Rephrase File Structure

```json
{
  "source": "macaodaily",
  "target_date": "2024-07-01",
  "filtered_at": "2025-07-05T10:00:00",
  "original_article_count": 25,
  "filter_criteria": {
    "min_content_length": 200,
    "max_content_length": 10000,
    "priority_threshold": 0.5
  },
  "articles": [
    {
      "id": "uuid",
      "title": "Article title",
      "content": "Article content...",
      "tags": ["A01", "澳聞"],
      "priority_score": 0.8,
      ...
    }
  ]
}
```

## 📈 Monitoring & Logging

### Log Files

1. **`rephrase_YYYYMMDD.log`** - Daily main log
2. **`rephrase_info.log`** - Info level with rotation
3. **`rephrase_debug.log`** - Debug level with rotation
4. **`rephrase_error.log`** - Error level with rotation
5. **`continuous_rephrase_YYYYMMDD.log`** - Monitoring script log

### Progress Monitoring

```bash
# Monitor real-time progress
tail -f logs/rephrase_$(date +%Y%m%d).log

# Check error logs
tail -f logs/rephrase_error.log

# Monitor quota status
python -m cli rephrase quota
```

### Key Log Messages

- `🚀 Starting continuous rephrase processing` - Process started
- `📊 Progress: X/Y (Z%)` - Processing progress
- `✅ Successfully processed YYYY-MM-DD: N articles` - Date completed
- `⚠️ Daily quota exhausted` - Quota limit reached
- `🏁 Processing completed!` - All processing finished

## 🛠️ Configuration

### Environment Variables

```bash
# Required
export GEMINI_API_KEY="your_api_key_here"

# Optional
export LOG_LEVEL="INFO"
export DATA_ROOT="/path/to/data"
export LOG_ROOT="/path/to/logs"
```

### Settings Customization

Edit `config/settings.py` for default values:

```python
@dataclass
class RephrasingSettings:
    batch_size: int = 15  # Articles per request
    max_requests_per_day: int = 250
    priority_threshold: float = 0.5
    min_content_length: int = 200
    max_content_length: int = 10000
```

## 🚨 Error Handling & Recovery

### Automatic Recovery
- **Rate Limit Errors**: Automatic backoff and retry
- **Quota Exceeded**: Pause until next day
- **Network Errors**: Exponential backoff
- **Process Crashes**: Auto-restart (monitoring script)

### Manual Recovery

```bash
# Check current status
python -m cli rephrase quota

# Resume from specific date
python -m cli rephrase continuous --start-date 2024-06-15

# Skip existing files (default behavior)
python -m cli rephrase continuous --year 2024 --skip-existing
```

## 📋 Best Practices

### For 24/7 Operation

1. **Use the monitoring script** for automatic restarts
2. **Monitor logs regularly** for quota and error status
3. **Set appropriate priority thresholds** to focus on important content
4. **Use screen/tmux** for persistent sessions

```bash
# Start in screen session
screen -S rephrase
python scripts/continuous_rephrase.py --year 2024

# Detach: Ctrl+A, D
# Reattach: screen -r rephrase
```

### Resource Management

1. **Disk Space**: Monitor log file growth
2. **Memory**: Process handles large JSON files efficiently
3. **Network**: Automatic retry for connection issues

### Quality Control

1. **Review pre-rephrase files** to understand filtering
2. **Check priority scores** for article selection
3. **Monitor success/failure rates** in logs
4. **Spot-check rephrased content** quality

## 🎯 Getting Started

1. **Set up API key**:
   ```bash
   export GEMINI_API_KEY="your_key_here"
   ```

2. **Test connection**:
   ```bash
   python -m cli rephrase test
   ```

3. **Check quota**:
   ```bash
   python -m cli rephrase quota
   ```

4. **Start small**:
   ```bash
   python -m cli rephrase continuous --start-date 2024-07-01 --end-date 2024-07-07 --dry-run
   ```

5. **Begin processing**:
   ```bash
   python scripts/continuous_rephrase.py --year 2024
   ```

This enhanced system will efficiently process your entire news dataset while respecting API limits and providing comprehensive monitoring capabilities.
