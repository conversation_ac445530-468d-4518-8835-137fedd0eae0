# 新聞內容聚合平台：專案描述與需求文件

**版本：1.0**
**日期：2025年6月28日**

### 1. 專案總覽 (Project Overview)

**本專案旨在建立一個自動化、可擴展的新聞內容聚合系統。系統核心功能為定期從多個指定的新聞網站上抓取最新文章，將抓取到的內容轉換為統一的標準化格式，並儲存於本地，為後續的內容處理（如 AI 轉述、發布至自有網站）提供乾淨、可靠的資料來源。**

**專案初期將著重於建立一個穩固且易於擴展的基礎架構，而非複雜的功能。**

### 2. 核心目標 (Core Objectives)

* **模組化爬蟲** **：建立一個能夠輕鬆整合不同網站爬蟲的框架。**
* **支援多樣性網站** **：系統必須能同時處理傳統的靜態 HTML 網站與現代的動態 JavaScript 渲染網站。**
* **數據標準化** **：所有從不同來源抓取的文章，都必須被格式化為一個統一的內部資料結構 (Schema)。**
* **可擴展性與可維護性** **：專案架構必須清晰，當需要新增爬取目標或修改現有爬蟲時，應能以最小的成本完成，且不同爬蟲模組之間互不影響。**

### 3. 系統架構與設計理念

**本系統應遵循「分離關注點」原則，將爬蟲邏輯、資料處理、與任務調度分離。**

**基本流程:**

`[目標網站 (A/B/C)] -> [獨立的爬蟲模組] -> [統一資料格式化] -> [資料儲存]`

* **獨立的爬蟲模組 (Independent Scraper Modules)** **：為每一個目標網站建立一個獨立的爬蟲類別 (Class) 或檔案。這確保了修改一個爬蟲不會影響到另一個。**
* **爬蟲基底類別 (Base Scraper Classes)** **：為了簡化開發與維持一致性，應建立兩個爬蟲「基底類別」：**

1. `StaticScraper`：內建使用 `requests` + `BeautifulSoup` 的邏輯，用於爬取靜態網站。
2. `DynamicScraper`：內建使用 `Playwright` 的邏輯，用於爬取需執行 JavaScript 的動態網站。

* **統一資料出口 (Unified Data Output)** **：所有爬蟲，不論其內部實現方式，都必須返回一個符合第 5 節所定義的「統一資料綱要」的物件。**

### 4. 功能需求 (Functional Requirements)

#### 4.1. 爬蟲模組 (Scraper Module)

* **需實作** `StaticScraper` 基底類別，用於處理靜態網站。
  * **開發人員在新增靜態網站爬蟲時，只需繼承此類別，並實作 **`parse()` 方法來定義如何從 HTML 中提取資料。
* **需實作** `DynamicScraper` 基底類別，用於處理動態網站。
  * **開發人員在新增動態網站爬蟲時，只需繼承此類別，並實作 **`parse()` 方法。
* **爬蟲應能處理基本的網路錯誤（如超時、連線失敗），並記錄錯誤日誌。**
* **爬蟲需能避免重複抓取。可透過比對已儲存資料的** `original_url` 來實現。

#### 4.2. 任務調度 (Task Scheduling)

* **需提供一個主執行腳本 (e.g.,**`main.py` 或 `run_scrapers.py`)。
* **此腳本能讀取一個設定檔（如** `config.py` 或 `sites.json`），該設定檔中定義了所有要執行的爬蟲列表。
* **腳本會依序或平行（初期依序即可）執行設定檔中所有的爬蟲任務。**

#### 4.3. 數據儲存 (Data Storage)

* **在專案初期 (MVP)，抓取到的標準化資料應儲存為本地的** **JSON 檔案** **。每個爬蟲執行一次，可以產生一個以日期時間命名的 JSON 檔，或將結果附加到一個主 JSON 檔案中。**
* **系統架構應設計成未來可以輕易地將儲存目標從 JSON 檔案切換為資料庫（如 SQLite, PostgreSQL）。**

### 5. 統一資料綱要 (Unified Data Schema)

**這是系統的核心。所有爬蟲模組完成抓取後，必須回傳一個包含以下欄位的資料物件。**

| **欄位名稱 (**`Field Name`) | **資料型態 (**`Type`) | **說明**                                                  | **範例**                          |
| ----------------------------------- | ----------------------------- | --------------------------------------------------------------- | --------------------------------------- |
| **`source_site_name`**      | `String`                    | **來源網站的中文名稱。**                                  | `"澳門日報"`                          |
| **`source_site_url`**       | `String`                    | **來源網站的首頁 URL。**                                  | `"http://www.macaodaily.com/"`        |
| **`original_url`**          | `String`                    | **[主鍵]**文章的原始 URL，必須是獨一無二的。                    | `"http://www.macaodaily.com/..."`     |
| **`original_title`**        | `String`                    | **文章的原始標題。**                                      | `"澳門舉行盛大慶典"`                  |
| **`author`**                | `String`或 `None`         | **作者名稱。若無則為** `None`。                         | `"記者陳大文"`                        |
| **`publish_date`**          | `String`(ISO 8601)          | **文章發布日期與時間，統一為 ISO 格式。**                 | `"2025-05-10T10:00:00+08:00"`         |
| **`scrape_timestamp`**      | `String`(ISO 8601)          | **系統執行爬取當下的時間戳。**                            | `"2025-06-28T23:00:00+08:00"`         |
| **`content_html`**          | `String`                    | **文章主要內容區塊的原始 HTML。用於存檔和未來重新解析。** | `"<div><p>這是第一段。</p>...</div>"` |
| **`content_markdown`**      | `String`                    | **由** `content_html`轉換而來的 Markdown 格式內容。     | `這是第一段。\n\n...`                 |
| **`content_text`**          | `String`                    | **由** `content_markdown`去除所有格式後的純文字內容。   | `"這是第一段。 ..."`                  |

**關於內容欄位的處理方式：**
爬蟲應首先定位到文章主體內容的 HTML 元素（例如 class 為 `article-body` 的 `<div>`），將其內部完整的 HTML 儲存至 `content_html`。接著，程式需使用函式庫（如 `markdownify` 或 `html2text`）將 `content_html` 轉換為 `content_markdown`，再進一步去除格式得到 `content_text`。

### 6. 非功能性需求 (Non-Functional Requirements)

* **環境簡易性** **：專案應能輕易地在開發人員的本機上設定並執行，僅需安裝 Python 及相關套件。不需依賴 Docker, Redis 等外部服務（初期）。**
* **程式碼品質** **：程式碼需有適當的註解，並遵循 PEP 8 編碼風格。**
* **文件**：需提供一份 **`README.md` 文件，說明如何安裝依賴、設定爬蟲、以及如何執行專案。

### 7. 第一階段 (MVP) 交付範圍

1. **完成** `StaticScraper` 和 `DynamicScraper` 兩個基底類別的實作。
2. **實作兩個範例爬蟲：**
   * `macaodaily_spider.py` (繼承 `StaticScraper`)
   * `shimindaily_spider.py` (繼承 `DynamicScraper`)
3. **實作第 5 節定義的統一資料綱要，包含從 HTML 轉換為 Markdown 和純文字的邏輯。**
4. **實作主執行腳本，能讀取設定檔並執行上述兩個範例爬蟲。**
5. **將爬取結果以 JSON 格式儲存於本地。**
6. **提供** `README.md` 和 `requirements.txt` (套件依賴清單)。

### 8. 未來擴展方向 (Future Expansion)

**此基礎架構完成後，可依序考慮以下擴展：**

* **整合** `Celery` 與 `Redis` 實現非同步、平行化的大規模爬取。
* **將資料儲存從 JSON 檔案升級為正式的資料庫系統。**
* **建立一個簡單的 Web Dashboard 來監控爬蟲狀態與結果。**
* **整合 AI 轉述模組。**
* **加入 IP 代理池、隨機 User-Agent 等進階反反爬蟲策略。**
