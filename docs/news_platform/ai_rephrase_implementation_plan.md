# Project Structure Refactor & AI Rephrase Implementation Plan

## Overview

This document outlines a comprehensive project structure refactor to treat both scraping and AI rephrasing as equal core features, followed by the detailed implementation plan for the AI-powered article rephrasing functionality. The refactor ensures best practices for maintainability, scalability, and feature parity.

## 1. Current Project Structure Analysis

### 1.1 Current Issues

- **Feature Imbalance**: Structure suggests scraping is primary, rephrasing is secondary
- **Mixed Responsibilities**: Scripts and utilities are not clearly separated by domain
- **Inconsistent Organization**: Some utilities in root, others in subdirectories
- **Output Management**: No clear strategy for different types of outputs
- **Configuration Scattered**: Config files and settings not centrally managed

### 1.2 Current Structure

```
scrape-rephrase-news/
├── config.py                    # Basic configuration
├── main.py                      # Entry point
├── example_usage.py             # Usage examples
├── data/                        # Raw scraped data
├── docs/                        # Documentation
├── models/                      # Data models
├── scrapers/                    # Scraper implementations
├── scripts/                     # Utility scripts
├── utils/                       # General utilities
└── requirements.txt
```

## 2. Proposed Project Structure Refactor

### 2.1 New Structure Overview

```
scrape-rephrase-news/
├── README.md
├── requirements.txt
├── setup.py                     # Package setup
├── .env.example                 # Environment template
├── config/                      # Configuration management
│   ├── __init__.py
│   ├── settings.py              # Centralized settings
│   ├── scraping.yaml            # Scraping configurations
│   ├── rephrasing.yaml          # AI rephrasing configurations
│   └── logging.yaml             # Logging configurations
├── src/                         # Main source code
│   ├── __init__.py
│   ├── core/                    # Core shared functionality
│   │   ├── __init__.py
│   │   ├── models/              # Shared data models
│   │   ├── utils/               # Shared utilities
│   │   └── exceptions.py        # Custom exceptions
│   ├── scraping/                # Scraping domain
│   │   ├── __init__.py
│   │   ├── scrapers/            # Scraper implementations
│   │   ├── processors/          # Content processors
│   │   ├── validators/          # Data validators
│   │   └── utils/               # Scraping-specific utils
│   ├── rephrasing/              # Rephrasing domain
│   │   ├── __init__.py
│   │   ├── ai_clients/          # AI service clients
│   │   ├── processors/          # Content processors
│   │   ├── templates/           # Prompt templates
│   │   └── utils/               # Rephrasing-specific utils
│   └── publishing/              # Publishing domain (future)
│       ├── __init__.py
│       ├── api_clients/         # API clients
│       └── formatters/          # Output formatters
├── cli/                         # Command-line interfaces
│   ├── __init__.py
│   ├── scrape.py                # Scraping CLI
│   ├── rephrase.py              # Rephrasing CLI
│   └── pipeline.py              # Full pipeline CLI
├── data/                        # Data storage
│   ├── raw/                     # Raw scraped data
│   │   ├── macaodaily/          # Organized by source
│   │   └── shimindaily/
│   ├── processed/               # AI-processed content
│   │   ├── rephrased/           # Rephrased articles
│   │   └── formatted/           # API-ready content
│   └── exports/                 # Final exports
├── logs/                        # Application logs
│   ├── scraping/                # Scraping logs
│   ├── rephrasing/              # Rephrasing logs
│   └── pipeline/                # Pipeline logs
├── tests/                       # Test suite
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   └── fixtures/                # Test data
├── docs/                        # Documentation
│   ├── api/                     # API documentation
│   ├── guides/                  # User guides
│   └── development/             # Development docs
└── scripts/                     # Utility scripts
    ├── setup/                   # Setup scripts
    ├── maintenance/             # Maintenance scripts
    └── migration/               # Data migration scripts
```

### 2.2 Key Improvements

#### Domain-Driven Design

- **Scraping Domain**: Complete scraping functionality in `src/scraping/`
- **Rephrasing Domain**: Complete AI rephrasing functionality in `src/rephrasing/`
- **Publishing Domain**: Future publishing capabilities in `src/publishing/`
- **Core Domain**: Shared functionality in `src/core/`

#### Clear Separation of Concerns

- **CLI Layer**: User interfaces in `cli/`
- **Business Logic**: Domain logic in `src/`
- **Configuration**: Centralized in `config/`
- **Data Management**: Organized storage in `data/`

#### Scalability Considerations

- **Modular Architecture**: Easy to add new scrapers, AI providers, or publishers
- **Plugin System**: Extensible design for future enhancements
- **Configuration Management**: Environment-specific settings
- **Testing Infrastructure**: Comprehensive test coverage

## 3. Detailed Component Design

### 3.1 Core Models (`src/core/models/`)

#### Base Models

```python
# src/core/models/base.py
class BaseArticle(BaseModel):
    id: Optional[str] = None
    title: str
    content: str
    source_url: str
    publish_date: datetime
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

# src/core/models/scraping.py
class ScrapedArticle(BaseArticle):
    source_site_name: str
    source_site_url: str
    original_title: str
    author: Optional[str]
    content_html: str
    content_markdown: str
    content_text: str
    images: List[ArticleImage]
    tags: List[str]
    scrape_timestamp: datetime

# src/core/models/rephrasing.py
class RephrasedArticle(BaseArticle):
    original_article_id: str
    rephrased_title: str
    rephrased_content: str
    slug: str
    categories: List[str]
    meta_title: Optional[str]
    meta_description: Optional[str]
    ai_model_used: str
    processing_metadata: Dict[str, Any]
    quality_score: Optional[float]
```

### 3.2 Configuration Management (`config/`)

#### Centralized Settings

```python
# config/settings.py
class ScrapingSettings(BaseSettings):
    rate_limit_delay: float = 2.0
    max_retries: int = 3
    timeout: int = 30
    user_agent: str = "NewsBot/1.0"

class RephrasingSettings(BaseSettings):
    gemini_api_key: str
    model_name: str = "gemini-2.0-flash"
    max_tokens: int = 4000
    temperature: float = 0.3
    rate_limit_delay: float = 2.0

class DataSettings(BaseSettings):
    raw_data_path: Path = Path("data/raw")
    processed_data_path: Path = Path("data/processed")
    export_data_path: Path = Path("data/exports")
    log_path: Path = Path("logs")
```

### 3.3 Scraping Domain (`src/scraping/`)

#### Enhanced Scraper Architecture

```python
# src/scraping/scrapers/base.py
class BaseScraper(ABC):
    def __init__(self, config: ScrapingSettings):
        self.config = config
        self.session = self._create_session()

    @abstractmethod
    def scrape_articles(self, date: datetime) -> List[ScrapedArticle]:
        pass

    @abstractmethod
    def get_article_urls(self, date: datetime) -> List[str]:
        pass

# src/scraping/processors/content_processor.py
class ContentProcessor:
    def clean_html(self, html: str) -> str:
        pass

    def extract_text(self, html: str) -> str:
        pass

    def convert_to_markdown(self, html: str) -> str:
        pass
```

### 3.4 Rephrasing Domain (`src/rephrasing/`)

#### AI Client Architecture

```python
# src/rephrasing/ai_clients/base.py
class BaseAIClient(ABC):
    @abstractmethod
    def rephrase_article(self, article: ScrapedArticle) -> RephrasedArticle:
        pass

# src/rephrasing/ai_clients/gemini_client.py
class GeminiClient(BaseAIClient):
    def __init__(self, config: RephrasingSettings):
        self.config = config
        self.client = self._create_client()

    def rephrase_article(self, article: ScrapedArticle) -> RephrasedArticle:
        prompt = self._build_prompt(article)
        response = self._call_api(prompt)
        return self._parse_response(response, article)
```

## 4. Data Management Strategy

### 4.1 Data Organization

```
data/
├── raw/                         # Raw scraped data
│   ├── macaodaily/
│   │   ├── 2022/
│   │   │   ├── 20220701.json
│   │   │   ├── 20220702.json
│   │   │   └── 20220703.json
│   │   ├── 2023/
│   │   │   ├── 20230101.json
│   │   │   └── 20230102.json
│   │   └── 2024/
│   └── shimindaily/
│       └── [similar structure]
├── processed/                   # AI-processed content
│   ├── rephrased/
│   │   ├── macaodaily/
│   │   │   ├── 2022/
│   │   │   │   ├── 20220701.json
│   │   │   │   └── 20220702.json
│   │   │   └── 2023/
│   │   └── shimindaily/
│   └── formatted/               # API-ready content
│       ├── posts_api/
│       └── custom_formats/
└── exports/                     # Final exports
    ├── daily_batches/
    ├── monthly_archives/
    └── custom_exports/
```

### 4.2 Configuration Files

#### Environment Configuration (`.env`)

```bash
# Database
DATABASE_URL=sqlite:///data/articles.db

# AI Services
GEMINI_API_KEY=your_api_key_here
OPENAI_API_KEY=your_openai_key_here  # Future use

# Scraping
SCRAPING_RATE_LIMIT=2.0
SCRAPING_MAX_RETRIES=3

# Rephrasing
REPHRASING_RATE_LIMIT=2.0
REPHRASING_BATCH_SIZE=10

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Paths
DATA_ROOT=./data
LOG_ROOT=./logs
```

#### Scraping Configuration (`config/scraping.yaml`)

```yaml
scrapers:
  macaodaily:
    base_url: "http://www.macaodaily.com"
    rate_limit: 2.0
    timeout: 30
    max_retries: 3
    selectors:
      article_list: "#all_article_list"
      title: "strong[style*='font-size:14px']"
      content: "tbody#ozoom, founder-content"

  shimindaily:
    base_url: "https://shimindaily.net"
    rate_limit: 1.5
    timeout: 30
    max_retries: 3

content_processing:
  min_content_length: 100
  max_content_length: 50000
  clean_html: true
  extract_images: true
  generate_markdown: true
```

#### Rephrasing Configuration (`config/rephrasing.yaml`)

```yaml
ai_providers:
  gemini:
    model: "gemini-2.0-flash"
    base_url: "https://generativelanguage.googleapis.com/v1beta/openai/"
    max_tokens: 4000
    temperature: 0.3
    rate_limit: 2.0

  openai:  # Future use
    model: "gpt-4"
    max_tokens: 4000
    temperature: 0.3

processing:
  batch_size: 10
  min_content_length: 100
  max_retries: 3
  quality_threshold: 0.7

categories:
  auto_categorize: true
  mapping:
    政治: "politics"
    經濟: "economy"
    科技: "tech"
    社會: "society"
    健康: "health"
    教育: "education"
    澳聞: "macao-news"

output:
  formats:
    - "posts_api"
    - "markdown"
    - "json"
  include_metadata: true
  include_images: true
  generate_seo: true
```

## 5. Command Line Interface Design

### 5.1 Unified CLI Structure

```bash
# Main entry point
python -m src.cli --help

# Scraping commands
python -m src.cli scrape --help
python -m src.cli scrape macaodaily --date 2022-07-01
python -m src.cli scrape macaodaily --start-date 2022-07-01 --end-date 2022-07-31
python -m src.cli scrape shimindaily --date today

# Rephrasing commands
python -m src.cli rephrase --help
python -m src.cli rephrase --file data/raw/macaodaily/2022/07/macaodaily_20220701.json
python -m src.cli rephrase --source macaodaily --date 2022-07-01
python -m src.cli rephrase --source macaodaily --start-date 2022-07-01 --end-date 2022-07-31

# Pipeline commands (scrape + rephrase)
python -m src.cli pipeline --help
python -m src.cli pipeline --source macaodaily --date 2022-07-01
python -m src.cli pipeline --source macaodaily --start-date 2022-07-01 --end-date 2022-07-31

# Utility commands
python -m src.cli status                    # Show system status
python -m src.cli validate --file [path]   # Validate data files
python -m src.cli export --format posts_api --date 2022-07-01
```

### 5.2 Enhanced CLI Features

#### Global Options

- `--config`: Custom configuration file
- `--verbose`: Detailed logging output
- `--dry-run`: Preview operations without execution
- `--log-level`: Set logging level (DEBUG, INFO, WARNING, ERROR)
- `--output-dir`: Custom output directory

#### Scraping-Specific Options

- `--source`: News source (macaodaily, shimindaily)
- `--date`: Specific date to scrape
- `--start-date` / `--end-date`: Date range
- `--force`: Overwrite existing files
- `--validate`: Validate scraped content

#### Rephrasing-Specific Options

- `--file`: Specific JSON file to process
- `--source`: Process all files from a source
- `--ai-provider`: AI service to use (gemini, openai)
- `--output-format`: Output format (posts_api, markdown, json)
- `--batch-size`: Number of articles to process in batch
- `--quality-filter`: Minimum quality score threshold

## 6. Migration Strategy

### 6.1 Current to New Structure Migration

#### Phase 1: Prepare New Structure

```bash
# Create new directory structure
mkdir -p src/{core/{models,utils},scraping/{scrapers,processors,validators,utils}}
mkdir -p src/{rephrasing/{ai_clients,processors,templates,utils},publishing/{api_clients,formatters}}
mkdir -p cli config data/{raw,processed,exports} logs/{scraping,rephrasing,pipeline}
mkdir -p tests/{unit,integration,fixtures} scripts/{setup,maintenance,migration}
```

#### Phase 2: Migrate Existing Code

```bash
# Move and refactor existing components
mv models/* src/core/models/
mv scrapers/* src/scraping/scrapers/
mv utils/* src/core/utils/
mv scripts/* cli/  # Convert to proper CLI commands
```

#### Phase 3: Reorganize Data

```bash
# Migrate existing data files
mkdir -p data/raw/macaodaily/2022/07
mv data/macaodaily_*.json data/raw/macaodaily/2022/07/
```

#### Phase 4: Update Imports and References

- Update all import statements to use new structure
- Modify configuration loading
- Update CLI entry points
- Fix test imports and paths

### 6.2 Backward Compatibility

- Maintain legacy CLI commands during transition
- Provide migration scripts for data conversion
- Keep old structure accessible via symlinks initially
- Gradual deprecation of old interfaces

### 6.3 Data Migration Scripts

```python
# scripts/migration/migrate_data_structure.py
def migrate_scraped_data():
    """Migrate existing scraped data to new structure"""
    old_pattern = "data/macaodaily_*.json"
    for file_path in glob.glob(old_pattern):
        date_match = re.search(r'(\d{4})(\d{2})(\d{2})', file_path)
        if date_match:
            year, month, day = date_match.groups()
            new_path = f"data/raw/macaodaily/{year}/{month}/"
            os.makedirs(new_path, exist_ok=True)
            shutil.move(file_path, f"{new_path}/macaodaily_{year}{month}{day}.json")

def update_file_references():
    """Update internal file references to new structure"""
    # Update configuration files
    # Update documentation
    # Update example scripts
```

## 7. Implementation Phases

### Phase 1: Project Structure Refactor (Week 1)

- [X] Create new directory structure
- [X] Set up configuration management system
- [X] Migrate existing code to new structure
- [X] Update imports and references
- [X] Create migration scripts for data
- [X] Set up new CLI structure
- [X] Update documentation

### Phase 2: Enhanced Scraping Infrastructure (Week 2)

- [X] Refactor scrapers to use new architecture
- [X] Implement enhanced content processors
- [X] Add comprehensive validation
- [X] Improve error handling and logging
- [X] Create scraping CLI commands
- [X] Add progress tracking and recovery

### Phase 3: AI Rephrasing Implementation (Week 3) ✅ COMPLETED

- [x] Implement Gemini API client with OpenAI library and structured outputs
- [x] Create intelligent article filtering system with priority scoring
- [x] Build quota tracking and progress persistence with resumable processing
- [x] Create Pydantic models for structured responses
- [x] Build rephrasing pipeline with comprehensive error recovery
- [x] Add batch processing with smart retry logic for network issues
- [x] Create rephrasing CLI commands with continuous processing
- [x] Implement rate limiting with automatic retry delay extraction
- [x] Add quota exhaustion detection with graceful stopping
- [x] Build pre-rephrase filtering system for token optimization
- [x] Implement incremental saving to prevent data loss
- [x] Add comprehensive logging with full error details

### Phase 4: Integration and Pipeline (Week 4)

- [ ] Create unified pipeline commands
- [ ] Implement data export functionality
- [ ] Add comprehensive testing
- [ ] Performance optimization
- [ ] Documentation and examples
- [ ] Deployment preparation

### Phase 5: Advanced Features (Week 5)

- [ ] Multiple AI provider support
- [ ] Advanced categorization
- [ ] Quality scoring system
- [ ] Publishing API integration
- [ ] Analytics and monitoring
- [ ] User interface improvements

## 8. Benefits of New Structure

### 8.1 For Developers

- **Clear Separation**: Each domain has its own space
- **Easier Testing**: Modular components are easier to test
- **Better Maintainability**: Changes in one domain don't affect others
- **Scalability**: Easy to add new features or providers

### 8.2 For Users

- **Consistent Interface**: Unified CLI for all operations
- **Better Configuration**: Centralized and environment-specific settings
- **Improved Reliability**: Better error handling and recovery
- **Enhanced Functionality**: More powerful processing capabilities

### 8.3 For Operations

- **Better Logging**: Organized logs by domain and operation
- **Easier Monitoring**: Clear metrics and status reporting
- **Simplified Deployment**: Package-based structure
- **Data Management**: Organized data storage and archiving

## 9. Gemini API Rate Limiting & Quota Management ✅ IMPLEMENTED

### 9.1 Gemini 2.5 Flash Free Tier Limitations

#### Rate Limits
- **Requests Per Minute (RPM)**: 10 requests/minute
- **Tokens Per Minute (TPM)**: 250,000 tokens/minute
- **Requests Per Day (RPD)**: 250 requests/day
- **Model**: gemini-2.5-flash (free tier)

#### Critical Considerations ✅ ALL IMPLEMENTED
1. **Daily Quota Management**: ✅ Intelligent article filtering with priority scoring (0.6+ threshold)
2. **Rate Limiting**: ✅ Automatic retry delay extraction from API responses (no fixed delays)
3. **Token Management**: ✅ Pre-rephrase filtering to optimize token usage
4. **Error Handling**: ✅ Graceful quota exhaustion detection with immediate stopping
5. **Resume Capability**: ✅ Progress tracking with resumable processing from last successful batch
6. **Network Resilience**: ✅ Smart retry logic for timeout/connection errors (2 retries with exponential backoff)
7. **Quota Preservation**: ✅ No automatic retries on quota exhaustion to prevent waste

### 9.2 Rate Limiting Strategy ✅ IMPLEMENTED

#### Smart Request Scheduling ✅
```python
# Dynamic delay extraction from API responses (no fixed delays)
retry_match = re.search(r"retrydelay.*?(\d+)s", error_msg)
retry_delay = int(retry_match.group(1)) if retry_match else 60

# Actual implementation respects API suggestions
GEMINI_MAX_REQUESTS_PER_DAY = 250
GEMINI_MAX_REQUESTS_PER_MINUTE = 10

# Intelligent batch processing
DEFAULT_BATCH_SIZE = 8  # Optimized for quota efficiency
MAX_RETRIES = 2         # For network/timeout errors only
```

#### Quota Tracking ✅ IMPLEMENTED
- **Daily Counter**: ✅ Persistent quota tracking with file-based storage
- **Request Tracking**: ✅ Real-time quota monitoring with status commands
- **Token Estimation**: ✅ Pre-processing token estimation for batch optimization
- **Progress Persistence**: ✅ Job state saved after each successful batch

#### Error Recovery ✅ IMPLEMENTED
- **429 Rate Limit**: ✅ Dynamic retry delay extraction from API responses
- **403 Quota Exceeded**: ✅ Immediate stop with quota_exhausted status
- **Network Errors**: ✅ Smart retry with exponential backoff (2, 4, 8 seconds)
- **Resume Mechanism**: ✅ Automatic resume from last successful batch with incomplete detection

### 9.3 Article Filtering Strategy

#### Priority-Based Selection
Given the 250 requests/day limit, we need intelligent filtering:

1. **High Priority Tags**:
   - 政治 (Politics)
   - 經濟 (Economy)
   - 科技 (Technology)
   - 社會 (Society)
   - 澳聞 (Macao News)

2. **Content Quality Filters**:
   - Minimum content length: 200 characters
   - Maximum content length: 10,000 characters
   - Exclude advertisements and announcements
   - Prioritize articles with images

3. **Daily Allocation**:
   - Reserve 200 requests for high-priority articles
   - Reserve 50 requests for medium-priority articles
   - Skip low-priority content when quota is limited

#### Smart Batching
```python
# Process articles in priority order
# Stop when daily quota is reached
# Save remaining articles for next day
DAILY_PROCESSING_STRATEGY = {
    "high_priority": 150,    # Reserve 150 requests for high priority
    "medium_priority": 75,   # Reserve 75 requests for medium priority
    "low_priority": 25,      # Reserve 25 requests for low priority
}
```

### 9.4 Implementation Requirements

#### Rate Limiter Component
```python
class GeminiRateLimiter:
    def __init__(self):
        self.daily_count = 0
        self.minute_count = 0
        self.last_request_time = None
        self.quota_reset_time = None

    async def acquire_permit(self) -> bool:
        """Check if request can be made, handle rate limiting"""

    def handle_rate_limit_error(self, error):
        """Handle 429 rate limit errors with backoff"""

    def handle_quota_exceeded(self, error):
        """Handle 403 quota exceeded, schedule next day retry"""
```

#### Progress Tracking
```python
class RephrasingProgress:
    def __init__(self):
        self.processed_articles = set()
        self.failed_articles = []
        self.quota_exhausted_time = None
        self.daily_quota_used = 0

    def save_state(self):
        """Persist progress to disk"""

    def load_state(self):
        """Load progress from disk"""

    def can_process_more(self) -> bool:
        """Check if more articles can be processed today"""
```

## 10. OpenAI Library Integration

### 10.1 Implementation Approach

Instead of using raw HTTP requests with aiohttp, the implementation uses the OpenAI library with Gemini's OpenAI-compatible endpoint. This provides several advantages:

#### Benefits of OpenAI Library
- **Structured Outputs**: Native support for Pydantic models via `response_format`
- **Better Error Handling**: Built-in retry logic and error classification
- **Type Safety**: Full type hints and validation
- **Cleaner Code**: Less boilerplate compared to raw HTTP requests
- **Future Compatibility**: Easy to switch between AI providers

#### Configuration
```python
from openai import AsyncOpenAI

client = AsyncOpenAI(
    api_key="GEMINI_API_KEY",
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
)

# Structured request with Pydantic model
completion = await client.beta.chat.completions.parse(
    model="gemini-2.0-flash-exp",
    messages=[
        {"role": "system", "content": "You are a professional news editor."},
        {"role": "user", "content": "Rephrase this article..."},
    ],
    response_format=RephrasedContent,  # Pydantic model
)

result = completion.choices[0].message.parsed  # Typed object
```

#### Structured Response Models
```python
class RephrasedContent(BaseModel):
    title: str = Field(description="Rephrased article title")
    summary: str = Field(description="2-3 sentence summary")
    content: str = Field(description="Full rephrased content")
    keywords: List[str] = Field(description="3-5 relevant keywords")
    categories: List[str] = Field(description="Suggested categories")
```

### 10.2 Multiple AI Functions

The system now supports multiple AI-powered functions:

1. **Article Rephrasing** (`RephrasedContent`)
   - Complete article rewriting with improved readability
   - Maintains factual accuracy while enhancing flow
   - Generates titles, summaries, keywords, and categories

2. **Article Summarization** (`ArticleSummary`)
   - Concise summaries with key points extraction
   - Optimized for quick content overview

3. **Content Classification** (`ArticleClassification`)
   - Intelligent categorization with confidence scores
   - Reasoning explanation for classification decisions

4. **Title Optimization** (`TitleOptimization`)
   - Enhanced titles with alternative options
   - Improvement notes and engagement optimization

5. **Quality Assessment** (`QualityAssessment`)
   - Multi-dimensional quality scoring (1-10 scale)
   - Detailed feedback for content improvement

## 11. Technology Stack Updates

### 11.1 Core Dependencies

```python
# requirements.txt updates
click>=8.0.0              # Enhanced CLI framework
pydantic>=2.0.0           # Data validation and settings
pyyaml>=6.0               # Configuration management
rich>=13.0.0              # Enhanced console output
typer>=0.9.0              # Modern CLI framework (alternative to click)
sqlalchemy>=2.0.0         # Database ORM (future use)
alembic>=1.12.0           # Database migrations (future use)

# AI and Processing
openai>=1.0.0             # Gemini API access
beautifulsoup4>=4.12.0    # HTML parsing
markdownify>=0.11.0       # HTML to Markdown conversion
python-slugify>=8.0.0     # URL-friendly slug generation

# Development and Testing
pytest>=7.0.0             # Testing framework
pytest-asyncio>=0.21.0    # Async testing
black>=23.0.0             # Code formatting
isort>=5.12.0             # Import sorting
mypy>=1.5.0               # Type checking
```

### 9.2 Development Tools

- **Code Quality**: Black, isort, mypy for consistent code style
- **Testing**: Pytest with async support and fixtures
- **Documentation**: Sphinx for API documentation
- **Packaging**: setuptools for proper package distribution

## 10. Success Metrics

### 10.1 Technical Metrics

- **Code Coverage**: >90% test coverage
- **Performance**: <2s average processing time per article
- **Reliability**: <1% error rate in production
- **Maintainability**: Clear separation of concerns, documented APIs

### 10.2 User Experience Metrics

- **Ease of Use**: Single command for common operations
- **Flexibility**: Support for various input/output formats
- **Transparency**: Clear progress reporting and error messages
- **Documentation**: Comprehensive guides and examples

### 10.3 Business Metrics

- **Processing Volume**: Handle 1000+ articles per day
- **Quality**: >95% user satisfaction with rephrased content
- **Efficiency**: 80% reduction in manual content processing time
- **Scalability**: Support for multiple news sources and AI providers

## 11. Next Steps

### 11.1 Immediate Actions

1. **Review and Approve**: Get stakeholder approval for structure refactor
2. **Create Migration Plan**: Detailed step-by-step migration process
3. **Set Up Development Environment**: New structure and dependencies
4. **Begin Phase 1**: Start with project structure refactor

### 11.2 Decision Points

- **CLI Framework**: Choose between Click and Typer
- **Database**: Decide if SQLite/PostgreSQL needed for metadata
- **AI Providers**: Priority order for multiple provider support
- **Publishing Integration**: Timeline for Posts API integration

## 12. Comprehensive CLI Usage Guide ✅ IMPLEMENTED

### 12.1 AI Rephrasing Commands

#### Basic Rephrasing
```bash
# Process single date
python -m cli rephrase process --source macaodaily --date 2025-07-01

# Process date range with continuous processing
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01

# Process with custom batch size
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --batch-size 8

# Process with priority filtering
python -m cli rephrase process --source macaodaily --date 2025-07-01 --priority-threshold 0.7
```

#### Quota Management
```bash
# Check current quota status
python -m cli rephrase quota

# Test API connection
python -m cli rephrase test

# View quota usage history
python -m cli rephrase quota --detailed
```

#### Advanced Options
```bash
# Skip existing files
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --skip-existing

# Create pre-rephrase files for inspection
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --create-pre-rephrase

# Custom priority threshold
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --priority-threshold 0.8

# Debug mode with verbose logging
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01 --log-level DEBUG
```

### 12.2 System Status Commands

#### File and Data Statistics
```bash
# Show comprehensive statistics
python -m cli status stats

# Show statistics for specific time period
python -m cli status stats --days 30

# Show recent files
python -m cli status files --limit 10

# Check system health
python -m cli status health

# Show configuration
python -m cli status config
```

### 12.3 Error Handling and Recovery

#### Quota Exhaustion
When daily quota is exhausted, the system:
1. **Immediately stops processing** to preserve remaining quota
2. **Saves all progress** up to the point of exhaustion
3. **Shows clear status**: `quota_exhausted` with timestamp
4. **Provides guidance**: "Resume tomorrow or upgrade plan"
5. **Enables resumption**: Next run continues from where it stopped

#### Network Issues
For timeout/connection errors, the system:
1. **Retries automatically** up to 2 times with exponential backoff
2. **Waits intelligently** using API-suggested delays (2s, 4s, 8s)
3. **Logs detailed errors** for debugging
4. **Continues processing** other batches after max retries

#### Resume Processing
```bash
# System automatically detects incomplete processing
python -m cli rephrase continuous --start-date 2025-06-01 --end-date 2025-07-01

# Output shows:
# "📊 Incomplete processing found for 2025-06-30: 45/90 articles processed (50.0%)"
# "📊 Resume processing: 45 unprocessed articles out of 90 total"
```

This comprehensive refactor plan transforms the project into a professional, scalable system that treats both scraping and rephrasing as equal first-class features while maintaining backward compatibility and providing a clear migration path.
