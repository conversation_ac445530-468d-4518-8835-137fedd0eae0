# Quick Start: Continuous Rephrase Processing

This guide will get you started with the new continuous rephrase system for 24/7 processing of all your news data.

## 🚀 Quick Setup

### 1. Set API Key
```bash
export GEMINI_API_KEY="your_gemini_api_key_here"
```

### 2. Test Connection
```bash
python -m cli rephrase test
```

### 3. Check Quota
```bash
python -m cli rephrase quota
```

## 📊 Monitor Current Status

```bash
# Quick status check
python scripts/monitor_rephrase.py

# Continuous monitoring (refreshes every 30 seconds)
python scripts/monitor_rephrase.py --watch
```

## 🎯 Start Processing

### Option 1: CLI Command (Simple)
```bash
# Process entire year 2024
python -m cli rephrase continuous --year 2024

# Process today only
python -m cli rephrase continuous --today

# Process specific single day
python -m cli rephrase continuous --start-date 2024-07-01 --end-date 2024-07-01

# Process specific date range
python -m cli rephrase continuous --start-date 2024-01-01 --end-date 2024-12-31

# Dry run to see what would be processed
python -m cli rephrase continuous --year 2024 --dry-run
```

### Option 2: Monitoring Script (Recommended for 24/7)
```bash
# Process entire year with auto-restart and monitoring
python scripts/continuous_rephrase.py --year 2024

# Process from specific date to today
python scripts/continuous_rephrase.py --start-date 2024-06-01

# Custom settings
python scripts/continuous_rephrase.py --year 2024 \
  --batch-size 10 \
  --priority-threshold 0.7
```

## 🔧 Key Settings

| Setting | Default | Description |
|---------|---------|-------------|
| `--batch-size` | 15 | Articles per API request (1-15) |
| `--priority-threshold` | 0.6 | Minimum priority score (0.0-1.0, 0.6=high priority only) |
| `--log-level` | INFO | Logging level (DEBUG/INFO/WARNING/ERROR) |

**Note:** The `--max-articles` limitation has been removed. All articles meeting the priority threshold will be processed to ensure no important news is missed.

## 📈 Understanding Priority Scores

Articles are scored based on tags and content quality:

**High Priority (0.6 base score):**
- A01-A08: 澳聞, 要聞, 政治, 經濟, 科技, 社會, 健康, 教育

**Medium Priority (0.4 base score):**
- A09-A12: 文化, 體育, 娛樂, 旅遊

**Bonus Points (+0.1 each):**
- Content length > 1000 characters
- Has images
- Has author
- Title length > 20 characters

**Example:** An article with tag "A01" (澳聞) + images + long content = 0.6 + 0.1 + 0.1 = 0.8 score

## 📁 Output Structure

```
data/
├── processed/
│   ├── pre-rephrase/          # Filtered articles (for review)
│   │   └── macaodaily/2024/20240701.json
│   └── rephrased/             # Final AI-rephrased articles
│       └── macaodaily/2024/20240701.json
└── logs/
    ├── rephrase_20250705.log  # Daily processing log
    └── continuous_rephrase_20250705.log  # Monitoring log
```

## 🔍 Monitoring Progress

### Real-time Log Monitoring
```bash
# Follow today's processing log
tail -f logs/rephrase_$(date +%Y%m%d).log

# Follow error log
tail -f logs/rephrase_error.log

# Follow monitoring script log
tail -f logs/continuous_rephrase_$(date +%Y%m%d).log
```

### Status Dashboard
```bash
# Show comprehensive status
python scripts/monitor_rephrase.py

# Continuous monitoring with custom interval
python scripts/monitor_rephrase.py --watch --interval 60
```

## ⏱️ Processing Time Estimates

With Gemini free tier limits (250 requests/day, 15 articles/batch):

- **Daily Capacity**: ~3,750 articles
- **Your Dataset**: ~68,620 articles/year (188 articles/day × 365 days)
- **Estimated Time**: ~18-20 days for full year

## 🛠️ For 24/7 Operation

### Using Screen/Tmux (Recommended)
```bash
# Start in screen session
screen -S rephrase
python scripts/continuous_rephrase.py --year 2024

# Detach: Ctrl+A, then D
# Reattach later: screen -r rephrase
```

### Using Tmux
```bash
# Start tmux session
tmux new-session -d -s rephrase
tmux send-keys -t rephrase "python scripts/continuous_rephrase.py --year 2024" Enter

# Attach later: tmux attach -t rephrase
```

## 🚨 Common Issues & Solutions

### API Key Not Set
```
❌ Gemini API key not configured
```
**Solution:** `export GEMINI_API_KEY="your_key"`

### Quota Exceeded
```
⚠️ Daily quota exhausted (250/250)
```
**Solution:** Wait for quota reset (automatic), or process will pause and resume

### No Files Found
```
❌ Input file not found
```
**Solution:** Check if raw data exists in `data/raw/macaodaily/YYYY/`

### Rate Limited
```
⚠️ Rate limiting: waiting 6.5 seconds
```
**Solution:** Normal behavior, system automatically handles rate limits

## 📋 Quick Commands Reference

```bash
# Status and monitoring
python scripts/monitor_rephrase.py                    # Current status
python scripts/monitor_rephrase.py --watch           # Live monitoring
python -m cli rephrase quota                         # API quota status
python -m cli rephrase test                          # Test API connection

# Processing
python -m cli rephrase continuous --year 2024 --dry-run    # Preview
python scripts/continuous_rephrase.py --year 2024          # Start processing

# Logs
tail -f logs/rephrase_$(date +%Y%m%d).log           # Follow processing
tail -f logs/rephrase_error.log                     # Follow errors
ls -la logs/                                        # List all logs
```

## 🎯 Next Steps

1. **Start Small**: Test with a small date range first
2. **Monitor Closely**: Watch logs for the first few hours
3. **Adjust Settings**: Tune priority threshold based on results
4. **Scale Up**: Once comfortable, process larger date ranges
5. **Review Output**: Check pre-rephrase and rephrased files quality

The system is designed to run continuously and handle all edge cases automatically. Just set it up and let it process your entire dataset!
