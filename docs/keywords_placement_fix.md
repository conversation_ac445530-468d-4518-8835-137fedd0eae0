# Keywords Placement Fix

## 📋 Overview

Fixed the incorrect placement of keywords in API requests. Keywords were previously nested inside the `extra` object but should be at the same level as `categories`, `meta`, etc.

## 🎯 Problem Identified

**Incorrect Structure (Before)**:
```json
{
  "posts": [{
    "title": "澳門歷史城區保護報告獲世遺會通過，肯定保育成果",
    "categories": ["社會民生", "澳門新聞"],
    "meta": { "title": "...", "description": "..." },
    "extra": {
      "keywords": ["澳門", "世界遺產", "歷史城區"],  // ❌ Wrong location
      "processing_metadata": {...},
      "tags": [...]
    }
  }]
}
```

**Correct Structure (After)**:
```json
{
  "posts": [{
    "title": "澳門歷史城區保護報告獲世遺會通過，肯定保育成果",
    "categories": ["社會民生", "澳門新聞"],
    "keywords": ["澳門", "世界遺產", "歷史城區"],  // ✅ Correct location
    "meta": { "title": "...", "description": "..." },
    "extra": {
      "processing_metadata": {...},
      "tags": [...]
      // keywords no longer here
    }
  }]
}
```

## 🔧 Changes Made

### 1. **Updated API Schema**

**File**: `src/api/models/api_schemas.py`

Added `keywords` as a top-level field in `PostSchema`:

```python
class PostSchema(BaseModel):
    """Schema for individual post in API requests."""
    title: str
    slug: str
    content: str  # Markdown content
    summary: Optional[str] = None
    source_url: Optional[HttpUrl] = None
    source_site_name: Optional[str] = None
    extra: Optional[Dict[str, Any]] = None  # For arbitrary JSON metadata
    categories: Optional[List[str]] = None
    keywords: Optional[List[str]] = None  # ✅ Added at top level
    meta: Optional[MetaSchema] = None
    publishedAt: Optional[str] = None
    status: Literal["draft", "published"] = Field(default="draft", alias="_status")
    featuredImage: Optional[FeaturedImageSchema] = None
```

### 2. **Updated Data Transformer**

**File**: `src/api/services/data_transformer.py`

#### A. Added Keywords to Main Post Data
```python
# Keywords mapping
if hasattr(article, 'keywords') and article.keywords:
    post_data["keywords"] = article.keywords
```

#### B. Removed Keywords from Extra Object
**Before**:
```python
# Add keywords
if hasattr(article, 'keywords') and article.keywords:
    extra["keywords"] = article.keywords  # ❌ Wrong location
```

**After**:
```python
# Keywords are now handled at top level, removed from extra
```

## ✅ Verification & Testing

### **Test Results**: `scripts/test_keywords_placement.py`

- ✅ **Keywords found at top level**: Correctly placed alongside categories, meta, etc.
- ✅ **Keywords NOT in extra object**: Removed from extra to avoid duplication
- ✅ **JSON serialization preserves structure**: Keywords maintain correct placement
- ✅ **Real file data transforms correctly**: Existing rephrased files work properly

### **Sample Output**: `scripts/show_corrected_api_structure.py`

```json
{
  "posts": [{
    "title": "澳門歷史城區保護報告獲世遺會通過，肯定保育成果",
    "slug": "macau-historic-centre-unesco-report-approved",
    "content": "# 澳門歷史城區保護報告獲世遺會通過...",
    "summary": "澳門歷史城區的保護狀況報告在第四十七屆世界遺產委員會會議上獲決議通過...",
    "source_url": "http://www.macaodaily.com/html/2025-07/12/content_1844355.htm",
    "source_site_name": "澳門日報",
    "categories": ["社會民生", "澳門新聞"],
    "keywords": ["澳門", "世界遺產", "歷史城區", "文化遺產", "聯合國教科文組織", "西夏陵", "文物保護"],
    "meta": {
      "title": "澳門歷史城區保護報告獲世遺會通過，肯定保育成果",
      "description": "澳門歷史城區的保護狀況報告在第四十七屆世界遺產委員會會議上獲決議通過..."
    },
    "extra": {
      "processing_metadata": {...},
      "original_article_id": "original-demo-123",
      "ai_model_used": "gemini-2.5-flash",
      "tags": ["A02", "澳聞"],
      "priority_score": 0.8,
      "quality_score": 0.0
    },
    "publishedAt": null,
    "status": "draft",
    "featuredImage": null
  }]
}
```

## 📊 Field Organization

### **Top-Level Fields** (Same level as categories, meta)
- ✅ `title` - Article title
- ✅ `slug` - SEO-friendly URL slug
- ✅ `content` - Markdown content
- ✅ `summary` - Article summary
- ✅ `categories` - Content categories
- ✅ **`keywords`** - SEO keywords (moved here)
- ✅ `meta` - SEO meta title and description
- ✅ `source_url` - Original article URL
- ✅ `source_site_name` - Source website name
- ✅ `publishedAt` - Publication date
- ✅ `status` - Draft or published status
- ✅ `featuredImage` - Featured image data

### **Extra Object** (Processing metadata)
- ✅ `processing_metadata` - AI processing details
- ✅ `original_article_id` - Original article reference
- ✅ `ai_model_used` - AI model information
- ✅ `tags` - Original article tags
- ✅ `priority_score` - Content priority score
- ✅ `quality_score` - Content quality score
- ❌ ~~`keywords`~~ - Moved to top level

## 🚀 Benefits

1. **🎯 Correct API Structure**: Keywords now at the same level as other primary content fields
2. **📊 Better Organization**: Clear separation between content fields and processing metadata
3. **🔍 Improved SEO**: Keywords properly positioned for SEO and content tagging
4. **🛡️ Consistency**: All content-related fields (title, categories, keywords, meta) at the same level
5. **📤 API Compliance**: Matches expected API schema structure

## 🔍 Verification Commands

To verify the fix is working correctly:

```bash
# Test keywords placement
python scripts/test_keywords_placement.py

# Show corrected API structure
python scripts/show_corrected_api_structure.py

# Test with real data
python scripts/test_meta_data.py
```

## 📝 Impact

- **✅ All future API uploads** will have keywords at the correct level
- **✅ Existing rephrased data** transforms correctly
- **✅ No data loss** - all keywords preserved during transformation
- **✅ Backward compatibility** maintained for existing workflows

---

*Last updated: 2025-07-18*
*Status: ✅ Complete - Keywords correctly placed at top level*
