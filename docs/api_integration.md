# API Integration Guide

This guide explains how to configure and use the API integration feature to upload rephrased articles to your server.

## Overview

The API integration allows you to automatically upload rephrased news articles to your server via REST API. The system supports:

- Batch uploading of articles
- Image handling (external URLs or base64 encoding)
- Upload status tracking and resumable uploads
- Rate limiting and retry logic
- Comprehensive error handling

## Configuration

### Environment Variables

Create a `.env` file in your project root with the following variables:

```bash
# News Platform API
NEWS_API_URL=https://your-server.com/api/posts
NEWS_API_AUTH_TOKEN=your_bearer_token_here
```

### Configuration File

Update `config/app.yaml` to customize API settings:

```yaml
api:
  # Server configuration
  api_url: ""  # Set via environment variable NEWS_API_URL

  # Authentication
  auth_token: ""  # Set via environment variable NEWS_API_AUTH_TOKEN

  # Request configuration
  timeout: 60
  max_retries: 3
  retry_delay: 1.0
  retry_backoff: 2.0
  batch_size: 10

  # Rate limiting
  rate_limit_delay: 1.0
  max_requests_per_minute: 60

  # Upload settings
  default_status: "draft"  # draft, published
  include_images: true
  image_format: "base64"  # external, base64
  max_image_size_mb: 5
```

## API Schema

Your server should accept the following JSON schema:

```typescript
interface PostSchema {
  title: string;
  slug: string;
  content: string; // Markdown content
  summary?: string;
  source_url?: string;
  source_site_name?: string;
  extra?: Record<string, any>; // Arbitrary JSON metadata
  categories?: string[];
  meta?: {
    title?: string;
    description?: string;
  };
  publishedAt?: string; // ISO datetime
  _status?: "draft" | "published";
  featuredImage?: {
    type: "base64" | "external";
    data: string; // Base64 string or external URL
    filename?: string;
    alt?: string;
  };
}

interface RequestSchema {
  posts: PostSchema[];
}
```

## Usage

### CLI Commands

#### Test API Connection

```bash
python -m cli upload test
```

#### Upload Single Date

```bash
# Upload today's articles
python -m cli upload single

# Upload specific date
python -m cli upload single --date 2024-01-15

# Upload limited number of articles (for testing)
python -m cli upload single --date 2024-01-15 --limit 5

# Upload specific file
python -m cli upload single --file data/processed/rephrased/macaodaily/2024/20240115.json

# Dry run (preview without uploading)
python -m cli upload single --date 2024-01-15 --limit 3 --dry-run
```

#### Upload Date Range

```bash
# Upload articles for a date range
python -m cli upload range --start-date 2024-01-01 --end-date 2024-01-31

# Dry run
python -m cli upload range --start-date 2024-01-01 --end-date 2024-01-31 --dry-run
```

#### Check Upload Status

```bash
# Show upload statistics
python -m cli upload status

# Reset failed uploads to pending
python -m cli upload reset-failed
```

### Programmatic Usage

```python
import asyncio
from src.api.services.upload_service import ApiUploadService
from pathlib import Path

async def upload_articles():
    service = ApiUploadService()
  
    # Upload single file
    file_path = Path("data/processed/rephrased/macaodaily/2024/20240115.json")
    result = await service.upload_rephrased_file(file_path)
  
    print(f"Uploaded: {result.successful_uploads}")
    print(f"Failed: {result.failed_uploads}")
    print(f"Skipped: {result.skipped_uploads}")

# Run the upload
asyncio.run(upload_articles())
```

## Data Transformation

The system automatically transforms rephrased articles to match your API schema:

| Rephrased Article Field                         | API Field            | Notes                     |
| ----------------------------------------------- | -------------------- | ------------------------- |
| `title`                                       | `title`            | Direct mapping            |
| `slug`                                        | `slug`             | Direct mapping            |
| `content`                                     | `content`          | Markdown format           |
| `summary`                                     | `summary`          | Direct mapping            |
| `source_url`                                  | `source_url`       | Direct mapping            |
| `source_site_name`                            | `source_site_name` | Direct mapping            |
| `categories`                                  | `categories`       | Array mapping             |
| `meta_title`                                  | `meta.title`       | Nested object             |
| `meta_description`                            | `meta.description` | Nested object             |
| `processing_timestamp`                        | `publishedAt`      | ISO datetime              |
| `status`                                      | `_status`          | Mapped to draft/published |
| `images[0]`                                   | `featuredImage`    | First image as featured   |
| `processing_metadata`, `tags`, `keywords` | `extra`            | JSON metadata             |

## Image Handling

### Base64 Encoding (Default)

Set `image_format: "base64"` in configuration. Images are downloaded and encoded:

```json
{
  "featuredImage": {
    "type": "base64",
    "data": "data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "filename": "article-slug.jpg",
    "alt": "Image description"
  }
}
```

### External URLs (Alternative)

Set `image_format: "external"` in configuration. Images are referenced by URL:

```json
{
  "featuredImage": {
    "type": "external",
    "data": "https://example.com/image.jpg",
    "alt": "Image description"
  }
}
```

**Note:** Base64 encoding provides better reliability but increases payload size. Use external URLs for better performance when your server can reliably access the image URLs.

### Image Filename Logic

For base64 images, the system automatically generates meaningful filenames:

- **Uses article slug**: `article-slug.jpg` (makes images easy to identify)
- **Preserves format**: Extension matches actual image format (`.jpg`, `.png`, `.gif`, etc.)
- **Handles optimization**: Optimized images are saved as `.jpg` with slug preserved

**Examples:**

- Article slug: `breaking-news-story` → Filename: `breaking-news-story.jpg`
- Article slug: `tech-update-2025` → Filename: `tech-update-2025.png` (if original is PNG)

## Upload Status Tracking

The system tracks upload status to prevent duplicates and enable resumable uploads:

- **Pending**: Article not yet uploaded
- **Uploaded**: Successfully uploaded to server
- **Failed**: Upload failed (will be retried)
- **Skipped**: Article skipped (already uploaded)

Status is stored in `data/processed/upload_status.json`.

## Error Handling

### Common Issues

1. **Authentication Failed**

   - Check `NEWS_API_AUTH_TOKEN` environment variable
   - Verify authentication type in configuration
2. **Connection Timeout**

   - Increase `timeout` setting in configuration
   - Check network connectivity to API server
3. **Rate Limit Exceeded**

   - Adjust `rate_limit_delay` and `max_requests_per_minute`
   - Reduce `batch_size` for smaller requests
4. **Image Processing Failed**

   - Check image URLs are accessible
   - Verify image format is supported (jpg, png, gif, webp)
   - Reduce `max_image_size_mb` if images are too large

### Retry Logic

The system automatically retries failed requests with exponential backoff:

- Initial delay: `retry_delay` seconds
- Backoff multiplier: `retry_backoff`
- Maximum retries: `max_retries`

### Logging

Enable verbose logging for debugging:

```bash
python -m cli upload single --verbose
```

Logs are written to console and optionally to files in the `logs/` directory.

## Best Practices

1. **Start Small**: Test with a single date before uploading large ranges
2. **Use Dry Run**: Always test with `--dry-run` first
3. **Monitor Status**: Check upload status regularly with `upload status`
4. **Handle Failures**: Use `reset-failed` to retry failed uploads
5. **Rate Limiting**: Respect your server's rate limits
6. **Image Optimization**: Use external URLs for better performance
7. **Backup Data**: Keep backups of rephrased data before uploading

## Troubleshooting

### Debug Mode

Enable debug logging for detailed information:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Test Connection

Always test your API connection first:

```bash
python -m cli upload test
```

### Check Configuration

Verify your configuration is loaded correctly:

```python
from config.settings import get_settings
settings = get_settings()
print(f"API URL: {settings.api.full_url}")
print(f"Auth Type: {settings.api.auth_type}")
print(f"Batch Size: {settings.api.batch_size}")
```

### Manual Testing

Test your API endpoint manually with curl:

```bash
curl -X POST "https://your-server.com/api/posts" \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"posts": [{"title": "Test", "slug": "test", "content": "Test content"}]}'
```
